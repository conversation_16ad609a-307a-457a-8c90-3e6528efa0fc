// TikTok Live Connector - Integrare reală cu TikTok Live
import { TikTokLiveConnector } from 'tiktok-live-connector';

export class TikTokConnector {
  constructor(casinoInstance) {
    this.casino = casinoInstance;
    this.connector = null;
    this.username = '';
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 3;
    this.reconnectInterval = null;
  }

  async connectToUser(username) {
    this.username = username;
    this.connectionAttempts++;
    
    try {
      // Creează conectorul TikTok Live
      this.connector = new TikTokLiveConnector(username, {
        enableExtendedGiftInfo: true,
        enableWebsocketUpgrade: true,
        requestPollingIntervalMs: 1000,
        sessionId: null, // Va fi setat automat
        requestOptions: {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        }
      });

      // Încearcă conectarea
      await this.connector.connect();
      
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      // Configurează event listeners pentru evenimente reale
      this.setupEventListeners();
      
      // Notifică casino-ul despre conexiunea reușită
      this.casino.addChatMessage('🔴 LIVE', `✅ Conectat REAL la @${username}! Chat-ul live este activ!`);
      this.casino.isReallyConnected = true;
      
      // Actualizează UI-ul
      this.updateConnectionStatus(true);
      
      return true;
      
    } catch (error) {
      console.error('Eroare conectare TikTok Live:', error);
      this.isConnected = false;
      
      // Determină tipul de eroare
      let errorMessage = 'Eroare necunoscută';
      if (error.message.includes('LIVE_STREAM_NOT_FOUND')) {
        errorMessage = `@${username} nu este live acum sau username-ul este greșit`;
      } else if (error.message.includes('RATE_LIMIT')) {
        errorMessage = 'Prea multe încercări. Încearcă din nou în câteva minute';
      } else if (error.message.includes('NETWORK')) {
        errorMessage = 'Probleme de conexiune la internet';
      } else {
        errorMessage = `Eroare: ${error.message}`;
      }
      
      this.casino.addChatMessage('❌ ERROR', errorMessage);
      
      // Încearcă reconectarea automată
      if (this.connectionAttempts < this.maxRetries) {
        this.casino.addChatMessage('🔄 RETRY', `Încerc reconectarea... (${this.connectionAttempts}/${this.maxRetries})`);
        setTimeout(() => {
          this.connectToUser(username);
        }, 5000);
      } else {
        this.casino.addChatMessage('💔 FAILED', 'Conexiunea a eșuat. Verifică username-ul și încearcă din nou.');
        this.updateConnectionStatus(false);
      }
      
      return false;
    }
  }

  setupEventListeners() {
    if (!this.connector) return;

    // Chat real de la TikTok Live
    this.connector.on('chat', (data) => {
      console.log('📝 Chat real primit:', data);
      this.casino.addChatMessage(data.nickname, data.comment);
      this.casino.processChatCommand(data.nickname, data.comment);
    });

    // Cadouri reale de la TikTok Live
    this.connector.on('gift', (data) => {
      console.log('🎁 Cadou real primit:', data);
      const message = `🎁 ${data.nickname} a trimis ${data.repeatCount}x ${data.giftName}!`;
      this.casino.addChatMessage('🎁 GIFT', message);
      this.casino.handleGiftReceived(data.nickname, data.giftName, data.repeatCount);
    });

    // Like-uri reale de la TikTok Live
    this.connector.on('like', (data) => {
      console.log('❤️ Like real primit:', data);
      if (data.likeCount > 10) { // Doar pentru like-uri multiple
        this.casino.addChatMessage('❤️ LIKES', `${data.nickname} a dat ${data.likeCount} like-uri!`);
        this.casino.handleTapAction(data.nickname);
      }
    });

    // Urmăritori noi reali
    this.connector.on('follow', (data) => {
      console.log('👥 Follow real primit:', data);
      this.casino.addChatMessage('👥 FOLLOW', `${data.nickname} te-a urmărit!`);
    });

    // Share-uri reale
    this.connector.on('share', (data) => {
      console.log('📤 Share real primit:', data);
      this.casino.addChatMessage('📤 SHARE', `${data.nickname} a distribuit live-ul!`);
      this.casino.handleShareAction(data.nickname);
    });

    // Viewer count real
    this.connector.on('roomUser', (data) => {
      console.log('👥 Viewer count real:', data.viewerCount);
      this.casino.updateViewerCount(data.viewerCount);
    });

    // Conexiune pierdută
    this.connector.on('disconnected', () => {
      console.log('💔 Conexiune pierdută cu TikTok Live');
      this.isConnected = false;
      this.casino.addChatMessage('💔 DISCONNECTED', 'Conexiunea cu TikTok Live s-a pierdut');
      this.updateConnectionStatus(false);
      
      // Încearcă reconectarea automată
      this.attemptReconnect();
    });

    // Erori de conexiune
    this.connector.on('error', (error) => {
      console.error('❌ Eroare TikTok Live:', error);
      this.casino.addChatMessage('❌ ERROR', `Eroare conexiune: ${error.message}`);
    });

    // Conexiune stabilită cu succes
    this.connector.on('connected', (state) => {
      console.log('✅ Conectat cu succes la TikTok Live:', state);
      this.casino.addChatMessage('✅ CONNECTED', `Live conectat! ${state.viewerCount} viewers`);
    });
  }

  attemptReconnect() {
    if (this.reconnectInterval) return; // Evită multiple încercări
    
    let attempts = 0;
    this.reconnectInterval = setInterval(async () => {
      attempts++;
      
      if (attempts > this.maxRetries) {
        clearInterval(this.reconnectInterval);
        this.reconnectInterval = null;
        this.casino.addChatMessage('💔 FAILED', 'Reconectarea automată a eșuat');
        return;
      }
      
      this.casino.addChatMessage('🔄 RECONNECT', `Încerc reconectarea... (${attempts}/${this.maxRetries})`);
      
      try {
        await this.connector.connect();
        this.isConnected = true;
        clearInterval(this.reconnectInterval);
        this.reconnectInterval = null;
        this.casino.addChatMessage('✅ RECONNECTED', 'Reconectat cu succes!');
        this.updateConnectionStatus(true);
      } catch (error) {
        console.log('Reconectare eșuată:', error.message);
      }
    }, 10000); // Încearcă la fiecare 10 secunde
  }

  disconnect() {
    if (this.connector && this.isConnected) {
      this.connector.disconnect();
      this.isConnected = false;
      this.casino.addChatMessage('👋 DISCONNECTED', 'Deconectat de la TikTok Live');
      this.updateConnectionStatus(false);
    }
    
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
    }
  }

  updateConnectionStatus(connected) {
    // Actualizează indicatorul de status în UI
    const liveIndicator = document.getElementById('live-indicator');
    if (liveIndicator) {
      if (connected) {
        liveIndicator.classList.add('connected');
        liveIndicator.textContent = `🔴 LIVE - @${this.username}`;
      } else {
        liveIndicator.classList.remove('connected');
        liveIndicator.textContent = 'OFFLINE';
      }
    }
  }
}

// Funcții helper pentru validare
export const TikTokUtils = {
  // Validează username-ul TikTok
  validateUsername(username) {
    if (!username || username.trim().length === 0) {
      return { valid: false, error: 'Username-ul nu poate fi gol' };
    }
    
    // Elimină @ dacă există
    username = username.replace('@', '');
    
    // Verifică lungimea
    if (username.length < 2 || username.length > 24) {
      return { valid: false, error: 'Username-ul trebuie să aibă între 2-24 caractere' };
    }
    
    // Verifică caracterele permise
    if (!/^[a-zA-Z0-9._]+$/.test(username)) {
      return { valid: false, error: 'Username-ul poate conține doar litere, cifre, punct și underscore' };
    }
    
    return { valid: true, username: username };
  }
};

import { OperatorFunction } from '../types';
export declare function map<T, R>(project: (value: T, index: number) => R): OperatorFunction<T, R>;
/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */
export declare function map<T, R, A>(project: (this: A, value: T, index: number) => R, thisArg: A): OperatorFunction<T, R>;
//# sourceMappingURL=map.d.ts.map
{"version": 3, "file": "tiktok-signer.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/lib/tiktok-signer.ts"], "names": [], "mappings": ";;;;;;AACA,2CAAkF;AAClF,+EAAkH;AAElH,+BAAmC;AAGnC;;GAEG;AACH,MAAa,WAAY,SAAQ,uBAAoB;IAEjD,YAAY,SAAuC,EAAE;QACjD,KAAK,CAAC,EAAE,GAAG,gBAAU,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,WAAW,CAAC,GAAiB,EAAE,MAAoC,EAAE,SAAiB;QAC/F,MAAM,gBAAgB,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAC5D,IAAI,QAAQ,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAE9D,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE;YAClC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YAC3E,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SAC5C;QACD,eAAe;QACf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9C;YACI,GAAG,EAAE,QAAQ;YACb,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,SAAS;SACvB,CACJ,CAAC;QAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;YACzB,MAAM,IAAI,4BAAmB,CACzB,0EAA0E,EAC1E,QAAQ,CAAC,IAAI,CAAC,OAAO,EACrB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAChC,CAAC;SACL;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/E,MAAM,IAAI,oCAA2B,CACjC,6DAA6D,CAChE,CAAC;SACL;QAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;YACzB,MAAM,IAAI,oCAA2B,CACjC,6BAA6B,QAAQ,EAAE,IAAI,EAAE,OAAO,IAAI,eAAe,EAAE,CAC5E,CAAC;SACL;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC;IACzB,CAAC;CACJ;AApDD,kCAoDC", "sourcesContent": ["import { URL } from 'url';\nimport { PremiumFeatureError, SignatureMissingTokensError } from '@/types/errors';\nimport EulerStreamApiClient, { ClientConfiguration, SignWebcastUrl200Response } from '@eulerstream/euler-api-sdk';\nimport { ISignTikTokUrlBodyMethodEnum } from '@eulerstream/euler-api-sdk/dist/sdk/api';\nimport { SignConfig } from '@/lib';\n\n\n/**\n * TikTok Signer class\n */\nexport class EulerSigner extends EulerStreamApiClient  {\n\n    constructor(config: Partial<ClientConfiguration> = {}) {\n        super({ ...SignConfig, ...config });\n    }\n\n    /**\n     * Sign a URL using the TikTok signature provider\n     *\n     * @param url The URL to sign\n     * @param method The HTTP method to use (GET, POST, etc.)\n     * @param userAgent The user agent to sign with\n     */\n    public async webcastSign(url: string | URL, method: ISignTikTokUrlBodyMethodEnum, userAgent: string): Promise<SignWebcastUrl200Response> {\n        const mustRemoveParams = ['X-Bogus', 'X-Gnarly', 'msToken'];\n        let cleanUrl = typeof url === 'string' ? url : url.toString();\n\n        for (const param of mustRemoveParams) {\n            cleanUrl = cleanUrl.replace(new RegExp(`([&?])${param}=[^&]*`, 'g'), '$1');\n            cleanUrl = cleanUrl.replace(/[&?]$/, '');\n        }\n        // Sign the URL\n        const response = await this.webcast.signWebcastUrl(\n            {\n                url: cleanUrl,\n                method: method,\n                userAgent: userAgent\n            }\n        );\n\n        if (response.status === 403) {\n            throw new PremiumFeatureError(\n                'You do not have permission from the signature provider to sign this URL.',\n                response.data.message,\n                JSON.stringify(response.data)\n            );\n        }\n\n        if (!response.data || Object.keys(response.data.response.tokens || {}).length < 1) {\n            throw new SignatureMissingTokensError(\n                'Failed to sign a request due to missing tokens in response!'\n            );\n        }\n\n        if (response.status !== 200) {\n            throw new SignatureMissingTokensError(\n                `Failed to sign a request: ${response?.data?.message || 'Unknown error'}`\n            );\n        }\n\n        return response.data;\n    }\n}\n"]}
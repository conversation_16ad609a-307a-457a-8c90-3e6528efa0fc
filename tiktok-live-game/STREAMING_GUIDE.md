# 🎮 TikTok Live Adventure Game - Streaming Guide

## Overview

The TikTok Live Adventure Game is a professional interactive game designed specifically for TikTok Live streamers. It provides multiple engaging features that allow your viewers to participate in real-time adventures, making your streams more interactive and entertaining.

## 🚀 Quick Start

### 1. Running the Game
```bash
npm install
npm run dev
```
The game will be available at `http://localhost:5173/`

### 2. For Live Streaming (OBS/TikTok Live Studio)
Add as Browser Source: `http://localhost:5173/?mode=browser-source`

## 🎯 Game Features

### Adventure Wheel
- **Interactive spinning wheel** with customizable options
- Viewers can influence outcomes through chat commands
- Smooth animations optimized for streaming
- **Chat Command**: `!spin`

### Community Voting
- Real-time polls for viewer participation
- Customizable questions and options
- Live vote counting with visual progress bars
- **Chat Command**: `!vote`

### Live Chat Integration
- Simulated TikTok Live chat experience
- Command processing for viewer interactions
- Real-time message display
- **Chat Command**: `!chat`

### Story Mode
- Progressive adventure storytelling
- Community-driven narrative development
- Chapter-based story progression
- **Chat Command**: `!story`

## 📱 TikTok Live Integration

### Connection Setup
1. Click the **📱 TikTok button** in the top-right corner
2. Click **"Connect to TikTok Live"**
3. Enter your stream key (when available)
4. Start streaming!

### Available Chat Commands
- `!spin` - Spin the adventure wheel
- `!vote` - Start or participate in voting
- `!story` - Switch to story mode
- `!wheel` - Switch to wheel mode
- `!chat` - Switch to chat mode
- `!help` - Show all available commands

### Gift Integration
The game responds to TikTok gifts with special effects:
- **Rose** (1 point) - Sparkle effect ✨
- **Heart** (5 points) - Heart animation 💖
- **Diamond** (10 points) - Diamond rain 💎
- **Rocket** (50 points) - Rocket boost 🚀
- **Lion** (100 points) - Golden lion effect 🦁👑

## 🎥 OBS Studio Setup

### Method 1: Browser Source
1. Add **Browser Source** in OBS
2. URL: `http://localhost:5173/?mode=browser-source`
3. Width: 1920, Height: 1080
4. Check "Shutdown source when not visible"

### Method 2: Window Capture
1. Open the game in your browser
2. Add **Window Capture** in OBS
3. Select your browser window
4. Crop as needed

### Recommended OBS Settings
- **Resolution**: 1920x1080 or 1280x720
- **FPS**: 30 or 60
- **Bitrate**: 2500-6000 kbps (depending on your upload speed)

## 🎨 Customization Options

### Visual Themes
Access via the ⚙️ settings button:
- **Rainbow** - Vibrant multi-color theme
- **Neon** - Bright neon colors
- **Pastel** - Soft, gentle colors
- **Dark** - Monochrome dark theme

### Game Settings
- **Sound Effects**: Enable/disable audio feedback
- **Animation Speed**: Adjust animation timing (1-5)
- **Color Themes**: Change visual appearance

## 🎮 Streamer Controls

### Keyboard Shortcuts
- **Ctrl/Cmd + 1**: Switch to Wheel section
- **Ctrl/Cmd + 2**: Switch to Voting section
- **Ctrl/Cmd + 3**: Switch to Chat section
- **Ctrl/Cmd + 4**: Switch to Story section
- **Ctrl/Cmd + Space**: Spin wheel (when in wheel section)

### Manual Controls
- **Add Wheel Options**: Type new adventure options
- **Start Votes**: Create custom polls for viewers
- **Story Chapters**: Add new story elements
- **Chat Simulation**: Test chat interactions

## 📊 Viewer Engagement Tips

### Best Practices
1. **Announce Commands**: Tell viewers about available chat commands
2. **Regular Interaction**: Use the wheel every 5-10 minutes
3. **Story Building**: Let votes influence your adventure story
4. **Gift Acknowledgment**: React to gift effects for engagement

### Content Ideas
- **Adventure Streams**: Use the wheel to decide your next game move
- **Community Stories**: Build collaborative narratives
- **Decision Making**: Let viewers vote on important choices
- **Interactive Challenges**: Create custom wheel options for challenges

## 🔧 Technical Requirements

### Minimum System Requirements
- **Browser**: Chrome 80+, Firefox 75+, Safari 13+
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: Dual-core 2.5GHz or equivalent
- **Internet**: 5 Mbps upload for smooth streaming

### Recommended Setup
- **Dual Monitor**: One for game, one for OBS/chat
- **Dedicated GPU**: For smooth streaming encoding
- **Wired Internet**: For stable connection
- **Good Lighting**: For webcam if using face cam

## 🛠️ Troubleshooting

### Common Issues

**Game Won't Load**
- Check browser console for errors
- Ensure JavaScript is enabled
- Try refreshing the page

**OBS Browser Source Black Screen**
- Restart OBS
- Check URL is correct
- Ensure browser source dimensions are set

**Chat Commands Not Working**
- Verify TikTok integration is connected
- Check command syntax (must start with !)
- Ensure you're in the correct game section

**Performance Issues**
- Lower animation speed in settings
- Close unnecessary browser tabs
- Reduce OBS encoding settings

### Getting Help
- Check browser developer console for errors
- Verify all files are properly loaded
- Test in different browsers
- Restart the development server

## 🎯 Advanced Features

### API Integration
The game exposes a global `TikTokLiveGame` object for external integrations:

```javascript
// Trigger wheel spin
window.TikTokLiveGame.spinWheel();

// Add chat message
window.TikTokLiveGame.addChatMessage('Username', 'Message');

// Switch sections
window.TikTokLiveGame.switchSection('voting-section');

// Get game state
const state = window.TikTokLiveGame.exportGameState();
```

### Custom Modifications
- Edit `src/main.js` for game logic changes
- Modify `src/style.css` for visual customizations
- Update `src/tiktok-integration.js` for TikTok features

## 📈 Analytics & Metrics

### Built-in Tracking
- Viewer count simulation
- Chat message frequency
- Wheel spin statistics
- Vote participation rates
- Gift effect triggers

### Stream Metrics
- Connection status monitoring
- Real-time viewer updates
- Command usage statistics
- Engagement rate tracking

## 🎊 Pro Tips for Streamers

1. **Pre-stream Setup**: Test all features before going live
2. **Backup Plans**: Have alternative content ready
3. **Viewer Education**: Explain commands at stream start
4. **Regular Updates**: Keep the adventure fresh with new options
5. **Community Building**: Use voting to make viewers feel involved
6. **Gift Reactions**: Always acknowledge and react to gifts
7. **Story Continuity**: Keep adventure stories consistent across streams
8. **Technical Checks**: Monitor connection status during stream

## 📞 Support

For technical support or feature requests:
- Check the troubleshooting section above
- Review browser console for error messages
- Test with different browsers
- Verify all dependencies are installed

---

**Happy Streaming! 🎮✨**

Make your TikTok Live streams unforgettable with interactive adventures that keep your viewers engaged and coming back for more!

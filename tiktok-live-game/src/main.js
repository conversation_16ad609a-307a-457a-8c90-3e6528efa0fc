import './style.css'
import { TikTokLiveIntegration } from './tiktok-integration.js'
import { StreamingUtils } from './streaming-utils.js'
import { AutoPilot } from './auto-pilot.js'

// TikTok Live Adventure Game - Main Application
class TikTokLiveGame {
  constructor() {
    this.currentSection = 'wheel-section';
    this.wheelOptions = [
      'Explore the Mysterious Cave',
      'Climb the Enchanted Mountain',
      'Sail Across the Stormy Sea',
      'Enter the Forbidden Forest',
      'Visit the Ancient Temple',
      'Cross the Desert of Wonders'
    ];
    this.wheelColors = [
      '#ff0050', '#00f2ea', '#ffd700', '#ff8800', '#00ff88', '#8a2be2'
    ];
    this.isSpinning = false;
    this.currentRotation = 0;
    this.votes = {};
    this.voteTimer = null;
    this.voteTimeLeft = 30;
    this.chatMessages = [];
    this.storyChapters = [];
    this.viewerCount = 0;
    this.settings = {
      colorTheme: 'rainbow',
      soundEnabled: true,
      animationSpeed: 3
    };
    this.tiktokIntegration = null;
    this.streamingUtils = null;
    this.autoPilot = null;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.drawWheel();
    this.updateViewerCount();
    this.initializeStory();
    this.simulateViewerActivity();
    this.initializeTikTokIntegration();
    this.addLiveIndicator();
    this.initializeStreamingUtils();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Wheel controls
    document.getElementById('spin-wheel').addEventListener('click', () => this.spinWheel());
    document.getElementById('add-option').addEventListener('click', () => this.addWheelOption());
    document.getElementById('clear-wheel').addEventListener('click', () => this.clearWheel());
    document.getElementById('new-option').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.addWheelOption();
    });

    // Voting controls
    document.getElementById('start-vote').addEventListener('click', () => this.startVote());
    document.getElementById('end-vote').addEventListener('click', () => this.endVote());

    // Chat controls
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Story controls
    document.getElementById('new-chapter').addEventListener('click', () => this.addStoryChapter());
    document.getElementById('reset-story').addEventListener('click', () => this.resetStory());

    // Settings
    document.getElementById('settings-toggle').addEventListener('click', () => this.toggleSettings());
    document.getElementById('close-settings').addEventListener('click', () => this.toggleSettings());
    document.getElementById('color-theme').addEventListener('change', (e) => this.updateColorTheme(e.target.value));
    document.getElementById('sound-enabled').addEventListener('change', (e) => this.updateSoundSetting(e.target.checked));
    document.getElementById('animation-speed').addEventListener('input', (e) => this.updateAnimationSpeed(e.target.value));
  }

  switchSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.game-section').forEach(section => {
      section.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  // Wheel functionality
  drawWheel() {
    const canvas = document.getElementById('adventure-wheel');
    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 180;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (this.wheelOptions.length === 0) {
      ctx.fillStyle = '#333';
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.fill();

      ctx.fillStyle = '#fff';
      ctx.font = '20px Inter';
      ctx.textAlign = 'center';
      ctx.fillText('Add options to spin!', centerX, centerY);
      return;
    }

    const anglePerOption = (2 * Math.PI) / this.wheelOptions.length;

    // Draw wheel segments
    this.wheelOptions.forEach((option, index) => {
      const startAngle = index * anglePerOption + this.currentRotation;
      const endAngle = (index + 1) * anglePerOption + this.currentRotation;

      // Draw segment
      ctx.fillStyle = this.wheelColors[index % this.wheelColors.length];
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fill();

      // Draw border
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw text
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(startAngle + anglePerOption / 2);
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 14px Inter';
      ctx.textAlign = 'center';

      // Wrap text if too long
      const words = option.split(' ');
      if (words.length > 2) {
        ctx.fillText(words.slice(0, 2).join(' '), radius * 0.7, -5);
        ctx.fillText(words.slice(2).join(' '), radius * 0.7, 10);
      } else {
        ctx.fillText(option, radius * 0.7, 0);
      }
      ctx.restore();
    });

    // Draw center circle
    ctx.fillStyle = '#1a1a1a';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI);
    ctx.fill();
    ctx.strokeStyle = '#ffd700';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw pointer
    ctx.fillStyle = '#ffd700';
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - radius - 20);
    ctx.lineTo(centerX - 15, centerY - radius - 5);
    ctx.lineTo(centerX + 15, centerY - radius - 5);
    ctx.closePath();
    ctx.fill();
  }

  spinWheel() {
    if (this.isSpinning || this.wheelOptions.length === 0) return;

    this.isSpinning = true;
    const spinButton = document.getElementById('spin-wheel');
    spinButton.disabled = true;
    spinButton.textContent = 'SPINNING...';

    // Random spin amount (3-6 full rotations plus random angle)
    const spins = 3 + Math.random() * 3;
    const finalRotation = spins * 2 * Math.PI + Math.random() * 2 * Math.PI;

    const canvas = document.getElementById('adventure-wheel');
    canvas.classList.add('spinning');

    // Animate the wheel
    const startTime = Date.now();
    const duration = 3000; // 3 seconds
    const startRotation = this.currentRotation;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth deceleration
      const easeOut = 1 - Math.pow(1 - progress, 3);

      this.currentRotation = startRotation + finalRotation * easeOut;
      this.drawWheel();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.finishSpin();
      }
    };

    animate();
  }

  finishSpin() {
    this.isSpinning = false;
    const canvas = document.getElementById('adventure-wheel');
    canvas.classList.remove('spinning');

    // Calculate which option was selected
    const normalizedRotation = (this.currentRotation % (2 * Math.PI) + 2 * Math.PI) % (2 * Math.PI);
    const anglePerOption = (2 * Math.PI) / this.wheelOptions.length;
    const selectedIndex = Math.floor((2 * Math.PI - normalizedRotation) / anglePerOption) % this.wheelOptions.length;
    const selectedOption = this.wheelOptions[selectedIndex];

    // Update UI
    const spinButton = document.getElementById('spin-wheel');
    spinButton.disabled = false;
    spinButton.textContent = 'SPIN THE WHEEL!';

    // Show result
    this.showSpinResult(selectedOption);
    this.addToStory(`The wheel has chosen: ${selectedOption}! The adventure continues...`);

    // Play sound effect if enabled
    if (this.settings.soundEnabled) {
      this.playSound('spin-complete');
    }
  }

  showSpinResult(result) {
    // Create a temporary result display
    const resultDiv = document.createElement('div');
    resultDiv.className = 'spin-result';
    resultDiv.innerHTML = `
      <h3>🎉 Wheel Result!</h3>
      <p>${result}</p>
    `;
    resultDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(45deg, #ff0050, #ffd700);
      color: white;
      padding: 2rem;
      border-radius: 12px;
      text-align: center;
      z-index: 1000;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      animation: fadeIn 0.5s ease-in-out;
    `;

    document.body.appendChild(resultDiv);

    // Remove after 3 seconds
    setTimeout(() => {
      resultDiv.style.animation = 'fadeOut 0.5s ease-in-out';
      setTimeout(() => {
        document.body.removeChild(resultDiv);
      }, 500);
    }, 3000);
  }

  addWheelOption() {
    const input = document.getElementById('new-option');
    const option = input.value.trim();

    if (option && !this.wheelOptions.includes(option)) {
      this.wheelOptions.push(option);
      input.value = '';
      this.drawWheel();
      this.addChatMessage('System', `Added new option: "${option}"`);
    }
  }

  clearWheel() {
    this.wheelOptions = [];
    this.drawWheel();
    this.addChatMessage('System', 'Wheel cleared! Add new options to continue.');
  }

  // Voting functionality
  startVote() {
    if (this.voteTimer) return;

    const question = prompt('Enter voting question:') || 'What should we do next?';
    const optionsInput = prompt('Enter options (separated by commas):') || 'Option A, Option B, Option C';
    const options = optionsInput.split(',').map(opt => opt.trim()).filter(opt => opt);

    if (options.length < 2) {
      alert('Please provide at least 2 options!');
      return;
    }

    document.getElementById('voting-question').textContent = question;
    this.votes = {};
    this.voteTimeLeft = 30;

    // Create voting options
    const container = document.getElementById('voting-options');
    container.innerHTML = '';

    options.forEach((option, index) => {
      const optionDiv = document.createElement('div');
      optionDiv.className = 'vote-option';
      optionDiv.innerHTML = `
        <div class="vote-text">${option}</div>
        <div class="vote-count">0 votes</div>
        <div class="vote-progress" style="width: 0%"></div>
      `;
      optionDiv.addEventListener('click', () => this.castVote(index, option));
      container.appendChild(optionDiv);
      this.votes[index] = { option, count: 0 };
    });

    // Start timer
    this.voteTimer = setInterval(() => {
      this.voteTimeLeft--;
      document.getElementById('vote-timer').textContent = `${this.voteTimeLeft}s`;

      if (this.voteTimeLeft <= 0) {
        this.endVote();
      }
    }, 1000);

    this.addChatMessage('System', `Vote started: ${question}`);
  }

  castVote(optionIndex, optionText) {
    if (!this.voteTimer) return;

    this.votes[optionIndex].count++;
    this.updateVoteDisplay();
    this.addChatMessage(`Viewer${Math.floor(Math.random() * 1000)}`, `Voted for: ${optionText}`);
  }

  updateVoteDisplay() {
    const totalVotes = Object.values(this.votes).reduce((sum, vote) => sum + vote.count, 0);

    Object.entries(this.votes).forEach(([index, vote]) => {
      const optionDiv = document.querySelectorAll('.vote-option')[index];
      const percentage = totalVotes > 0 ? (vote.count / totalVotes) * 100 : 0;

      optionDiv.querySelector('.vote-count').textContent = `${vote.count} votes`;
      optionDiv.querySelector('.vote-progress').style.width = `${percentage}%`;
    });
  }

  endVote() {
    if (!this.voteTimer) return;

    clearInterval(this.voteTimer);
    this.voteTimer = null;
    document.getElementById('vote-timer').textContent = '30s';

    // Find winner
    const winner = Object.values(this.votes).reduce((max, vote) =>
      vote.count > max.count ? vote : max, { option: 'No votes', count: 0 });

    this.addChatMessage('System', `Vote ended! Winner: ${winner.option} (${winner.count} votes)`);
    this.addToStory(`The community voted for: ${winner.option}. The story takes an exciting turn!`);
  }

  // Chat functionality
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';

      // Simulate viewer responses
      setTimeout(() => {
        this.simulateViewerResponse(message);
      }, 1000 + Math.random() * 2000);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    // Keep only last 50 messages
    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }
  }

  simulateViewerResponse(originalMessage) {
    const responses = [
      'That sounds awesome!',
      'Great idea!',
      'Let\'s do it!',
      'I love this adventure!',
      'What happens next?',
      'This is so exciting!',
      'Can we explore more?',
      'Amazing choice!',
      'The suspense is killing me!',
      'Best stream ever!'
    ];

    const viewers = ['AdventureSeeker', 'QuestMaster', 'StreamFan', 'Explorer123', 'MysticWanderer'];
    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    this.addChatMessage(randomViewer, randomResponse);
  }

  // Story functionality
  initializeStory() {
    this.storyChapters = [
      'Welcome to the TikTok Live Adventure! Your viewers will help guide this epic journey through mysterious lands and exciting challenges.'
    ];
    this.updateStoryDisplay();
  }

  addStoryChapter() {
    const chapter = prompt('Enter new story chapter:');
    if (chapter && chapter.trim()) {
      this.addToStory(chapter.trim());
    }
  }

  addToStory(text) {
    this.storyChapters.push(text);
    this.updateStoryDisplay();
    this.addChatMessage('Narrator', `Story updated: ${text.substring(0, 50)}...`);
  }

  updateStoryDisplay() {
    const storyContent = document.getElementById('story-content');
    storyContent.innerHTML = this.storyChapters.map((chapter, index) =>
      `<p><strong>Chapter ${index + 1}:</strong> ${chapter}</p>`
    ).join('');
    storyContent.scrollTop = storyContent.scrollHeight;
  }

  resetStory() {
    if (confirm('Are you sure you want to reset the story?')) {
      this.initializeStory();
      this.addChatMessage('System', 'Story has been reset!');
    }
  }

  // Settings functionality
  toggleSettings() {
    const panel = document.getElementById('settings-panel');
    panel.classList.toggle('active');
  }

  updateColorTheme(theme) {
    this.settings.colorTheme = theme;

    // Update wheel colors based on theme
    switch (theme) {
      case 'neon':
        this.wheelColors = ['#ff0080', '#00ff80', '#8000ff', '#ff8000', '#0080ff', '#ff0040'];
        break;
      case 'pastel':
        this.wheelColors = ['#ffb3ba', '#bae1ff', '#ffffba', '#baffc9', '#ffdfba', '#e0bbff'];
        break;
      case 'dark':
        this.wheelColors = ['#333333', '#555555', '#777777', '#999999', '#bbbbbb', '#dddddd'];
        break;
      default: // rainbow
        this.wheelColors = ['#ff0050', '#00f2ea', '#ffd700', '#ff8800', '#00ff88', '#8a2be2'];
    }

    this.drawWheel();
  }

  updateSoundSetting(enabled) {
    this.settings.soundEnabled = enabled;
  }

  updateAnimationSpeed(speed) {
    this.settings.animationSpeed = parseInt(speed);
  }

  // Utility functions
  updateViewerCount() {
    this.viewerCount = Math.floor(Math.random() * 500) + 50;
    document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;
  }

  simulateViewerActivity() {
    // Simulate viewer count changes
    setInterval(() => {
      const change = Math.floor(Math.random() * 20) - 10;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;
    }, 10000);

    // Simulate random chat messages
    setInterval(() => {
      if (Math.random() < 0.3) { // 30% chance every 5 seconds
        const messages = [
          'This is so cool!',
          'What\'s next?',
          'Love this game!',
          'Spin the wheel!',
          'Start a vote!',
          'Tell us a story!',
          'This is amazing!',
          'Best stream ever!',
          'More adventures please!',
          'Can I join the quest?'
        ];
        const viewers = ['AdventureSeeker', 'QuestMaster', 'StreamFan', 'Explorer123', 'MysticWanderer', 'EpicGamer', 'StoryLover'];
        const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];

        this.addChatMessage(randomViewer, randomMessage);
      }
    }, 5000);
  }

  playSound(type) {
    if (!this.settings.soundEnabled) return;

    // Create audio context for sound effects
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    switch (type) {
      case 'spin-complete':
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.5);
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.5);
        break;
    }
  }

  initializeTikTokIntegration() {
    this.tiktokIntegration = new TikTokLiveIntegration(this);

    // Add TikTok panel toggle button
    const toggleButton = document.createElement('button');
    toggleButton.id = 'tiktok-toggle';
    toggleButton.className = 'tiktok-toggle';
    toggleButton.innerHTML = '📱';
    toggleButton.title = 'Toggle TikTok Live Panel';
    toggleButton.style.cssText = `
      position: fixed;
      top: 80px;
      right: 80px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(45deg, #ff0050, #00f2ea);
      border: none;
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
      z-index: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(255, 0, 80, 0.4);
    `;

    toggleButton.addEventListener('click', () => {
      const panel = document.getElementById('tiktok-connection-panel');
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    });

    document.body.appendChild(toggleButton);
  }

  initializeStreamingUtils() {
    this.streamingUtils = new StreamingUtils(this);

    // Create default overlays for streaming
    this.streamingUtils.createOverlay('viewer-count', {
      position: { top: '20px', right: '20px' }
    });

    this.streamingUtils.createOverlay('recent-activity', {
      position: { top: '20px', left: '20px' }
    });

    this.streamingUtils.createOverlay('stream-stats', {
      position: { bottom: '20px', left: '20px' }
    });

    // Start stream session tracking
    this.streamingUtils.startStreamSession();
  }

  initializeAutoPilot() {
    this.autoPilot = new AutoPilot(this);

    // Creează panoul de control Auto-Pilot
    this.createAutoPilotPanel();
  }

  createAutoPilotPanel() {
    const autoPilotPanel = document.createElement('div');
    autoPilotPanel.id = 'autopilot-panel';
    autoPilotPanel.className = 'autopilot-panel';
    autoPilotPanel.innerHTML = `
      <div class="autopilot-header">
        <h3>🤖 Auto-Pilot Control</h3>
        <div class="autopilot-status" id="autopilot-status">
          <span class="status-indicator offline"></span>
          <span class="status-text">Offline</span>
        </div>
      </div>

      <div class="autopilot-setup">
        <div class="setup-group">
          <label for="streamer-username">Username-ul tău:</label>
          <input type="text" id="streamer-username" placeholder="Introdu username-ul tău TikTok" />
        </div>

        <div class="setup-group">
          <label for="adventure-theme">Tema aventurii:</label>
          <select id="adventure-theme">
            <option value="fantasy">Fantasy (Magie & Mistere)</option>
            <option value="scifi">Sci-Fi (Spațiu & Tehnologie)</option>
            <option value="adventure">Aventură (Explorare & Acțiune)</option>
          </select>
        </div>

        <div class="setup-group">
          <label for="engagement-level">Nivel de interacțiune:</label>
          <select id="engagement-level">
            <option value="low">Scăzut (Relaxat)</option>
            <option value="medium">Mediu (Echilibrat)</option>
            <option value="high">Înalt (Foarte Activ)</option>
          </select>
        </div>
      </div>

      <div class="autopilot-controls">
        <button id="start-autopilot" class="autopilot-btn start-btn">🚀 Pornește Auto-Pilot</button>
        <button id="stop-autopilot" class="autopilot-btn stop-btn" disabled>⏹️ Oprește Auto-Pilot</button>
      </div>

      <div class="autopilot-settings">
        <h4>Setări Avansate:</h4>
        <div class="setting-row">
          <label>Interval roată (secunde):</label>
          <input type="range" id="wheel-interval" min="15" max="120" value="30">
          <span id="wheel-interval-value">30s</span>
        </div>
        <div class="setting-row">
          <label>Interval voturi (secunde):</label>
          <input type="range" id="vote-interval" min="30" max="300" value="60">
          <span id="vote-interval-value">60s</span>
        </div>
        <div class="setting-row">
          <label>Rata răspuns chat (%):</label>
          <input type="range" id="chat-response-rate" min="0" max="100" value="80">
          <span id="chat-response-rate-value">80%</span>
        </div>
      </div>

      <div class="autopilot-stats" id="autopilot-stats">
        <h4>Statistici Live:</h4>
        <div class="stats-row">
          <span>Mesaje procesate: <strong id="messages-processed">0</strong></span>
          <span>Răspunsuri generate: <strong id="responses-generated">0</strong></span>
        </div>
        <div class="stats-row">
          <span>Roți învârtite: <strong id="wheels-spun">0</strong></span>
          <span>Voturi create: <strong id="votes-created">0</strong></span>
        </div>
      </div>
    `;

    // Poziționează panoul
    autoPilotPanel.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--background-dark);
      border: 2px solid var(--primary-color);
      border-radius: 15px;
      padding: 2rem;
      z-index: 2000;
      width: 450px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(255, 0, 80, 0.3);
      display: block;
    `;

    document.body.appendChild(autoPilotPanel);

    // Event listeners pentru controale
    this.setupAutoPilotControls();
  }

  setupAutoPilotControls() {
    // Start Auto-Pilot
    document.getElementById('start-autopilot').addEventListener('click', () => {
      const username = document.getElementById('streamer-username').value.trim();
      if (!username) {
        alert('Te rog introdu username-ul tău!');
        return;
      }

      const theme = document.getElementById('adventure-theme').value;
      const engagementLevel = document.getElementById('engagement-level').value;

      // Actualizează setările
      this.autoPilot.currentAdventure.theme = theme;
      this.autoPilot.autoSettings.viewerEngagementLevel = engagementLevel;
      this.autoPilot.autoSettings.wheelSpinInterval = parseInt(document.getElementById('wheel-interval').value) * 1000;
      this.autoPilot.autoSettings.voteInterval = parseInt(document.getElementById('vote-interval').value) * 1000;
      this.autoPilot.autoSettings.chatResponseRate = parseInt(document.getElementById('chat-response-rate').value) / 100;

      // Pornește Auto-Pilot
      this.autoPilot.start(username);

      // Actualizează UI
      this.updateAutoPilotStatus(true);
      document.getElementById('start-autopilot').disabled = true;
      document.getElementById('stop-autopilot').disabled = false;

      // Ascunde panoul după 3 secunde
      setTimeout(() => {
        document.getElementById('autopilot-panel').style.display = 'none';
      }, 3000);
    });

    // Stop Auto-Pilot
    document.getElementById('stop-autopilot').addEventListener('click', () => {
      this.autoPilot.stop();
      this.updateAutoPilotStatus(false);
      document.getElementById('start-autopilot').disabled = false;
      document.getElementById('stop-autopilot').disabled = true;
    });

    // Actualizează valorile în timp real
    document.getElementById('wheel-interval').addEventListener('input', (e) => {
      document.getElementById('wheel-interval-value').textContent = e.target.value + 's';
    });

    document.getElementById('vote-interval').addEventListener('input', (e) => {
      document.getElementById('vote-interval-value').textContent = e.target.value + 's';
    });

    document.getElementById('chat-response-rate').addEventListener('input', (e) => {
      document.getElementById('chat-response-rate-value').textContent = e.target.value + '%';
    });

    // Buton pentru a arăta/ascunde panoul
    const toggleButton = document.createElement('button');
    toggleButton.id = 'autopilot-toggle';
    toggleButton.innerHTML = '🤖';
    toggleButton.title = 'Auto-Pilot Control';
    toggleButton.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(45deg, #ff0050, #8a2be2);
      border: none;
      color: white;
      font-size: 2rem;
      cursor: pointer;
      z-index: 1500;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(255, 0, 80, 0.4);
    `;

    toggleButton.addEventListener('click', () => {
      const panel = document.getElementById('autopilot-panel');
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    });

    document.body.appendChild(toggleButton);
  }

  updateAutoPilotStatus(isActive) {
    const statusElement = document.querySelector('#autopilot-status .status-indicator');
    const statusText = document.querySelector('#autopilot-status .status-text');

    if (isActive) {
      statusElement.className = 'status-indicator online';
      statusText.textContent = 'Online & Active';
    } else {
      statusElement.className = 'status-indicator offline';
      statusText.textContent = 'Offline';
    }
  }

  addLiveIndicator() {
    const liveIndicator = document.createElement('div');
    liveIndicator.id = 'live-indicator';
    liveIndicator.className = 'live-indicator';
    liveIndicator.textContent = 'LIVE';
    document.body.appendChild(liveIndicator);
  }

  updateViewerCount() {
    document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;

    // Update live indicator if streaming
    const liveIndicator = document.getElementById('live-indicator');
    if (this.tiktokIntegration && this.tiktokIntegration.getStreamStats().isLive) {
      liveIndicator.classList.add('active');
    } else {
      liveIndicator.classList.remove('active');
    }
  }

  // Enhanced method to support browser source mode for OBS
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');

    // Hide unnecessary elements for streaming
    const elementsToHide = [
      '.tiktok-panel',
      '.settings-toggle',
      '#tiktok-toggle'
    ];

    elementsToHide.forEach(selector => {
      const element = document.querySelector(selector);
      if (element) element.style.display = 'none';
    });

    // Add stream overlay
    this.addStreamOverlay();
  }

  addStreamOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'stream-overlay';
    overlay.innerHTML = `
      <div class="overlay-title">🎮 TikTok Live Adventure</div>
      <div class="overlay-content">
        <div>Viewers: <span id="overlay-viewers">${this.viewerCount}</span></div>
        <div>Mode: <span id="overlay-mode">Adventure Wheel</span></div>
      </div>
    `;
    document.body.appendChild(overlay);

    // Update overlay when section changes
    const originalSwitchSection = this.switchSection.bind(this);
    this.switchSection = (sectionId) => {
      originalSwitchSection(sectionId);
      const modeNames = {
        'wheel-section': 'Adventure Wheel',
        'voting-section': 'Community Vote',
        'chat-section': 'Live Chat',
        'story-section': 'Story Mode'
      };
      document.getElementById('overlay-mode').textContent = modeNames[sectionId] || 'Game Mode';
    };
  }

  // Method to export game state for external integrations
  exportGameState() {
    return {
      currentSection: this.currentSection,
      wheelOptions: this.wheelOptions,
      viewerCount: this.viewerCount,
      isSpinning: this.isSpinning,
      votes: this.votes,
      storyChapters: this.storyChapters,
      settings: this.settings,
      tiktokStats: this.tiktokIntegration ? this.tiktokIntegration.getStreamStats() : null
    };
  }

  // Method to handle external commands (for API integration)
  handleExternalCommand(command, data) {
    switch (command) {
      case 'spin_wheel':
        this.spinWheel();
        break;
      case 'start_vote':
        this.switchSection('voting-section');
        this.startVote();
        break;
      case 'add_chat_message':
        this.addChatMessage(data.username, data.message);
        break;
      case 'switch_section':
        this.switchSection(data.section);
        break;
      case 'trigger_gift_effect':
        if (this.tiktokIntegration) {
          this.tiktokIntegration.handleGiftReceived(data.username, data.giftType, data.giftData);
        }
        break;
    }
  }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
  const game = new TikTokLiveGame();

  // Make game instance globally available for external integrations
  window.TikTokLiveGame = game;

  // Check for browser source mode
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    game.enableBrowserSourceMode();
  }

  // Add keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          game.switchSection('wheel-section');
          break;
        case '2':
          e.preventDefault();
          game.switchSection('voting-section');
          break;
        case '3':
          e.preventDefault();
          game.switchSection('chat-section');
          break;
        case '4':
          e.preventDefault();
          game.switchSection('story-section');
          break;
        case ' ':
          e.preventDefault();
          if (game.currentSection === 'wheel-section') {
            game.spinWheel();
          }
          break;
      }
    }
  });
});

{"version": 3, "file": "http-client.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/lib/http-client.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA6C;AAC7C,+CAAqD;AACrD,0EAAiD;AAEjD,0DAAkC;AAClC,iEAAuF;AACvF,+BAAoC;AAGpC,MAAqB,iBAAiB;IAYd;IAOA;IAjBpB,sBAAsB;IACN,aAAa,CAAgB;IAE7C,sBAAsB;IACN,SAAS,CAAY;IAErC,kCAAkC;IAC3B,YAAY,CAAyB;IAE5C,YACoB,gBAAyC;QACrD,aAAa,EAAE,EAAE;QACjB,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,EAAE;QAChB,cAAc,EAAE,KAAK;QACrB,UAAU,EAAE,SAAS;KACxB,EACe,YAAyB,IAAI,iBAAW,CAAC,EAAC,MAAM,EAAE,aAAa,CAAC,UAAU,EAAC,CAAC;QAP5E,kBAAa,GAAb,aAAa,CAM5B;QACe,cAAS,GAAT,SAAS,CAAmE;QAG5F,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAC9B,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO,CAAC;YAC/D,OAAO,EAAE,EAAE,GAAG,gBAAM,CAAC,2BAA2B,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YACvF,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY;SACrC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG;YAChB,GAAG,gBAAM,CAAC,0BAA0B;YACpC,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY;SACrC,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEnD,4BAA4B;QAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,MAAM,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC;YAC7D,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAClD,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7F;IAEL,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM,CAAC,MAAc;QAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,MAAM,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAQ,IAAI,CAAC,YAAY,CAAC,OAAkB,IAAI,EAAE,CAAC;IACvD,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,OAAO,CAChB,EACI,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,EACX,MAAM,GAAG,KAAK,EACd,OAAO,EACP,GAAG,YAAY,EACc;QAGjC,wBAAwB;QACxB,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QACvG,IAAI,GAAG,GAAW,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,eAAe,CAAC,MAAM,IAAI,EAAE,CAAC,EAAE,CAAC;QAEpG,kEAAkE;QAClE,IAAI,WAAW,EAAE;YACb,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kCAA4B,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAkC,CAAC,CAAC;YAC9H,IAAI,CAAC,UAAU,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,+BAA+B,MAAM,oBAAoB,MAAM,CAAC,MAAM,CAAC,kCAA4B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACtI;YAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CACjD,GAAG,EACH,MAAM,CAAC,WAAW,EAAkC,EACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAW,CAC9D,CAAC;YAEF,GAAG,GAAG,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;YAEtC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;SAC3D;QAGD,sBAAsB;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAC7B;YACI,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,OAAO,IAAI,SAAS;YAC7B,MAAM,EAAE,MAAM;YACd,GAAG,YAAY;SAClB,CACJ,CAAC;IAEN,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,wBAAwB,CACjC,IAAY,EACZ,UAAmD,EAAE;QAGrD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CACpC;YACI,IAAI,EAAE,gBAAM,CAAC,eAAe;YAC5B,IAAI,EAAE,IAAI;YACV,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,KAAK;YAClB,GAAG,OAAO;SACb,CACJ,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,mCAAmC,CAC5C,IAAY,EACZ,MAA2B,EAC3B,UAAa,EACb,cAAuB,KAAK,EAC5B,UAAmD,EAAE;QAErD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CACpC;YACI,IAAI,EAAE,gBAAM,CAAC,mBAAmB;YAChC,IAAI,EAAE,UAAU,GAAG,IAAI;YACvB,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,WAAW;YACxB,YAAY,EAAE,aAAa;YAC3B,GAAG,OAAO;SACb,CACJ,CAAC;QAEF,OAAO,IAAA,8BAAkB,EAAC,UAAU,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAEM,KAAK,CAAC,0BAA0B,CACnC,IAAY,EACZ,MAA8B,EAC9B,IAAyB,EACzB,cAAuB,KAAK,EAC5B,UAAmD,EAAE;QAGrD,OAAO,CAAC,OAAO,KAAK,EAAE,CAAC;QACvB,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,iCAAiC,CAAC;QAEpE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CACpC;YACI,IAAI,EAAE,gBAAM,CAAC,mBAAmB;YAChC,IAAI,EAAE,UAAU,GAAG,IAAI;YACvB,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,WAAW;YACxB,MAAM,EAAE,MAAM;YACd,GAAG,OAAO;SACb,CACJ,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,2BAA2B,CACpC,IAAY,EACZ,MAA8B,EAC9B,cAAuB,KAAK,EAC5B,UAAmD,EAAE;QAGrD,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;QAErB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CACpC;YACI,IAAI,EAAE,gBAAM,CAAC,mBAAmB;YAChC,IAAI,EAAE,UAAU,GAAG,IAAI;YACvB,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,WAAW;YACxB,OAAO,EAAE;gBACL,GAAG,OAAO,CAAC,OAAO;aACrB;YACD,GAAG,OAAO;SACb,CACJ,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,0BAA0B,CACnC,IAAY,EACZ,MAA8B,EAC9B,cAAuB,KAAK,EAC5B,UAAmD,EAAE;QAGrD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CACpC;YACI,IAAI,EAAE,gBAAM,CAAC,eAAe;YAC5B,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,WAAW;YACxB,GAAG,OAAO;SACb,CACJ,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC;IAC9B,CAAC;CAEJ;AAxQD,oCAwQC", "sourcesContent": ["import axios, { AxiosInstance } from 'axios';\nimport { deserializeMessage } from '@/lib/utilities';\nimport <PERSON>ie<PERSON>ar from '@/lib/web/lib/cookie-jar';\nimport { WebcastHttpClientConfig, WebcastHttpClientRequestParams, WebcastMessage } from '@/types/client';\nimport Config from '@/lib/config';\nimport { ISignTikTokUrlBodyMethodEnum } from '@eulerstream/euler-api-sdk/dist/sdk/api';\nimport { EulerSigner } from '@/lib';\n\n\nexport default class WebcastHttpClient {\n\n    // HTTP Request Client\n    public readonly axiosInstance: AxiosInstance;\n\n    // External Cookie Jar\n    public readonly cookieJar: CookieJar;\n\n    // Internal Client Parameter Store\n    public clientParams: Record<string, string>;\n\n    constructor(\n        public readonly configuration: WebcastHttpClientConfig = {\n            customHeaders: {},\n            axiosOptions: {},\n            clientParams: {},\n            authenticateWs: false,\n            signApiKey: undefined\n        },\n        public readonly webSigner: EulerSigner = new EulerSigner({apiKey: configuration.signApiKey})\n    ) {\n\n        this.axiosInstance = axios.create({\n            timeout: parseInt(process.env.TIKTOK_CLIENT_TIMEOUT || '10000'),\n            headers: { ...Config.DEFAULT_HTTP_CLIENT_HEADERS, ...this.configuration.customHeaders },\n            ...this.configuration.axiosOptions\n        });\n\n        this.clientParams = {\n            ...Config.DEFAULT_HTTP_CLIENT_PARAMS,\n            ...this.configuration.clientParams\n        };\n\n        // Create the cookie jar\n        this.cookieJar = new CookieJar(this.axiosInstance);\n\n        // Process the cookie header\n        if (!!this.configuration.customHeaders?.Cookie) {\n            const cookieHeader = this.configuration.customHeaders.Cookie;\n            delete this.configuration.customHeaders['Cookie'];\n            cookieHeader.split('; ').forEach((v: string) => this.cookieJar.processSetCookieHeader(v));\n        }\n\n    }\n\n    /**\n     * Set the Room ID for the client\n     * @param roomId The client's Room ID\n     */\n    public set roomId(roomId: string) {\n        this.clientParams.room_id = roomId;\n    }\n\n    /**\n     * Get the Room ID for the client\n     */\n    public get roomId() {\n        return( this.clientParams.room_id as string) || '';\n    }\n\n    /**\n     * Build the URL for the request\n     *\n     * @param host The host for the request\n     * @param path The path for the request\n     * @param params The query parameters for the request\n     * @param signRequest Whether to sign the request or not\n     * @param method The HTTP method for the request\n     * @param headers The headers for the request\n     * @param extraOptions Additional axios request options\n     * @protected\n     */\n    public async request(\n        {\n            host,\n            path,\n            params,\n            signRequest,\n            method = 'GET',\n            headers,\n            ...extraOptions\n        }: WebcastHttpClientRequestParams\n    ) {\n\n        // Build the initial URL\n        let secure = !(host.startsWith('127.0.0.1') || host.startsWith('localhost') || host.startsWith('::1'));\n        let url: string = `http${secure ? 's' : ''}://${host}/${path}?${new URLSearchParams(params || {})}`;\n\n        // Sign the request. Assumption is if it doesn't throw, it worked.\n        if (signRequest) {\n            const signMethod = Object.values(ISignTikTokUrlBodyMethodEnum).includes(method.toUpperCase() as ISignTikTokUrlBodyMethodEnum);\n            if (!signMethod) {\n                throw new Error(`Invalid method for signing: ${method}. Must be one of ${Object.values(ISignTikTokUrlBodyMethodEnum).join(', ')}`);\n            }\n\n            const signResponse = await this.webSigner.webcastSign(\n                url,\n                method.toUpperCase() as ISignTikTokUrlBodyMethodEnum,\n                this.axiosInstance.defaults.headers['User-Agent'] as string\n            );\n\n            url = signResponse.response.signedUrl;\n\n            headers ||= {};\n            headers['User-Agent'] = signResponse.response.userAgent;\n        }\n\n\n        // Execute the request\n        return this.axiosInstance.request(\n            {\n                url: url,\n                headers: headers ?? undefined,\n                method: method,\n                ...extraOptions\n            }\n        );\n\n    }\n\n    /**\n     * Get HTML from TikTok website\n     *\n     * @param path Path to the HTML page\n     * @param options Additional request options\n     */\n    public async getHtmlFromTikTokWebsite(\n        path: string,\n        options: Partial<WebcastHttpClientRequestParams> = {}\n    ): Promise<string> {\n\n        const fetchResponse = await this.request(\n            {\n                host: Config.TIKTOK_HOST_WEB,\n                path: path,\n                responseType: 'text',\n                signRequest: false,\n                ...options\n            }\n        );\n\n        return fetchResponse.data;\n    }\n\n    /**\n     * Get deserialized object from Webcast API\n     *\n     * @param path Path to the API endpoint\n     * @param params Query parameters to be sent with the request\n     * @param schemaName Schema name for deserialization\n     * @param signRequest Whether to sign the request or not\n     * @param options Additional request options\n     */\n    public async getDeserializedObjectFromWebcastApi<T extends keyof WebcastMessage>(\n        path: string,\n        params: Record<string, any>,\n        schemaName: T,\n        signRequest: boolean = false,\n        options: Partial<WebcastHttpClientRequestParams> = {}\n    ) {\n        const fetchResponse = await this.request(\n            {\n                host: Config.TIKTOK_HOST_WEBCAST,\n                path: 'webcast/' + path,\n                params: params,\n                signRequest: signRequest,\n                responseType: 'arraybuffer',\n                ...options\n            }\n        );\n\n        return deserializeMessage(schemaName, fetchResponse.data);\n    }\n\n    public async postJsonObjectToWebcastApi<T extends Record<string, any>>(\n        path: string,\n        params: Record<string, string>,\n        data: Record<string, any>,\n        signRequest: boolean = false,\n        options: Partial<WebcastHttpClientRequestParams> = {}\n    ): Promise<T> {\n\n        options.headers ||= {};\n        options.headers['Content-Type'] = 'application/json; charset=UTF-8';\n\n        const fetchResponse = await this.request(\n            {\n                host: Config.TIKTOK_HOST_WEBCAST,\n                path: 'webcast/' + path,\n                data: data,\n                params: params,\n                responseType: 'json',\n                signRequest: signRequest,\n                method: 'POST',\n                ...options\n            }\n        );\n\n        return fetchResponse.data;\n    }\n\n    /**\n     * Get JSON object from Webcast API\n     *\n     * @param path Path to the API endpoint\n     * @param params Query parameters to be sent with the request\n     * @param signRequest Whether to sign the request or not\n     * @param options Additional request options\n     */\n    public async getJsonObjectFromWebcastApi<T extends Record<string, any>>(\n        path: string,\n        params: Record<string, string>,\n        signRequest: boolean = false,\n        options: Partial<WebcastHttpClientRequestParams> = {}\n    ): Promise<T> {\n\n        options.headers = {};\n\n        const fetchResponse = await this.request(\n            {\n                host: Config.TIKTOK_HOST_WEBCAST,\n                path: 'webcast/' + path,\n                params: params,\n                responseType: 'json',\n                signRequest: signRequest,\n                headers: {\n                    ...options.headers\n                },\n                ...options\n            }\n        );\n\n        return fetchResponse.data;\n    }\n\n    /**\n     * Get JSON object from TikTok API\n     *\n     * @param path Path to the API endpoint\n     * @param params Query parameters to be sent with the request\n     * @param signRequest Whether to sign the request or not\n     * @param options Additional request options\n     */\n    public async getJsonObjectFromTikTokApi<T extends Record<string, any>>(\n        path: string,\n        params: Record<string, string>,\n        signRequest: boolean = false,\n        options: Partial<WebcastHttpClientRequestParams> = {}\n    ): Promise<T> {\n\n        const fetchResponse = await this.request(\n            {\n                host: Config.TIKTOK_HOST_WEB,\n                path: path,\n                params: params,\n                responseType: 'json',\n                signRequest: signRequest,\n                ...options\n            }\n        );\n\n        return fetchResponse.data;\n    }\n\n}\n\n"]}
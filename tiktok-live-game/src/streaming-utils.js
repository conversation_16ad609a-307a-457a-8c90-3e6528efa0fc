// Streaming Utilities for TikTok Live Game
// Additional features for streamers and OBS integration

export class StreamingUtils {
  constructor(gameInstance) {
    this.game = gameInstance;
    this.overlayElements = new Map();
    this.streamStats = {
      startTime: null,
      totalSpins: 0,
      totalVotes: 0,
      totalChatMessages: 0,
      peakViewers: 0
    };
    this.alertQueue = [];
    this.isProcessingAlert = false;
  }

  // Create customizable overlays for different streaming scenarios
  createOverlay(type, config = {}) {
    const overlay = document.createElement('div');
    overlay.className = `stream-overlay overlay-${type}`;
    overlay.id = `overlay-${type}`;

    switch (type) {
      case 'viewer-count':
        overlay.innerHTML = `
          <div class="overlay-content">
            <div class="overlay-icon">👥</div>
            <div class="overlay-text">
              <div class="overlay-number" id="overlay-viewer-number">${this.game.viewerCount}</div>
              <div class="overlay-label">Viewers</div>
            </div>
          </div>
        `;
        break;

      case 'recent-activity':
        overlay.innerHTML = `
          <div class="overlay-content">
            <div class="overlay-header">Recent Activity</div>
            <div class="activity-list" id="activity-list"></div>
          </div>
        `;
        break;

      case 'wheel-result':
        overlay.innerHTML = `
          <div class="overlay-content">
            <div class="overlay-header">🎡 Wheel Result</div>
            <div class="result-text" id="wheel-result-text">Ready to spin!</div>
          </div>
        `;
        break;

      case 'vote-progress':
        overlay.innerHTML = `
          <div class="overlay-content">
            <div class="overlay-header">🗳️ Live Vote</div>
            <div class="vote-question" id="overlay-vote-question">No active vote</div>
            <div class="vote-options" id="overlay-vote-options"></div>
            <div class="vote-timer" id="overlay-vote-timer"></div>
          </div>
        `;
        break;

      case 'stream-stats':
        overlay.innerHTML = `
          <div class="overlay-content">
            <div class="overlay-header">📊 Stream Stats</div>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number" id="stat-spins">${this.streamStats.totalSpins}</div>
                <div class="stat-label">Spins</div>
              </div>
              <div class="stat-item">
                <div class="stat-number" id="stat-votes">${this.streamStats.totalVotes}</div>
                <div class="stat-label">Votes</div>
              </div>
              <div class="stat-item">
                <div class="stat-number" id="stat-messages">${this.streamStats.totalChatMessages}</div>
                <div class="stat-label">Messages</div>
              </div>
            </div>
          </div>
        `;
        break;
    }

    // Apply custom positioning
    if (config.position) {
      overlay.style.position = 'fixed';
      overlay.style.top = config.position.top || 'auto';
      overlay.style.bottom = config.position.bottom || 'auto';
      overlay.style.left = config.position.left || 'auto';
      overlay.style.right = config.position.right || 'auto';
    }

    // Apply custom styling
    if (config.style) {
      Object.assign(overlay.style, config.style);
    }

    document.body.appendChild(overlay);
    this.overlayElements.set(type, overlay);

    return overlay;
  }

  // Update overlay content
  updateOverlay(type, data) {
    const overlay = this.overlayElements.get(type);
    if (!overlay) return;

    switch (type) {
      case 'viewer-count':
        const viewerNumber = overlay.querySelector('#overlay-viewer-number');
        if (viewerNumber) {
          viewerNumber.textContent = data.count || this.game.viewerCount;
          // Add animation for viewer count changes
          viewerNumber.classList.add('count-update');
          setTimeout(() => viewerNumber.classList.remove('count-update'), 500);
        }
        break;

      case 'recent-activity':
        const activityList = overlay.querySelector('#activity-list');
        if (activityList && data.activity) {
          const activityItem = document.createElement('div');
          activityItem.className = 'activity-item';
          activityItem.innerHTML = `
            <span class="activity-time">${new Date().toLocaleTimeString()}</span>
            <span class="activity-text">${data.activity}</span>
          `;
          activityList.insertBefore(activityItem, activityList.firstChild);
          
          // Keep only last 5 activities
          while (activityList.children.length > 5) {
            activityList.removeChild(activityList.lastChild);
          }
        }
        break;

      case 'wheel-result':
        const resultText = overlay.querySelector('#wheel-result-text');
        if (resultText && data.result) {
          resultText.textContent = data.result;
          overlay.classList.add('result-highlight');
          setTimeout(() => overlay.classList.remove('result-highlight'), 3000);
        }
        break;

      case 'vote-progress':
        if (data.question) {
          const questionEl = overlay.querySelector('#overlay-vote-question');
          if (questionEl) questionEl.textContent = data.question;
        }
        if (data.options) {
          const optionsEl = overlay.querySelector('#overlay-vote-options');
          if (optionsEl) {
            optionsEl.innerHTML = data.options.map(option => `
              <div class="overlay-vote-option">
                <div class="option-text">${option.text}</div>
                <div class="option-progress">
                  <div class="progress-bar" style="width: ${option.percentage}%"></div>
                  <div class="progress-text">${option.votes} votes</div>
                </div>
              </div>
            `).join('');
          }
        }
        if (data.timeLeft !== undefined) {
          const timerEl = overlay.querySelector('#overlay-vote-timer');
          if (timerEl) timerEl.textContent = `${data.timeLeft}s remaining`;
        }
        break;

      case 'stream-stats':
        if (data.spins !== undefined) {
          const spinsEl = overlay.querySelector('#stat-spins');
          if (spinsEl) spinsEl.textContent = data.spins;
        }
        if (data.votes !== undefined) {
          const votesEl = overlay.querySelector('#stat-votes');
          if (votesEl) votesEl.textContent = data.votes;
        }
        if (data.messages !== undefined) {
          const messagesEl = overlay.querySelector('#stat-messages');
          if (messagesEl) messagesEl.textContent = data.messages;
        }
        break;
    }
  }

  // Remove overlay
  removeOverlay(type) {
    const overlay = this.overlayElements.get(type);
    if (overlay) {
      overlay.remove();
      this.overlayElements.delete(type);
    }
  }

  // Create alert system for important events
  showAlert(type, message, duration = 5000) {
    this.alertQueue.push({ type, message, duration });
    if (!this.isProcessingAlert) {
      this.processAlertQueue();
    }
  }

  async processAlertQueue() {
    if (this.alertQueue.length === 0) {
      this.isProcessingAlert = false;
      return;
    }

    this.isProcessingAlert = true;
    const alert = this.alertQueue.shift();

    const alertElement = document.createElement('div');
    alertElement.className = `stream-alert alert-${alert.type}`;
    alertElement.innerHTML = `
      <div class="alert-content">
        <div class="alert-icon">${this.getAlertIcon(alert.type)}</div>
        <div class="alert-message">${alert.message}</div>
      </div>
    `;

    alertElement.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(45deg, #ff0050, #00f2ea);
      color: white;
      padding: 2rem 3rem;
      border-radius: 15px;
      font-size: 1.5rem;
      font-weight: 700;
      text-align: center;
      z-index: 10000;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      animation: alertSlideIn 0.5s ease-out;
    `;

    document.body.appendChild(alertElement);

    // Remove alert after duration
    setTimeout(() => {
      alertElement.style.animation = 'alertSlideOut 0.5s ease-in';
      setTimeout(() => {
        alertElement.remove();
        this.processAlertQueue(); // Process next alert
      }, 500);
    }, alert.duration);
  }

  getAlertIcon(type) {
    const icons = {
      'spin': '🎡',
      'vote': '🗳️',
      'gift': '🎁',
      'follow': '❤️',
      'milestone': '🎉',
      'error': '⚠️',
      'success': '✅'
    };
    return icons[type] || '📢';
  }

  // Track stream statistics
  trackEvent(eventType, data = {}) {
    switch (eventType) {
      case 'spin':
        this.streamStats.totalSpins++;
        this.updateOverlay('stream-stats', { spins: this.streamStats.totalSpins });
        this.updateOverlay('recent-activity', { activity: 'Wheel spun!' });
        break;
      case 'vote':
        this.streamStats.totalVotes++;
        this.updateOverlay('stream-stats', { votes: this.streamStats.totalVotes });
        this.updateOverlay('recent-activity', { activity: 'Vote started!' });
        break;
      case 'chat':
        this.streamStats.totalChatMessages++;
        this.updateOverlay('stream-stats', { messages: this.streamStats.totalChatMessages });
        break;
      case 'viewer_update':
        if (data.count > this.streamStats.peakViewers) {
          this.streamStats.peakViewers = data.count;
          this.showAlert('milestone', `New peak: ${data.count} viewers!`, 3000);
        }
        this.updateOverlay('viewer-count', { count: data.count });
        break;
    }
  }

  // Generate stream summary
  getStreamSummary() {
    const duration = this.streamStats.startTime ? 
      Math.floor((Date.now() - this.streamStats.startTime) / 1000 / 60) : 0;
    
    return {
      duration: `${duration} minutes`,
      totalSpins: this.streamStats.totalSpins,
      totalVotes: this.streamStats.totalVotes,
      totalMessages: this.streamStats.totalChatMessages,
      peakViewers: this.streamStats.peakViewers,
      averageEngagement: this.calculateEngagement()
    };
  }

  calculateEngagement() {
    const totalInteractions = this.streamStats.totalSpins + this.streamStats.totalVotes;
    const avgViewers = this.streamStats.peakViewers / 2; // Rough estimate
    return avgViewers > 0 ? Math.round((totalInteractions / avgViewers) * 100) : 0;
  }

  // Start stream session
  startStreamSession() {
    this.streamStats.startTime = Date.now();
    this.showAlert('success', 'Stream session started!', 2000);
  }

  // End stream session
  endStreamSession() {
    const summary = this.getStreamSummary();
    this.showAlert('success', `Stream ended! ${summary.totalSpins} spins, ${summary.totalVotes} votes`, 5000);
    
    // Reset stats for next session
    this.streamStats = {
      startTime: null,
      totalSpins: 0,
      totalVotes: 0,
      totalChatMessages: 0,
      peakViewers: 0
    };
  }

  // Export configuration for OBS
  exportOBSConfig() {
    return {
      browserSources: [
        {
          name: 'TikTok Live Game',
          url: `${window.location.origin}/?mode=browser-source`,
          width: 1920,
          height: 1080,
          fps: 30
        },
        {
          name: 'Game Overlay',
          url: `${window.location.origin}/overlay.html`,
          width: 1920,
          height: 1080,
          fps: 30
        }
      ],
      recommendedSettings: {
        resolution: '1920x1080',
        fps: 30,
        bitrate: '2500-6000 kbps',
        encoder: 'x264 or NVENC'
      }
    };
  }
}

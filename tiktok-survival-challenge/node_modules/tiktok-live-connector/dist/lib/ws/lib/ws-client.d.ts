/// <reference types="node" />
/// <reference types="node" />
import { client as WebSocket, connection as WebSocketConnection, Message as WebSocketMessage } from 'websocket';
import * as http from 'node:http';
import TypedEventEmitter from 'typed-emitter';
import CookieJar from '../../../lib/web/lib/cookie-jar';
type EventMap = {
    connect: (connection: WebSocketConnection) => void;
    close: () => void;
    messageDecodingFailed: (error: Error) => void;
    unknownResponse: (message: WebSocketMessage) => void;
    webcastResponse: (response: any) => void;
};
type TypedWebSocket = WebSocket & TypedEventEmitter<EventMap>;
type WebSocketConstructor = new () => TypedWebSocket;
declare const TikTokWsClient_base: WebSocketConstructor;
export default class TikTokWsClient extends TikTokWsClient_base {
    protected readonly webSocketParams: Record<string, string>;
    protected webSocketPingIntervalMs: number;
    connection: WebSocketConnection | null;
    protected pingInterval: NodeJS.Timeout | null;
    protected wsHeaders: Record<string, string>;
    protected wsUrlWithParams: string;
    constructor(wsUrl: string, cookieJar: CookieJar, webSocketParams: Record<string, string>, webSocketHeaders: Record<string, string>, webSocketOptions: http.RequestOptions, webSocketPingIntervalMs?: number);
    protected onConnect(wsConnection: WebSocketConnection): void;
    protected onDisconnect(): void;
    /**
     * Handle incoming messages
     * @param message The incoming WebSocket message
     * @protected
     */
    protected onMessage(message: WebSocketMessage): Promise<boolean>;
    /**
     * Static Keep-Alive ping
     */
    protected sendPing(): void;
    /**
     * Message Acknowledgement
     * @param id The message id to acknowledge
     */
    protected sendAck(id: string): void;
    /**
     * Close the WebSocket connection
     */
    close(): Promise<void>;
}
export {};
//# sourceMappingURL=ws-client.d.ts.map
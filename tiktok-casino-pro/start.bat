@echo off
echo.
echo ========================================
echo    🎰 TikTok Live Casino PRO 🎰
echo ========================================
echo.
echo Pornesc aplicația...
echo.

REM Verifică dacă Node.js este instalat
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js nu este instalat!
    echo Descarcă și instalează Node.js de la: https://nodejs.org
    pause
    exit /b 1
)

REM Verifică dacă dependințele sunt instalate
if not exist "node_modules" (
    echo 📦 Instalez dependințele...
    npm install
    if errorlevel 1 (
        echo ❌ Eroare la instalarea dependințelor!
        pause
        exit /b 1
    )
)

echo ✅ Dependințele sunt instalate
echo.
echo 🚀 Pornesc serverul de dezvoltare...
echo.
echo 📱 Aplicația va fi disponibilă la:
echo    http://localhost:5173
echo.
echo 💡 Pentru a opri aplicația, apasă Ctrl+C
echo.

REM Pornește serverul
npm run dev

pause

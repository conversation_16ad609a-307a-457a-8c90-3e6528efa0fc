import './style.css'

// TikTok Live Music Battle - Main Application
class TikTokMusicBattle {
  constructor() {
    this.currentSection = 'guess-section';
    this.currentSong = null;
    this.isPlaying = false;
    this.guessTimer = null;
    this.timeLeft = 30;
    this.currentRound = 1;
    this.totalScore = 0;
    this.viewerCount = 0;
    this.statistics = {
      songsPlayed: 0,
      correctGuesses: 0,
      battleWins: 0,
      favoriteGenre: 'Pop'
    };
    this.teams = {
      a: { score: 0, members: [] },
      b: { score: 0, members: [] }
    };
    this.chatMessages = [];
    this.autoPilot = null;

    // Music Database
    this.musicDatabase = {
      pop: [
        { title: "Blinding Lights", artist: "The Weeknd", year: 2019 },
        { title: "Shape of <PERSON>", artist: "<PERSON>", year: 2017 },
        { title: "Anti-Hero", artist: "<PERSON> Swift", year: 2022 },
        { title: "As It Was", artist: "<PERSON>", year: 2022 }
      ],
      rock: [
        { title: "Bohemian Rhapsody", artist: "Queen", year: 1975 },
        { title: "Sweet Child O' Mine", artist: "Guns N' Roses", year: 1987 },
        { title: "Smells Like Teen Spirit", artist: "Nirvana", year: 1991 },
        { title: "Thunderstruck", artist: "AC/DC", year: 1990 }
      ],
      "hip-hop": [
        { title: "God's Plan", artist: "Drake", year: 2018 },
        { title: "HUMBLE.", artist: "Kendrick Lamar", year: 2017 },
        { title: "Sicko Mode", artist: "<PERSON>", year: 2018 },
        { title: "Old Town Road", artist: "Lil Nas X", year: 2019 }
      ],
      electronic: [
        { title: "Titanium", artist: "David Guetta ft. Sia", year: 2011 },
        { title: "Levels", artist: "Avicii", year: 2011 },
        { title: "Clarity", artist: "Zedd ft. Foxes", year: 2012 },
        { title: "Animals", artist: "Martin Garrix", year: 2013 }
      ]
    };

    this.currentGenre = 'pop';
    this.playlist = [];

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateDisplay();
    this.simulateViewerActivity();
    this.generatePlaylist();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Music player controls
    document.getElementById('play-song').addEventListener('click', () => this.playSong());
    document.getElementById('stop-song').addEventListener('click', () => this.stopSong());

    // Guess game
    document.querySelectorAll('.choice-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.makeGuess(e.target.dataset.choice);
      });
    });

    // Battle controls
    document.getElementById('start-battle').addEventListener('click', () => this.startBattle());
    document.getElementById('next-round').addEventListener('click', () => this.nextBattleRound());

    // DJ controls
    document.querySelectorAll('.genre-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.selectGenre(e.target.dataset.genre);
      });
    });
    document.getElementById('auto-dj').addEventListener('click', () => this.startAutoDJ());
    document.getElementById('shuffle-playlist').addEventListener('click', () => this.shufflePlaylist());

    // Chat
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Auto-pilot
    document.getElementById('autopilot-toggle').addEventListener('click', () => this.toggleAutoPilot());
  }

  switchSection(sectionId) {
    document.querySelectorAll('.music-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');

    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  // Music Game Logic
  startGuessGame() {
    this.currentSong = this.getRandomSong();
    this.generateChoices();
    this.timeLeft = 30;
    this.startGuessTimer();

    document.getElementById('song-title').textContent = '🎵 Guess this song!';
    document.getElementById('song-artist').textContent = 'Listen carefully...';

    this.addChatMessage('Music Master', `🎵 New song challenge! Genre: ${this.currentGenre.toUpperCase()}`);
  }

  getRandomSong() {
    const songs = this.musicDatabase[this.currentGenre];
    return songs[Math.floor(Math.random() * songs.length)];
  }

  generateChoices() {
    const correctSong = this.currentSong;
    const wrongSongs = this.musicDatabase[this.currentGenre]
      .filter(song => song !== correctSong)
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);

    const allChoices = [correctSong, ...wrongSongs].sort(() => 0.5 - Math.random());

    const choiceButtons = document.querySelectorAll('.choice-btn');
    choiceButtons.forEach((btn, index) => {
      const song = allChoices[index];
      btn.textContent = `${btn.dataset.choice}. ${song.title} - ${song.artist}`;
      btn.classList.remove('correct', 'incorrect');
      btn.disabled = false;
    });
  }

  playSong() {
    if (this.isPlaying) return;

    this.isPlaying = true;
    document.getElementById('play-song').disabled = true;
    document.getElementById('stop-song').disabled = false;

    // Simulate song playing with progress bar
    let progress = 0;
    const progressBar = document.getElementById('progress');

    const progressInterval = setInterval(() => {
      progress += 2;
      progressBar.style.width = `${progress}%`;

      if (progress >= 100 || !this.isPlaying) {
        clearInterval(progressInterval);
        this.stopSong();
      }
    }, 200);

    this.addChatMessage('Music Master', '🎵 Song is playing! Make your guess!');
  }

  stopSong() {
    this.isPlaying = false;
    document.getElementById('play-song').disabled = false;
    document.getElementById('stop-song').disabled = true;
    document.getElementById('progress').style.width = '0%';
  }

  startGuessTimer() {
    this.guessTimer = setInterval(() => {
      this.timeLeft--;
      document.getElementById('guess-time').textContent = `${this.timeLeft}s`;

      if (this.timeLeft <= 0) {
        this.endGuessRound();
      }
    }, 1000);
  }

  makeGuess(choice) {
    if (!this.currentSong) return;

    const choiceButtons = document.querySelectorAll('.choice-btn');
    const selectedButton = document.querySelector(`[data-choice="${choice}"]`);
    const selectedText = selectedButton.textContent;

    // Check if correct
    const isCorrect = selectedText.includes(this.currentSong.title);

    if (isCorrect) {
      selectedButton.classList.add('correct');
      this.statistics.correctGuesses++;
      this.totalScore += 10;
      this.addChatMessage('Music Master', `🎉 Correct! "${this.currentSong.title}" by ${this.currentSong.artist}!`);
    } else {
      selectedButton.classList.add('incorrect');
      // Show correct answer
      choiceButtons.forEach(btn => {
        if (btn.textContent.includes(this.currentSong.title)) {
          btn.classList.add('correct');
        }
      });
      this.addChatMessage('Music Master', `❌ Wrong! It was "${this.currentSong.title}" by ${this.currentSong.artist}`);
    }

    // Disable all buttons
    choiceButtons.forEach(btn => btn.disabled = true);

    this.statistics.songsPlayed++;
    this.endGuessRound();
  }

  endGuessRound() {
    if (this.guessTimer) {
      clearInterval(this.guessTimer);
      this.guessTimer = null;
    }

    this.stopSong();
    this.updateDisplay();

    // Auto next round after 3 seconds
    setTimeout(() => {
      if (this.autoPilot && this.autoPilot.isActive) {
        this.startGuessGame();
      }
    }, 3000);
  }

  // Battle System
  startBattle() {
    this.addChatMessage('Music Master', '⚔️ Music Battle started! Teams are forming!');
    this.assignViewersToTeams();
    this.updateBattleDisplay();
  }

  assignViewersToTeams() {
    const viewers = ['MusicLover1', 'BeatMaster', 'SongGuru', 'MelodyFan', 'RhythmKing'];
    this.teams.a.members = viewers.slice(0, Math.ceil(viewers.length / 2));
    this.teams.b.members = viewers.slice(Math.ceil(viewers.length / 2));
  }

  updateBattleDisplay() {
    document.getElementById('team-a-score').textContent = this.teams.a.score;
    document.getElementById('team-b-score').textContent = this.teams.b.score;
    document.getElementById('team-a-members').innerHTML = this.teams.a.members.map(m => `<div>${m}</div>`).join('');
    document.getElementById('team-b-members').innerHTML = this.teams.b.members.map(m => `<div>${m}</div>`).join('');
  }

  // DJ System
  selectGenre(genre) {
    this.currentGenre = genre;
    document.querySelectorAll('.genre-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-genre="${genre}"]`).classList.add('active');
    this.generatePlaylist();
    this.addChatMessage('DJ Auto', `🎧 Switched to ${genre.toUpperCase()} music!`);
  }

  generatePlaylist() {
    this.playlist = [...this.musicDatabase[this.currentGenre]];
    this.displayPlaylist();
  }

  displayPlaylist() {
    const playlistDiv = document.getElementById('playlist');
    playlistDiv.innerHTML = this.playlist.map((song, index) =>
      `<div class="playlist-item">${index + 1}. ${song.title} - ${song.artist}</div>`
    ).join('');
  }

  startAutoDJ() {
    this.addChatMessage('DJ Auto', '🎧 Auto DJ activated! Music will play automatically!');
    // Auto DJ logic would go here
  }

  shufflePlaylist() {
    this.playlist.sort(() => 0.5 - Math.random());
    this.displayPlaylist();
    this.addChatMessage('DJ Auto', '🔀 Playlist shuffled!');
  }

  // Chat System
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';
      this.processChatCommand('You', message);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }

    this.chatMessages.push({ username, message, timestamp });
  }

  processChatCommand(username, message) {
    const msg = message.toLowerCase();

    if (msg.includes('!guess') || msg.includes('!song')) {
      this.switchSection('guess-section');
      this.startGuessGame();
    } else if (msg.includes('!battle')) {
      this.switchSection('battle-section');
      this.startBattle();
    } else if (msg.includes('!dj')) {
      this.switchSection('dj-section');
    } else if (msg.includes('!play')) {
      this.playSong();
    }
  }

  // Display Updates
  updateDisplay() {
    document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;
    document.getElementById('current-round').textContent = `Round ${this.currentRound}`;
    document.getElementById('total-score').textContent = `Score: ${this.totalScore}`;

    document.getElementById('songs-played').textContent = this.statistics.songsPlayed;
    document.getElementById('correct-guesses').textContent = this.statistics.correctGuesses;
    document.getElementById('battle-wins').textContent = this.statistics.battleWins;
    document.getElementById('favorite-genre').textContent = this.statistics.favoriteGenre;
  }

  // Viewer Activity Simulation
  simulateViewerActivity() {
    setInterval(() => {
      const change = Math.floor(Math.random() * 20) - 10;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      this.updateDisplay();
    }, 10000);

    setInterval(() => {
      if (Math.random() < 0.3) {
        this.simulateViewerMessage();
      }
    }, 5000);
  }

  simulateViewerMessage() {
    const viewers = ['MusicLover2024', 'BeatMaster', 'SongGuru', 'MelodyFan', 'RhythmKing'];
    const messages = [
      'Love this song!',
      'Play some rock!',
      'I know this one!',
      'Great music choice!',
      'Turn up the volume!',
      'This beat is fire!',
      'Next song please!',
      'Music battle time!',
      'DJ is amazing!',
      'Can\'t stop dancing!'
    ];

    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addChatMessage(randomViewer, randomMessage);
  }

  // Auto-Pilot System
  initializeAutoPilot() {
    this.addChatMessage('Music Master', '🤖 Auto-Pilot available! Click 🤖 to start automated music battles!');
  }

  toggleAutoPilot() {
    // Simple auto-pilot implementation
    if (!this.autoPilot || !this.autoPilot.isActive) {
      this.autoPilot = { isActive: true, username: 'MusicStreamer' };
      this.addChatMessage('Auto-Pilot', '🎵 Music Auto-Pilot activated! Let the music battle begin!');
      document.getElementById('live-indicator').classList.add('active');

      // Start first game
      this.startGuessGame();

      // Auto game rotation
      setInterval(() => {
        if (this.autoPilot && this.autoPilot.isActive) {
          const sections = ['guess-section', 'battle-section', 'dj-section'];
          const randomSection = sections[Math.floor(Math.random() * sections.length)];
          this.switchSection(randomSection);

          if (randomSection === 'guess-section') {
            this.startGuessGame();
          } else if (randomSection === 'battle-section') {
            this.startBattle();
          }
        }
      }, 45000);
    } else {
      this.autoPilot.isActive = false;
      this.addChatMessage('Auto-Pilot', '🎵 Music Auto-Pilot stopped. Thanks for the music!');
      document.getElementById('live-indicator').classList.remove('active');
    }
  }

  // Browser Source Mode
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');
    document.getElementById('autopilot-toggle').style.display = 'none';
  }
}

// Initialize the Music Battle
document.addEventListener('DOMContentLoaded', () => {
  const musicBattle = new TikTokMusicBattle();

  window.TikTokMusicBattle = musicBattle;

  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    musicBattle.enableBrowserSourceMode();
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          musicBattle.switchSection('guess-section');
          break;
        case '2':
          e.preventDefault();
          musicBattle.switchSection('battle-section');
          break;
        case '3':
          e.preventDefault();
          musicBattle.switchSection('dj-section');
          break;
        case ' ':
          e.preventDefault();
          musicBattle.playSong();
          break;
      }
    }
  });
});

import './style.css'

// TikTok Live Quiz Master - Main Application
class TikTokQuizMaster {
  constructor() {
    this.currentSection = 'question-section';
    this.currentQuestion = null;
    this.questionTimer = null;
    this.timeLeft = 30;
    this.isQuestionActive = false;
    this.currentRound = 1;
    this.totalScore = 0;
    this.viewerCount = 0;
    this.leaderboard = new Map();
    this.statistics = {
      totalQuestions: 0,
      correctAnswers: 0,
      totalParticipants: 0,
      averageTime: 0
    };
    this.chatMessages = [];
    this.autoPilot = null;

    // Question Database
    this.questionDatabase = {
      'General Knowledge': [
        {
          question: "Care este capitala României?",
          answers: ["București", "Cluj-Napoca", "Timișoara", "Iași"],
          correct: 0,
          difficulty: "easy"
        },
        {
          question: "În ce an a avut loc Revoluția Română?",
          answers: ["1987", "1989", "1991", "1993"],
          correct: 1,
          difficulty: "medium"
        },
        {
          question: "Care este cel mai înalt munte din România?",
          answers: ["<PERSON>ldo<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ân<PERSON><PERSON> Mare", "<PERSON><PERSON>"],
          correct: 0,
          difficulty: "medium"
        },
        {
          question: "Câte județe are România?",
          answers: ["40", "41", "42", "43"],
          correct: 2,
          difficulty: "easy"
        }
      ],
      'Science': [
        {
          question: "Care este simbolul chimic pentru aur?",
          answers: ["Au", "Ag", "Al", "Ar"],
          correct: 0,
          difficulty: "medium"
        },
        {
          question: "Câte planete sunt în sistemul solar?",
          answers: ["7", "8", "9", "10"],
          correct: 1,
          difficulty: "easy"
        },
        {
          question: "Care este cea mai rapidă particulă din univers?",
          answers: ["Electronul", "Protonul", "Fotonul", "Neutronul"],
          correct: 2,
          difficulty: "hard"
        }
      ],
      'Entertainment': [
        {
          question: "Care este cea mai populară platformă de social media în 2024?",
          answers: ["Facebook", "Instagram", "TikTok", "YouTube"],
          correct: 2,
          difficulty: "easy"
        },
        {
          question: "Cine a câștigat Eurovision 2023?",
          answers: ["Suedia", "Finlanda", "Ucraina", "Italia"],
          correct: 0,
          difficulty: "medium"
        }
      ],
      'Sports': [
        {
          question: "Câte jucătoare sunt pe teren într-o echipă de fotbal?",
          answers: ["10", "11", "12", "9"],
          correct: 1,
          difficulty: "easy"
        },
        {
          question: "În ce sport se folosește termenul 'slam dunk'?",
          answers: ["Tenis", "Volei", "Baschet", "Handbal"],
          correct: 2,
          difficulty: "medium"
        }
      ],
      'Technology': [
        {
          question: "Ce înseamnă 'AI' în tehnologie?",
          answers: ["Artificial Intelligence", "Advanced Internet", "Automatic Interface", "Applied Innovation"],
          correct: 0,
          difficulty: "easy"
        },
        {
          question: "Care companie a creat TikTok?",
          answers: ["Meta", "Google", "ByteDance", "Apple"],
          correct: 2,
          difficulty: "medium"
        }
      ]
    };

    this.categories = Object.keys(this.questionDatabase);
    this.currentCategory = this.categories[0];

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateDisplay();
    this.simulateViewerActivity();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Question controls
    document.getElementById('next-question').addEventListener('click', () => this.nextQuestion());
    document.getElementById('reveal-answer').addEventListener('click', () => this.revealAnswer());
    document.getElementById('skip-question').addEventListener('click', () => this.skipQuestion());

    // Answer buttons
    document.querySelectorAll('.answer-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        if (this.isQuestionActive) {
          this.selectAnswer(e.currentTarget.dataset.answer);
        }
      });
    });

    // Leaderboard controls
    document.getElementById('reset-scores').addEventListener('click', () => this.resetLeaderboard());
    document.getElementById('show-top10').addEventListener('click', () => this.showTop10());

    // Chat controls
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Auto-pilot toggle
    document.getElementById('autopilot-toggle').addEventListener('click', () => this.toggleAutoPilot());
  }

  switchSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.quiz-section').forEach(section => {
      section.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  // Quiz Logic
  nextQuestion() {
    if (this.isQuestionActive) {
      this.endQuestion();
    }

    this.loadRandomQuestion();
    this.startQuestion();
  }

  loadRandomQuestion() {
    const categoryQuestions = this.questionDatabase[this.currentCategory];
    const randomIndex = Math.floor(Math.random() * categoryQuestions.length);
    this.currentQuestion = categoryQuestions[randomIndex];

    // Update UI
    document.getElementById('question-category').textContent = this.currentCategory;
    document.getElementById('question-text').textContent = this.currentQuestion.question;

    // Update answers
    const answerButtons = document.querySelectorAll('.answer-btn');
    answerButtons.forEach((btn, index) => {
      const answerText = btn.querySelector('.answer-text');
      answerText.textContent = this.currentQuestion.answers[index];
      btn.classList.remove('correct', 'incorrect');
      btn.querySelector('.answer-progress').style.width = '0%';
    });

    // Reset timer
    this.timeLeft = 30;
    this.updateTimer();
  }

  startQuestion() {
    this.isQuestionActive = true;
    this.statistics.totalQuestions++;

    // Start timer
    this.questionTimer = setInterval(() => {
      this.timeLeft--;
      this.updateTimer();

      if (this.timeLeft <= 0) {
        this.endQuestion();
      }
    }, 1000);

    // Add chat announcement
    this.addChatMessage('Quiz Master', `🎯 Întrebarea ${this.statistics.totalQuestions}: ${this.currentQuestion.question}`);

    // Simulate viewer participation
    this.simulateViewerAnswers();
  }

  endQuestion() {
    this.isQuestionActive = false;
    if (this.questionTimer) {
      clearInterval(this.questionTimer);
      this.questionTimer = null;
    }

    this.revealAnswer();
    this.updateStatistics();
  }

  revealAnswer() {
    const answerButtons = document.querySelectorAll('.answer-btn');
    const correctIndex = this.currentQuestion.correct;

    answerButtons.forEach((btn, index) => {
      if (index === correctIndex) {
        btn.classList.add('correct');
      } else {
        btn.classList.add('incorrect');
      }
    });

    // Add chat message with answer
    const correctAnswer = this.currentQuestion.answers[correctIndex];
    this.addChatMessage('Quiz Master', `✅ Răspunsul corect: ${correctAnswer}`);

    // Auto next question after 5 seconds
    setTimeout(() => {
      if (this.autoPilot && this.autoPilot.isActive) {
        this.nextQuestion();
      }
    }, 5000);
  }

  skipQuestion() {
    this.endQuestion();
    setTimeout(() => this.nextQuestion(), 1000);
  }

  selectAnswer(answerLetter) {
    const answerIndex = ['A', 'B', 'C', 'D'].indexOf(answerLetter);
    const isCorrect = answerIndex === this.currentQuestion.correct;

    if (isCorrect) {
      this.statistics.correctAnswers++;
      this.addChatMessage('Quiz Master', `🎉 Răspuns corect! Felicitări!`);
    } else {
      this.addChatMessage('Quiz Master', `❌ Răspuns greșit. Încearcă din nou!`);
    }
  }

  updateTimer() {
    const timerElement = document.getElementById('question-timer');
    timerElement.textContent = `${this.timeLeft}s`;

    if (this.timeLeft <= 10) {
      timerElement.style.background = '#ff4757';
      timerElement.style.animation = 'pulse 0.5s infinite';
    } else {
      timerElement.style.background = '#ffa502';
      timerElement.style.animation = 'pulse 1s infinite';
    }
  }

  // Leaderboard Management
  updateLeaderboard(username, points) {
    if (this.leaderboard.has(username)) {
      this.leaderboard.set(username, this.leaderboard.get(username) + points);
    } else {
      this.leaderboard.set(username, points);
    }

    this.displayLeaderboard();
  }

  displayLeaderboard() {
    const leaderboardList = document.getElementById('leaderboard-list');
    const sortedEntries = Array.from(this.leaderboard.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    leaderboardList.innerHTML = sortedEntries.map((entry, index) => `
      <div class="leaderboard-entry">
        <div class="leaderboard-rank">${index + 1}</div>
        <div class="leaderboard-name">${entry[0]}</div>
        <div class="leaderboard-score">${entry[1]} pts</div>
      </div>
    `).join('');
  }

  resetLeaderboard() {
    this.leaderboard.clear();
    this.displayLeaderboard();
    this.addChatMessage('Quiz Master', '🔄 Clasamentul a fost resetat!');
  }

  showTop10() {
    this.switchSection('leaderboard-section');
    this.addChatMessage('Quiz Master', '🏆 Iată top 10 jucători!');
  }

  // Statistics
  updateStatistics() {
    const participationRate = this.statistics.totalQuestions > 0 ?
      Math.round((this.statistics.correctAnswers / this.statistics.totalQuestions) * 100) : 0;

    document.getElementById('total-questions').textContent = this.statistics.totalQuestions;
    document.getElementById('correct-answers').textContent = this.statistics.correctAnswers;
    document.getElementById('participation-rate').textContent = `${participationRate}%`;
    document.getElementById('average-time').textContent = `${30 - this.timeLeft}s`;
  }

  // Chat System
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';

      // Process chat commands
      this.processChatCommand('You', message);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    // Keep only last 50 messages
    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }

    this.chatMessages.push({ username, message, timestamp });
  }

  processChatCommand(username, message) {
    const msg = message.toLowerCase();

    if (msg.includes('!quiz') || msg.includes('!start')) {
      this.nextQuestion();
      this.addChatMessage('Quiz Master', `${username} a pornit o nouă întrebare!`);
    } else if (msg.includes('!skip')) {
      this.skipQuestion();
      this.addChatMessage('Quiz Master', `${username} a sărit întrebarea!`);
    } else if (msg.includes('!leaderboard') || msg.includes('!top')) {
      this.showTop10();
    } else if (msg.includes('!stats')) {
      this.switchSection('stats-section');
    }

    // Answer detection
    if (this.isQuestionActive) {
      const answerMatch = msg.match(/^[abcd]$/);
      if (answerMatch) {
        const answerIndex = answerMatch[0].charCodeAt(0) - 97; // a=0, b=1, c=2, d=3
        const isCorrect = answerIndex === this.currentQuestion.correct;

        if (isCorrect) {
          this.updateLeaderboard(username, 10);
          this.addChatMessage('Quiz Master', `🎉 ${username} a răspuns corect! +10 puncte!`);
        }
      }
    }
  }

  // Viewer Activity Simulation
  simulateViewerActivity() {
    // Update viewer count
    setInterval(() => {
      const change = Math.floor(Math.random() * 20) - 10;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;
    }, 10000);

    // Random chat messages
    setInterval(() => {
      if (Math.random() < 0.3) {
        this.simulateViewerMessage();
      }
    }, 5000);
  }

  simulateViewerMessage() {
    const viewers = ['QuizMaster2024', 'BrainiacRO', 'TriviaKing', 'SmartViewer', 'QuizLover', 'GeniusPlayer'];
    const messages = [
      'Grea întrebarea asta!',
      'Știu răspunsul!',
      'Quiz tare!',
      'Următoarea întrebare!',
      'Bravo pentru quiz!',
      'Îmi place jocul!',
      'Când e următoarea?',
      'Super distractiv!',
      'Vreau să particip!',
      'Greu de răspuns!'
    ];

    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addChatMessage(randomViewer, randomMessage);
  }

  simulateViewerAnswers() {
    if (!this.isQuestionActive) return;

    // Simulate viewers answering
    const answerButtons = document.querySelectorAll('.answer-btn');
    const totalViewers = this.viewerCount;

    setTimeout(() => {
      answerButtons.forEach((btn, index) => {
        const percentage = Math.random() * 100;
        const participationRate = Math.random() * 0.3 + 0.1; // 10-40% participation
        const votes = Math.floor(totalViewers * participationRate * (percentage / 100));

        btn.querySelector('.answer-progress').style.width = `${percentage}%`;

        // Add some viewers to leaderboard
        if (index === this.currentQuestion.correct && Math.random() < 0.3) {
          const viewers = ['QuizMaster2024', 'BrainiacRO', 'TriviaKing', 'SmartViewer'];
          const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
          this.updateLeaderboard(randomViewer, 10);
        }
      });
    }, 2000);
  }

  // Display Updates
  updateDisplay() {
    document.getElementById('current-round').textContent = `Round ${this.currentRound}`;
    document.getElementById('total-score').textContent = `Score: ${this.totalScore}`;
    this.updateStatistics();
    this.displayLeaderboard();

    // Initialize with first question
    this.loadRandomQuestion();
  }

  // Auto-Pilot Integration
  initializeAutoPilot() {
    // Auto-pilot will be implemented separately
    this.addChatMessage('Quiz Master', '🤖 Auto-Pilot disponibil! Apasă butonul 🤖 pentru a configura.');
  }

  toggleAutoPilot() {
    // This will open the auto-pilot configuration panel
    this.createAutoPilotPanel();
  }

  createAutoPilotPanel() {
    // Remove existing panel if any
    const existingPanel = document.getElementById('autopilot-panel');
    if (existingPanel) {
      existingPanel.remove();
      return;
    }

    const panel = document.createElement('div');
    panel.id = 'autopilot-panel';
    panel.className = 'autopilot-panel';
    panel.innerHTML = `
      <div class="autopilot-header">
        <h3>🤖 Quiz Auto-Pilot</h3>
        <button id="close-autopilot" class="close-btn">✕</button>
      </div>

      <div class="autopilot-content">
        <div class="setup-section">
          <h4>Configurare Streamer</h4>
          <div class="input-group">
            <label>Username TikTok:</label>
            <input type="text" id="streamer-username" placeholder="Username-ul tău" />
          </div>
          <div class="input-group">
            <label>Categoria Quiz:</label>
            <select id="quiz-category">
              ${this.categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
            </select>
          </div>
        </div>

        <div class="settings-section">
          <h4>Setări Automatizare</h4>
          <div class="setting-item">
            <label>Interval întrebări (secunde):</label>
            <input type="range" id="question-interval" min="20" max="120" value="45">
            <span id="question-interval-value">45s</span>
          </div>
          <div class="setting-item">
            <label>Timp răspuns (secunde):</label>
            <input type="range" id="answer-time" min="15" max="60" value="30">
            <span id="answer-time-value">30s</span>
          </div>
          <div class="setting-item">
            <label>Nivel dificultate:</label>
            <select id="difficulty-level">
              <option value="easy">Ușor</option>
              <option value="medium">Mediu</option>
              <option value="hard">Greu</option>
              <option value="mixed">Mixt</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Răspunsuri automate:</label>
            <input type="checkbox" id="auto-responses" checked>
            <span>Răspunde automat la chat</span>
          </div>
        </div>

        <div class="control-section">
          <button id="start-autopilot" class="start-btn">🚀 Pornește Auto-Pilot</button>
          <button id="stop-autopilot" class="stop-btn" disabled>⏹️ Oprește Auto-Pilot</button>
        </div>

        <div class="status-section">
          <div class="status-indicator" id="autopilot-status">
            <span class="status-dot offline"></span>
            <span class="status-text">Offline</span>
          </div>
          <div class="stats-mini">
            <span>Întrebări: <strong id="auto-questions">0</strong></span>
            <span>Participanți: <strong id="auto-participants">0</strong></span>
          </div>
        </div>
      </div>
    `;

    panel.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
      border: 2px solid #ff6b35;
      border-radius: 15px;
      padding: 2rem;
      z-index: 2000;
      width: 450px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(255, 107, 53, 0.3);
      color: white;
      font-family: 'Inter', sans-serif;
    `;

    document.body.appendChild(panel);
    this.setupAutoPilotControls();
  }

  setupAutoPilotControls() {
    // Close button
    document.getElementById('close-autopilot').addEventListener('click', () => {
      document.getElementById('autopilot-panel').remove();
    });

    // Range inputs
    document.getElementById('question-interval').addEventListener('input', (e) => {
      document.getElementById('question-interval-value').textContent = e.target.value + 's';
    });

    document.getElementById('answer-time').addEventListener('input', (e) => {
      document.getElementById('answer-time-value').textContent = e.target.value + 's';
    });

    // Start Auto-Pilot
    document.getElementById('start-autopilot').addEventListener('click', () => {
      const username = document.getElementById('streamer-username').value.trim();
      if (!username) {
        alert('Te rog introdu username-ul tău!');
        return;
      }

      this.startAutoPilot(username);
    });

    // Stop Auto-Pilot
    document.getElementById('stop-autopilot').addEventListener('click', () => {
      this.stopAutoPilot();
    });
  }

  startAutoPilot(username) {
    this.autoPilot = {
      isActive: true,
      username: username,
      questionInterval: parseInt(document.getElementById('question-interval').value) * 1000,
      answerTime: parseInt(document.getElementById('answer-time').value),
      category: document.getElementById('quiz-category').value,
      autoResponses: document.getElementById('auto-responses').checked,
      timer: null
    };

    // Update UI
    document.getElementById('autopilot-status').innerHTML = `
      <span class="status-dot online"></span>
      <span class="status-text">Online & Active</span>
    `;
    document.getElementById('start-autopilot').disabled = true;
    document.getElementById('stop-autopilot').disabled = false;

    // Start automatic quiz
    this.addChatMessage('Auto-Pilot', `🤖 Auto-Pilot activat! Bun venit la quiz-ul live al lui ${username}!`);
    this.currentCategory = this.autoPilot.category;

    // Start first question immediately
    this.nextQuestion();

    // Set up automatic question cycle
    this.autoPilot.timer = setInterval(() => {
      if (this.autoPilot.isActive) {
        this.nextQuestion();
      }
    }, this.autoPilot.questionInterval);

    // Auto responses
    if (this.autoPilot.autoResponses) {
      this.startAutoResponses();
    }

    // Show live indicator
    document.getElementById('live-indicator').classList.add('active');
  }

  stopAutoPilot() {
    if (this.autoPilot) {
      this.autoPilot.isActive = false;
      if (this.autoPilot.timer) {
        clearInterval(this.autoPilot.timer);
      }
    }

    // Update UI
    document.getElementById('autopilot-status').innerHTML = `
      <span class="status-dot offline"></span>
      <span class="status-text">Offline</span>
    `;
    document.getElementById('start-autopilot').disabled = false;
    document.getElementById('stop-autopilot').disabled = true;

    this.addChatMessage('Auto-Pilot', '🤖 Auto-Pilot oprit. Mulțumim pentru participare!');
    document.getElementById('live-indicator').classList.remove('active');
  }

  startAutoResponses() {
    const responses = [
      'Excelentă întrebare! 🎯',
      'Bravo pentru participare! 👏',
      'Continuați să răspundeți! 💪',
      'Quiz-ul devine din ce în ce mai interesant! 🔥',
      'Îmi place energia voastră! ⚡',
      'Să vedem cine știe răspunsul! 🤔',
      'Timpul trece repede! ⏰',
      'Felicitări pentru răspunsurile corecte! 🎉'
    ];

    setInterval(() => {
      if (this.autoPilot && this.autoPilot.isActive && Math.random() < 0.4) {
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        this.addChatMessage(this.autoPilot.username, randomResponse);
      }
    }, 8000);
  }

  // Browser Source Mode
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');
    document.getElementById('autopilot-toggle').style.display = 'none';
  }
}

// Initialize the Quiz Master
document.addEventListener('DOMContentLoaded', () => {
  const quizMaster = new TikTokQuizMaster();

  // Make globally available
  window.TikTokQuizMaster = quizMaster;

  // Check for browser source mode
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    quizMaster.enableBrowserSourceMode();
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          quizMaster.switchSection('question-section');
          break;
        case '2':
          e.preventDefault();
          quizMaster.switchSection('leaderboard-section');
          break;
        case '3':
          e.preventDefault();
          quizMaster.switchSection('stats-section');
          break;
        case '4':
          e.preventDefault();
          quizMaster.switchSection('chat-section');
          break;
        case ' ':
          e.preventDefault();
          quizMaster.nextQuestion();
          break;
      }
    }
  });
});

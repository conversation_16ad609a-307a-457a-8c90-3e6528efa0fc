import { Route } from '../../../types/route';
export type SendRoomChatRouteParams = {
    content: string;
    roomId?: string;
};
export type SendRoomChatRouteResponse = any;
export declare class SendRoomChatRoute extends Route<SendRoomChatRouteParams, SendRoomChatRouteResponse> {
    call({ roomId, content }: {
        roomId: any;
        content: any;
    }): Promise<Record<string, any>>;
}
//# sourceMappingURL=send-room-chat.d.ts.map
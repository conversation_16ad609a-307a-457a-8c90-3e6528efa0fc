<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🎵 TikTok Live Music Battle</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <div id="music-container">
        <header class="music-header">
          <h1>🎵 TikTok Live Music Battle</h1>
          <div class="music-status">
            <span id="viewer-count">0 viewers</span>
            <span id="current-round">Round 1</span>
            <span id="total-score">Score: 0</span>
          </div>
        </header>

        <button id="autopilot-toggle" class="autopilot-toggle">🤖</button>

        <main class="music-main">
          <section id="guess-section" class="music-section active">
            <div class="guess-container">
              <div class="music-player">
                <div class="now-playing">
                  <div class="song-info">
                    <div id="song-title">🎵 Get ready for music battle!</div>
                    <div id="song-artist">Press play to start</div>
                  </div>
                  <div class="player-controls">
                    <button id="play-song" class="play-btn">▶️ Play Song</button>
                    <button id="stop-song" class="stop-btn">⏹️ Stop</button>
                    <div class="progress-bar">
                      <div id="progress" class="progress"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="guess-options">
                <h3>Guess the Song!</h3>
                <div id="song-choices" class="choices-grid">
                  <button class="choice-btn" data-choice="A">A. Song Option 1</button>
                  <button class="choice-btn" data-choice="B">B. Song Option 2</button>
                  <button class="choice-btn" data-choice="C">C. Song Option 3</button>
                  <button class="choice-btn" data-choice="D">D. Song Option 4</button>
                </div>
                <div class="guess-timer">
                  <span id="guess-time">30s</span> remaining
                </div>
              </div>
            </div>
          </section>

          <section id="battle-section" class="music-section">
            <div class="battle-container">
              <h2>🎤 Music Battle Arena</h2>
              <div class="battle-teams">
                <div class="team team-a">
                  <h3>Team A</h3>
                  <div class="team-score" id="team-a-score">0</div>
                  <div class="team-members" id="team-a-members"></div>
                </div>
                <div class="vs-indicator">VS</div>
                <div class="team team-b">
                  <h3>Team B</h3>
                  <div class="team-score" id="team-b-score">0</div>
                  <div class="team-members" id="team-b-members"></div>
                </div>
              </div>
              <div class="battle-controls">
                <button id="start-battle" class="battle-btn">🎵 Start Battle</button>
                <button id="next-round" class="battle-btn">➡️ Next Round</button>
              </div>
            </div>
          </section>

          <section id="dj-section" class="music-section">
            <div class="dj-container">
              <h2>🎧 DJ Auto-Mix</h2>
              <div class="dj-deck">
                <div class="genre-selector">
                  <h3>Select Genre</h3>
                  <div class="genre-buttons">
                    <button class="genre-btn active" data-genre="pop">🎤 Pop</button>
                    <button class="genre-btn" data-genre="rock">🎸 Rock</button>
                    <button class="genre-btn" data-genre="hip-hop">🎤 Hip-Hop</button>
                    <button class="genre-btn" data-genre="electronic">🎛️ Electronic</button>
                  </div>
                </div>
                <div class="playlist-display">
                  <h3>Current Playlist</h3>
                  <div id="playlist" class="playlist"></div>
                </div>
                <div class="dj-controls">
                  <button id="auto-dj" class="dj-btn">🎧 Auto DJ</button>
                  <button id="shuffle-playlist" class="dj-btn">🔀 Shuffle</button>
                </div>
              </div>
            </div>
          </section>

          <section id="stats-section" class="music-section">
            <div class="stats-container">
              <h2>📊 Music Stats</h2>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-number" id="songs-played">0</div>
                  <div class="stat-label">Songs Played</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number" id="correct-guesses">0</div>
                  <div class="stat-label">Correct Guesses</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number" id="battle-wins">0</div>
                  <div class="stat-label">Battle Wins</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number" id="favorite-genre">Pop</div>
                  <div class="stat-label">Favorite Genre</div>
                </div>
              </div>
            </div>
          </section>

          <section id="chat-section" class="music-section">
            <div class="chat-container">
              <h2>💬 Music Chat</h2>
              <div id="chat-display" class="chat-display"></div>
              <div class="chat-controls">
                <input type="text" id="chat-input" placeholder="Chat about music...">
                <button id="send-chat" class="control-btn">Send</button>
              </div>
            </div>
          </section>
        </main>

        <nav class="music-nav">
          <button class="nav-btn active" data-section="guess-section">🎵 Guess</button>
          <button class="nav-btn" data-section="battle-section">⚔️ Battle</button>
          <button class="nav-btn" data-section="dj-section">🎧 DJ</button>
          <button class="nav-btn" data-section="stats-section">📊 Stats</button>
          <button class="nav-btn" data-section="chat-section">💬 Chat</button>
        </nav>

        <div id="live-indicator" class="live-indicator">LIVE MUSIC</div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

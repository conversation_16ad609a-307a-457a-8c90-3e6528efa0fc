<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🏆 TikTok Live Quiz Master</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <!-- Quiz Game Container -->
      <div id="quiz-container">
        <!-- Header -->
        <header class="quiz-header">
          <h1>🏆 TikTok Live Quiz Master</h1>
          <div class="quiz-status">
            <span id="viewer-count">0 viewers</span>
            <span id="current-round">Round 1</span>
            <span id="total-score">Score: 0</span>
          </div>
        </header>

        <!-- Auto-Pilot Control Button -->
        <button id="autopilot-toggle" class="autopilot-toggle">🤖</button>

        <!-- Main Quiz Area -->
        <main class="quiz-main">
          <!-- Question Display -->
          <section id="question-section" class="quiz-section active">
            <div class="question-container">
              <div class="question-header">
                <span id="question-category" class="category-badge">General Knowledge</span>
                <span id="question-timer" class="timer">30s</span>
              </div>
              <div id="question-text" class="question-text">
                Welcome to TikTok Live Quiz Master! Get ready for an exciting trivia experience!
              </div>
              <div id="question-image" class="question-image" style="display: none;">
                <img src="" alt="Question Image" />
              </div>
            </div>

            <div class="answers-container">
              <div id="answers-grid" class="answers-grid">
                <button class="answer-btn" data-answer="A">
                  <span class="answer-letter">A</span>
                  <span class="answer-text">Answer A</span>
                  <div class="answer-progress"></div>
                </button>
                <button class="answer-btn" data-answer="B">
                  <span class="answer-letter">B</span>
                  <span class="answer-text">Answer B</span>
                  <div class="answer-progress"></div>
                </button>
                <button class="answer-btn" data-answer="C">
                  <span class="answer-letter">C</span>
                  <span class="answer-text">Answer C</span>
                  <div class="answer-progress"></div>
                </button>
                <button class="answer-btn" data-answer="D">
                  <span class="answer-letter">D</span>
                  <span class="answer-text">Answer D</span>
                  <div class="answer-progress"></div>
                </button>
              </div>
            </div>

            <div class="question-controls">
              <button id="next-question" class="control-btn">Next Question</button>
              <button id="reveal-answer" class="control-btn">Reveal Answer</button>
              <button id="skip-question" class="control-btn">Skip Question</button>
            </div>
          </section>

          <!-- Leaderboard Section -->
          <section id="leaderboard-section" class="quiz-section">
            <div class="leaderboard-container">
              <h2>🏆 Live Leaderboard</h2>
              <div id="leaderboard-list" class="leaderboard-list">
                <!-- Leaderboard entries will be populated here -->
              </div>
              <div class="leaderboard-controls">
                <button id="reset-scores" class="control-btn">Reset Scores</button>
                <button id="show-top10" class="control-btn">Show Top 10</button>
              </div>
            </div>
          </section>

          <!-- Statistics Section -->
          <section id="stats-section" class="quiz-section">
            <div class="stats-container">
              <h2>📊 Quiz Statistics</h2>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-number" id="total-questions">0</div>
                  <div class="stat-label">Questions Asked</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number" id="correct-answers">0</div>
                  <div class="stat-label">Correct Answers</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number" id="participation-rate">0%</div>
                  <div class="stat-label">Participation Rate</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number" id="average-time">0s</div>
                  <div class="stat-label">Average Time</div>
                </div>
              </div>
            </div>
          </section>

          <!-- Chat Integration -->
          <section id="chat-section" class="quiz-section">
            <div class="chat-container">
              <h2>💬 Live Chat</h2>
              <div id="chat-display" class="chat-display"></div>
              <div class="chat-controls">
                <input type="text" id="chat-input" placeholder="Simulate chat message...">
                <button id="send-chat" class="control-btn">Send</button>
              </div>
            </div>
          </section>
        </main>

        <!-- Navigation -->
        <nav class="quiz-nav">
          <button class="nav-btn active" data-section="question-section">❓ Quiz</button>
          <button class="nav-btn" data-section="leaderboard-section">🏆 Leaderboard</button>
          <button class="nav-btn" data-section="stats-section">📊 Stats</button>
          <button class="nav-btn" data-section="chat-section">💬 Chat</button>
        </nav>

        <!-- Live Indicator -->
        <div id="live-indicator" class="live-indicator">LIVE</div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

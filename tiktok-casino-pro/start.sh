#!/bin/bash

echo ""
echo "========================================"
echo "   🎰 TikTok Live Casino PRO 🎰"
echo "========================================"
echo ""
echo "Pornesc aplicația..."
echo ""

# Verifică dacă Node.js este instalat
if ! command -v node &> /dev/null; then
    echo "❌ Node.js nu este instalat!"
    echo "Instalează Node.js de la: https://nodejs.org"
    exit 1
fi

# Verifică dacă dependințele sunt instalate
if [ ! -d "node_modules" ]; then
    echo "📦 Instalez dependințele..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Eroare la instalarea dependințelor!"
        exit 1
    fi
fi

echo "✅ Dependințele sunt instalate"
echo ""
echo "🚀 Pornesc serverul de dezvoltare..."
echo ""
echo "📱 Aplicația va fi disponibilă la:"
echo "   http://localhost:5173"
echo ""
echo "💡 Pentru a opri aplicația, apasă Ctrl+C"
echo ""

# Pornește serverul
npm run dev

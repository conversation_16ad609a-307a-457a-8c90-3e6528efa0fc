{"name": "requizzle", "version": "0.2.3", "description": "Swizzle a little something into your require() calls.", "main": "index.js", "scripts": {"test": "gulp test"}, "repository": {"type": "git", "url": "git://github.com/hegemonic/requizzle.git"}, "keywords": ["module", "modules", "require", "inject", "dependency", "swizzle"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/hegemonic/requizzle/issues"}, "homepage": "https://github.com/hegemonic/requizzle", "dependencies": {"lodash": "^4.17.14"}, "devDependencies": {"expectations": "^1.0.0", "gulp": "^4.0.2", "gulp-eslint": "^6.0.0", "gulp-mocha": "^6.0.0"}}
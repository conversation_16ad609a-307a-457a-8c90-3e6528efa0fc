import './style.css'
import { TikTokLiveConnector, TikTokUtils } from './tiktok-live-connector.js'

// TikTok Live Casino - Main Application cu integrare reală
class TikTokLiveCasino {
  constructor() {
    this.currentSection = 'roulette-section';
    this.playerChips = 1000;
    this.totalGames = 0;
    this.totalWinnings = 0;
    this.biggestWin = 0;
    this.wins = 0;
    this.viewerCount = 0;
    this.leaderboard = new Map();
    this.chatMessages = [];
    this.autoPilot = null;

    // Integrare TikTok Live reală
    this.tiktokConnector = new TikTokLiveConnector(this);
    this.isLiveConnected = false;
    this.currentStreamer = '';

    // Sistem de jucători și credite
    this.players = new Map();
    this.creditSystem = {
      likeCredits: 1,        // 1 credit per like
      followCredits: 100,    // 100 credite pentru follow
      shareCredits: 50,      // 50 credite pentru share
      joinCredits: 10,       // 10 credite pentru join
      giftMultiplier: 10     // Multiplicator pentru cadouri
    };

    // Game States
    this.roulette = {
      isSpinning: false,
      currentBets: new Map(),
      lastNumber: null,
      wheel: null
    };

    this.blackjack = {
      isPlaying: false,
      dealerCards: [],
      playerCards: [],
      deck: [],
      currentBet: 0
    };

    this.slots = {
      isSpinning: false,
      symbols: ['🍒', '🍋', '🍊', '🍇', '🍎', '💎', '⭐', '🔔'],
      payouts: {
        '🍒🍒🍒': 100,
        '🍋🍋🍋': 50,
        '🍊🍊🍊': 25,
        '🍇🍇🍇': 75,
        '🍎🍎🍎': 40,
        '💎💎💎': 500,
        '⭐⭐⭐': 1000,
        '🔔🔔🔔': 200
      }
    };

    this.dice = {
      isRolling: false,
      currentBets: new Map(),
      lastRoll: [1, 1]
    };

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.initializeRouletteWheel();
    this.generateRouletteNumbers();
    this.updateDisplay();
    this.simulateViewerActivity();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Roulette
    document.getElementById('spin-roulette').addEventListener('click', () => this.spinRoulette());
    document.getElementById('clear-bets').addEventListener('click', () => this.clearRouletteBets());
    document.getElementById('max-bet').addEventListener('click', () => this.maxBet());

    // Blackjack
    document.getElementById('deal-cards').addEventListener('click', () => this.dealBlackjack());
    document.getElementById('hit-card').addEventListener('click', () => this.hitBlackjack());
    document.getElementById('stand-cards').addEventListener('click', () => this.standBlackjack());
    document.getElementById('double-down').addEventListener('click', () => this.doubleDownBlackjack());

    // Slots
    document.getElementById('spin-slots').addEventListener('click', () => this.spinSlots());

    // Dice
    document.getElementById('roll-dice').addEventListener('click', () => this.rollDice());

    // Chat
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Auto-pilot
    document.getElementById('autopilot-toggle').addEventListener('click', () => this.toggleAutoPilot());
  }

  switchSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.casino-section').forEach(section => {
      section.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  // Roulette Game Logic
  initializeRouletteWheel() {
    const canvas = document.getElementById('roulette-wheel');
    const ctx = canvas.getContext('2d');
    this.roulette.wheel = { canvas, ctx };
    this.drawRouletteWheel();
  }

  drawRouletteWheel() {
    const { canvas, ctx } = this.roulette.wheel;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 180;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Roulette numbers and colors
    const numbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];

    const anglePerNumber = (2 * Math.PI) / numbers.length;

    numbers.forEach((number, index) => {
      const startAngle = index * anglePerNumber;
      const endAngle = (index + 1) * anglePerNumber;

      // Determine color
      let color = '#228b22'; // Green for 0
      if (number !== 0) {
        color = redNumbers.includes(number) ? '#dc143c' : '#000000';
      }

      // Draw segment
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fill();

      // Draw border
      ctx.strokeStyle = '#ffd700';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw number
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(startAngle + anglePerNumber / 2);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Inter';
      ctx.textAlign = 'center';
      ctx.fillText(number.toString(), radius * 0.8, 5);
      ctx.restore();
    });

    // Draw center
    ctx.fillStyle = '#ffd700';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI);
    ctx.fill();
  }

  generateRouletteNumbers() {
    const numberGrid = document.querySelector('.number-grid');
    const numbers = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36];
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];

    numberGrid.innerHTML = '';

    numbers.forEach(number => {
      const btn = document.createElement('button');
      btn.className = 'number-btn';
      btn.textContent = number;
      btn.dataset.number = number;

      if (number === 0) {
        btn.classList.add('zero');
      } else if (redNumbers.includes(number)) {
        btn.classList.add('red');
      } else {
        btn.classList.add('black');
      }

      btn.addEventListener('click', () => this.placeBet('number', number));
      numberGrid.appendChild(btn);
    });

    // Setup outside bets
    document.querySelectorAll('.bet-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const betType = e.target.dataset.bet;
        this.placeBet('outside', betType);
      });
    });
  }

  placeBet(type, value) {
    const betAmount = parseInt(document.getElementById('bet-amount').value) || 10;

    if (betAmount > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    const betKey = `${type}-${value}`;
    const currentBet = this.roulette.currentBets.get(betKey) || 0;
    this.roulette.currentBets.set(betKey, currentBet + betAmount);
    this.playerChips -= betAmount;

    this.updateDisplay();
    this.addChatMessage('Casino', `Bet placed: ${betAmount} chips on ${value}`);
  }

  spinRoulette() {
    if (this.roulette.isSpinning || this.roulette.currentBets.size === 0) return;

    this.roulette.isSpinning = true;
    document.getElementById('spin-roulette').disabled = true;

    // Animate wheel
    const ball = document.getElementById('roulette-ball');
    ball.style.animation = 'none';
    setTimeout(() => {
      ball.style.animation = 'spin 3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }, 10);

    // Generate winning number
    setTimeout(() => {
      const numbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];
      const winningNumber = numbers[Math.floor(Math.random() * numbers.length)];
      this.roulette.lastNumber = winningNumber;

      this.processRouletteWin(winningNumber);
      this.roulette.isSpinning = false;
      document.getElementById('spin-roulette').disabled = false;
    }, 3000);
  }

  processRouletteWin(winningNumber) {
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
    let totalWin = 0;

    this.roulette.currentBets.forEach((betAmount, betKey) => {
      const [type, value] = betKey.split('-');
      let isWin = false;
      let payout = 0;

      if (type === 'number' && parseInt(value) === winningNumber) {
        isWin = true;
        payout = betAmount * 35; // 35:1 payout
      } else if (type === 'outside') {
        switch (value) {
          case 'red':
            isWin = redNumbers.includes(winningNumber);
            payout = betAmount * 2;
            break;
          case 'black':
            isWin = !redNumbers.includes(winningNumber) && winningNumber !== 0;
            payout = betAmount * 2;
            break;
          case 'even':
            isWin = winningNumber % 2 === 0 && winningNumber !== 0;
            payout = betAmount * 2;
            break;
          case 'odd':
            isWin = winningNumber % 2 === 1;
            payout = betAmount * 2;
            break;
          case 'low':
            isWin = winningNumber >= 1 && winningNumber <= 18;
            payout = betAmount * 2;
            break;
          case 'high':
            isWin = winningNumber >= 19 && winningNumber <= 36;
            payout = betAmount * 2;
            break;
        }
      }

      if (isWin) {
        totalWin += payout;
      }
    });

    this.playerChips += totalWin;
    this.totalGames++;

    if (totalWin > 0) {
      this.wins++;
      this.totalWinnings += totalWin;
      if (totalWin > this.biggestWin) {
        this.biggestWin = totalWin;
      }
      this.addChatMessage('Casino', `🎉 Winner! Number ${winningNumber}! You won ${totalWin} chips!`);
    } else {
      this.addChatMessage('Casino', `Number ${winningNumber}. Better luck next time!`);
    }

    this.roulette.currentBets.clear();
    this.updateDisplay();
  }

  clearRouletteBets() {
    // Return chips
    let totalReturn = 0;
    this.roulette.currentBets.forEach(amount => {
      totalReturn += amount;
    });
    this.playerChips += totalReturn;
    this.roulette.currentBets.clear();
    this.updateDisplay();
    this.addChatMessage('Casino', 'All bets cleared!');
  }

  maxBet() {
    document.getElementById('bet-amount').value = Math.min(100, this.playerChips);
  }

  // Blackjack Game Logic
  createDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const deck = [];

    suits.forEach(suit => {
      ranks.forEach(rank => {
        deck.push({
          suit,
          rank,
          value: rank === 'A' ? 11 : (rank === 'J' || rank === 'Q' || rank === 'K') ? 10 : parseInt(rank)
        });
      });
    });

    // Shuffle deck
    for (let i = deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [deck[i], deck[j]] = [deck[j], deck[i]];
    }

    return deck;
  }

  dealBlackjack() {
    const bet = parseInt(document.getElementById('blackjack-bet').value) || 10;

    if (bet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    this.blackjack.currentBet = bet;
    this.playerChips -= bet;
    this.blackjack.isPlaying = true;
    this.blackjack.deck = this.createDeck();
    this.blackjack.dealerCards = [];
    this.blackjack.playerCards = [];

    // Deal initial cards
    this.blackjack.playerCards.push(this.blackjack.deck.pop());
    this.blackjack.dealerCards.push(this.blackjack.deck.pop());
    this.blackjack.playerCards.push(this.blackjack.deck.pop());
    this.blackjack.dealerCards.push(this.blackjack.deck.pop());

    this.updateBlackjackDisplay();
    this.updateBlackjackControls();

    // Check for blackjack
    if (this.calculateBlackjackScore(this.blackjack.playerCards) === 21) {
      this.endBlackjack('blackjack');
    }
  }

  hitBlackjack() {
    if (!this.blackjack.isPlaying) return;

    this.blackjack.playerCards.push(this.blackjack.deck.pop());
    this.updateBlackjackDisplay();

    const playerScore = this.calculateBlackjackScore(this.blackjack.playerCards);
    if (playerScore > 21) {
      this.endBlackjack('bust');
    } else if (playerScore === 21) {
      this.standBlackjack();
    }
  }

  standBlackjack() {
    if (!this.blackjack.isPlaying) return;

    // Dealer plays
    while (this.calculateBlackjackScore(this.blackjack.dealerCards) < 17) {
      this.blackjack.dealerCards.push(this.blackjack.deck.pop());
    }

    this.updateBlackjackDisplay();

    const playerScore = this.calculateBlackjackScore(this.blackjack.playerCards);
    const dealerScore = this.calculateBlackjackScore(this.blackjack.dealerCards);

    if (dealerScore > 21) {
      this.endBlackjack('dealer-bust');
    } else if (playerScore > dealerScore) {
      this.endBlackjack('win');
    } else if (playerScore < dealerScore) {
      this.endBlackjack('lose');
    } else {
      this.endBlackjack('push');
    }
  }

  doubleDownBlackjack() {
    if (!this.blackjack.isPlaying || this.blackjack.playerCards.length !== 2) return;

    if (this.blackjack.currentBet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips to double down!');
      return;
    }

    this.playerChips -= this.blackjack.currentBet;
    this.blackjack.currentBet *= 2;

    this.hitBlackjack();
    if (this.blackjack.isPlaying) {
      this.standBlackjack();
    }
  }

  calculateBlackjackScore(cards) {
    let score = 0;
    let aces = 0;

    cards.forEach(card => {
      if (card.rank === 'A') {
        aces++;
        score += 11;
      } else {
        score += card.value;
      }
    });

    while (score > 21 && aces > 0) {
      score -= 10;
      aces--;
    }

    return score;
  }

  updateBlackjackDisplay() {
    const dealerArea = document.getElementById('dealer-cards');
    const playerArea = document.getElementById('player-cards');

    // Show dealer cards (hide first card if game is active)
    dealerArea.innerHTML = '';
    this.blackjack.dealerCards.forEach((card, index) => {
      const cardDiv = document.createElement('div');
      cardDiv.className = 'card';

      if (this.blackjack.isPlaying && index === 0) {
        cardDiv.classList.add('back');
        cardDiv.textContent = '?';
      } else {
        if (card.suit === '♥' || card.suit === '♦') {
          cardDiv.classList.add('red');
        }
        cardDiv.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
      }

      dealerArea.appendChild(cardDiv);
    });

    // Show player cards
    playerArea.innerHTML = '';
    this.blackjack.playerCards.forEach(card => {
      const cardDiv = document.createElement('div');
      cardDiv.className = 'card';

      if (card.suit === '♥' || card.suit === '♦') {
        cardDiv.classList.add('red');
      }
      cardDiv.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
      playerArea.appendChild(cardDiv);
    });

    // Update scores
    const dealerScore = this.blackjack.isPlaying ? '?' : this.calculateBlackjackScore(this.blackjack.dealerCards);
    const playerScore = this.calculateBlackjackScore(this.blackjack.playerCards);

    document.getElementById('dealer-score').textContent = `Score: ${dealerScore}`;
    document.getElementById('player-score').textContent = `Score: ${playerScore}`;
  }

  updateBlackjackControls() {
    const isPlaying = this.blackjack.isPlaying;
    const canDoubleDown = isPlaying && this.blackjack.playerCards.length === 2;

    document.getElementById('deal-cards').disabled = isPlaying;
    document.getElementById('hit-card').disabled = !isPlaying;
    document.getElementById('stand-cards').disabled = !isPlaying;
    document.getElementById('double-down').disabled = !canDoubleDown;
  }

  endBlackjack(result) {
    this.blackjack.isPlaying = false;
    this.totalGames++;

    let winAmount = 0;
    let message = '';

    switch (result) {
      case 'blackjack':
        winAmount = Math.floor(this.blackjack.currentBet * 2.5);
        message = '🎉 Blackjack! You win!';
        this.wins++;
        break;
      case 'win':
      case 'dealer-bust':
        winAmount = this.blackjack.currentBet * 2;
        message = '🎉 You win!';
        this.wins++;
        break;
      case 'push':
        winAmount = this.blackjack.currentBet;
        message = 'Push! Bet returned.';
        break;
      case 'lose':
      case 'bust':
        winAmount = 0;
        message = 'You lose!';
        break;
    }

    this.playerChips += winAmount;
    this.totalWinnings += winAmount - this.blackjack.currentBet;

    if (winAmount > this.biggestWin) {
      this.biggestWin = winAmount;
    }

    document.getElementById('blackjack-result').textContent = message;
    this.addChatMessage('Casino', message);
    this.updateBlackjackDisplay();
    this.updateBlackjackControls();
    this.updateDisplay();
  }

  // Slots Game Logic
  spinSlots() {
    if (this.slots.isSpinning) return;

    const bet = parseInt(document.getElementById('slots-bet').value) || 10;

    if (bet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    this.slots.isSpinning = true;
    this.playerChips -= bet;
    document.getElementById('spin-slots').disabled = true;

    // Animate reels
    const reels = document.querySelectorAll('.reel');
    reels.forEach(reel => {
      reel.classList.add('spinning');
    });

    // Generate results
    setTimeout(() => {
      const results = [];
      reels.forEach((reel, index) => {
        const randomSymbol = this.slots.symbols[Math.floor(Math.random() * this.slots.symbols.length)];
        results.push(randomSymbol);

        reel.innerHTML = `<div class="symbol">${randomSymbol}</div>`;
        reel.classList.remove('spinning');
      });

      this.processSlotsWin(results, bet);
      this.slots.isSpinning = false;
      document.getElementById('spin-slots').disabled = false;
    }, 2000);
  }

  processSlotsWin(results, bet) {
    const combination = results.join('');
    const payout = this.slots.payouts[combination];

    this.totalGames++;

    if (payout) {
      const winAmount = bet * payout;
      this.playerChips += winAmount;
      this.wins++;
      this.totalWinnings += winAmount - bet;

      if (winAmount > this.biggestWin) {
        this.biggestWin = winAmount;
      }

      this.addChatMessage('Casino', `🎰 JACKPOT! ${combination} - You won ${winAmount} chips!`);

      // Special effects for big wins
      if (payout >= 500) {
        document.querySelector('.slot-machine').classList.add('jackpot-animation');
        setTimeout(() => {
          document.querySelector('.slot-machine').classList.remove('jackpot-animation');
        }, 1500);
      }
    } else {
      // Check for partial matches
      const uniqueSymbols = [...new Set(results)];
      if (uniqueSymbols.length === 2) {
        const smallWin = Math.floor(bet * 0.5);
        this.playerChips += smallWin;
        this.addChatMessage('Casino', `Two matching symbols! Small win: ${smallWin} chips`);
      } else {
        this.addChatMessage('Casino', `${combination} - Try again!`);
      }
    }

    this.updateDisplay();
  }

  // Dice Game Logic
  rollDice() {
    if (this.dice.isRolling) return;

    const bet = parseInt(document.getElementById('dice-bet').value) || 10;

    if (bet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    this.dice.isRolling = true;
    this.playerChips -= bet;
    document.getElementById('roll-dice').disabled = true;

    // Animate dice
    const dice1 = document.getElementById('dice1');
    const dice2 = document.getElementById('dice2');

    dice1.classList.add('rolling');
    dice2.classList.add('rolling');

    // Generate results
    setTimeout(() => {
      const roll1 = Math.floor(Math.random() * 6) + 1;
      const roll2 = Math.floor(Math.random() * 6) + 1;
      const total = roll1 + roll2;

      this.dice.lastRoll = [roll1, roll2];

      // Update dice display
      const diceSymbols = ['⚀', '⚁', '⚂', '⚃', '⚄', '⚅'];
      dice1.textContent = diceSymbols[roll1 - 1];
      dice2.textContent = diceSymbols[roll2 - 1];
      document.getElementById('dice-total').textContent = total;

      dice1.classList.remove('rolling');
      dice2.classList.remove('rolling');

      this.processDiceWin(total, roll1, roll2, bet);
      this.dice.isRolling = false;
      document.getElementById('roll-dice').disabled = false;
    }, 1000);
  }

  processDiceWin(total, roll1, roll2, bet) {
    this.totalGames++;
    let winAmount = 0;
    let message = `Rolled ${roll1} and ${roll2} (Total: ${total})`;

    // Check active bets (simulate player bets)
    const betTypes = ['under7', 'seven', 'over7', 'doubles'];
    const activeBet = betTypes[Math.floor(Math.random() * betTypes.length)];

    switch (activeBet) {
      case 'under7':
        if (total < 7) {
          winAmount = bet * 2;
          message += ' - Under 7 wins!';
          this.wins++;
        } else {
          message += ' - Under 7 loses!';
        }
        break;
      case 'seven':
        if (total === 7) {
          winAmount = bet * 5;
          message += ' - Lucky Seven!';
          this.wins++;
        } else {
          message += ' - Seven loses!';
        }
        break;
      case 'over7':
        if (total > 7) {
          winAmount = bet * 2;
          message += ' - Over 7 wins!';
          this.wins++;
        } else {
          message += ' - Over 7 loses!';
        }
        break;
      case 'doubles':
        if (roll1 === roll2) {
          winAmount = bet * 6;
          message += ' - Doubles win!';
          this.wins++;
        } else {
          message += ' - Doubles lose!';
        }
        break;
    }

    this.playerChips += winAmount;
    this.totalWinnings += winAmount - bet;

    if (winAmount > this.biggestWin) {
      this.biggestWin = winAmount;
    }

    this.addChatMessage('Casino', message);
    this.updateDisplay();
  }

  // Chat System
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';

      // Process chat commands
      this.processChatCommand('You', message);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    // Keep only last 50 messages
    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }

    this.chatMessages.push({ username, message, timestamp });
  }

  processChatCommand(username, message) {
    const msg = message.toLowerCase();

    if (msg.includes('!spin') || msg.includes('!roulette')) {
      this.switchSection('roulette-section');
      if (!this.roulette.isSpinning) {
        setTimeout(() => this.spinRoulette(), 1000);
      }
    } else if (msg.includes('!blackjack') || msg.includes('!cards')) {
      this.switchSection('blackjack-section');
      if (!this.blackjack.isPlaying) {
        setTimeout(() => this.dealBlackjack(), 1000);
      }
    } else if (msg.includes('!slots') || msg.includes('!slot')) {
      this.switchSection('slots-section');
      if (!this.slots.isSpinning) {
        setTimeout(() => this.spinSlots(), 1000);
      }
    } else if (msg.includes('!dice') || msg.includes('!roll')) {
      this.switchSection('dice-section');
      if (!this.dice.isRolling) {
        setTimeout(() => this.rollDice(), 1000);
      }
    } else if (msg.includes('!stats')) {
      this.switchSection('stats-section');
    } else if (msg.includes('!chips')) {
      this.addChatMessage('Casino', `You have ${this.playerChips} chips`);
    }
  }

  // Display Updates
  updateDisplay() {
    document.getElementById('total-chips').textContent = `💰 ${this.playerChips} chips`;
    document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;

    // Update statistics
    document.getElementById('total-games').textContent = this.totalGames;
    document.getElementById('total-winnings').textContent = this.totalWinnings;
    document.getElementById('biggest-win').textContent = this.biggestWin;

    const winRate = this.totalGames > 0 ? Math.round((this.wins / this.totalGames) * 100) : 0;
    document.getElementById('win-rate').textContent = `${winRate}%`;

    this.updateLeaderboard();
  }

  updateLeaderboard() {
    // Add current player to leaderboard
    this.leaderboard.set('You', this.playerChips);

    const leaderboardList = document.getElementById('casino-leaderboard');
    const sortedEntries = Array.from(this.leaderboard.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    leaderboardList.innerHTML = sortedEntries.map((entry, index) => `
      <div class="leaderboard-entry">
        <div class="leaderboard-rank">${index + 1}</div>
        <div class="leaderboard-name">${entry[0]}</div>
        <div class="leaderboard-chips">${entry[1]} chips</div>
      </div>
    `).join('');
  }

  // Viewer Activity Simulation
  simulateViewerActivity() {
    // Update viewer count
    setInterval(() => {
      const change = Math.floor(Math.random() * 20) - 10;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      this.updateDisplay();
    }, 10000);

    // Random chat messages
    setInterval(() => {
      if (Math.random() < 0.4) {
        this.simulateViewerMessage();
      }
    }, 5000);

    // Add random players to leaderboard
    setInterval(() => {
      if (Math.random() < 0.3) {
        const names = ['HighRoller', 'LuckyPlayer', 'CasinoKing', 'SlotMaster', 'CardShark', 'DiceRoller'];
        const randomName = names[Math.floor(Math.random() * names.length)] + Math.floor(Math.random() * 100);
        const randomChips = Math.floor(Math.random() * 2000) + 500;
        this.leaderboard.set(randomName, randomChips);
      }
    }, 15000);
  }

  simulateViewerMessage() {
    const viewers = ['HighRoller2024', 'LuckyGambler', 'CasinoFan', 'SlotLover', 'CardMaster', 'DiceKing'];
    const messages = [
      'Spin that roulette!',
      'Hit me in blackjack!',
      'Lucky slots tonight!',
      'Roll those dice!',
      'Big win incoming!',
      'Casino night!',
      'Feeling lucky!',
      'Let\'s gamble!',
      'All in!',
      'House always wins... or does it?'
    ];

    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addChatMessage(randomViewer, randomMessage);
  }

  // Auto-Pilot System
  initializeAutoPilot() {
    this.addChatMessage('Casino', '🤖 Auto-Pilot available! Click 🤖 to configure automated casino games.');
  }

  toggleAutoPilot() {
    this.createAutoPilotPanel();
  }

  createAutoPilotPanel() {
    // Remove existing panel if any
    const existingPanel = document.getElementById('autopilot-panel');
    if (existingPanel) {
      existingPanel.remove();
      return;
    }

    const panel = document.createElement('div');
    panel.id = 'autopilot-panel';
    panel.innerHTML = `
      <div class="autopilot-header">
        <h3>🎰 Casino Auto-Pilot</h3>
        <button id="close-autopilot" class="close-btn">✕</button>
      </div>

      <div class="autopilot-content">
        <div class="setup-section">
          <h4>Streamer Configuration</h4>
          <div class="input-group">
            <label>TikTok Username:</label>
            <input type="text" id="streamer-username" placeholder="Your username" />
          </div>
          <div class="input-group">
            <label>Starting Chips:</label>
            <input type="number" id="starting-chips" value="1000" min="500" max="10000" />
          </div>
        </div>

        <div class="settings-section">
          <h4>Game Settings</h4>
          <div class="setting-item">
            <label>Game Rotation (seconds):</label>
            <input type="range" id="game-interval" min="30" max="180" value="60">
            <span id="game-interval-value">60s</span>
          </div>
          <div class="setting-item">
            <label>Auto-bet Amount:</label>
            <input type="range" id="auto-bet" min="5" max="50" value="10">
            <span id="auto-bet-value">10 chips</span>
          </div>
          <div class="setting-item">
            <label>Risk Level:</label>
            <select id="risk-level">
              <option value="low">Conservative</option>
              <option value="medium" selected>Balanced</option>
              <option value="high">High Roller</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Chat Responses:</label>
            <input type="checkbox" id="auto-chat" checked>
            <span>Auto-respond to viewers</span>
          </div>
        </div>

        <div class="control-section">
          <button id="start-autopilot" class="start-btn">🚀 Start Casino Auto-Pilot</button>
          <button id="stop-autopilot" class="stop-btn" disabled>⏹️ Stop Auto-Pilot</button>
        </div>

        <div class="status-section">
          <div class="status-indicator" id="autopilot-status">
            <span class="status-dot offline"></span>
            <span class="status-text">Offline</span>
          </div>
        </div>
      </div>
    `;

    panel.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #1a0f1a, #2a1a2a);
      border: 2px solid #ffd700;
      border-radius: 15px;
      padding: 2rem;
      z-index: 2000;
      width: 450px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
      color: white;
      font-family: 'Inter', sans-serif;
    `;

    document.body.appendChild(panel);
    this.setupAutoPilotControls();
  }

  setupAutoPilotControls() {
    // Close button
    document.getElementById('close-autopilot').addEventListener('click', () => {
      document.getElementById('autopilot-panel').remove();
    });

    // Range inputs
    document.getElementById('game-interval').addEventListener('input', (e) => {
      document.getElementById('game-interval-value').textContent = e.target.value + 's';
    });

    document.getElementById('auto-bet').addEventListener('input', (e) => {
      document.getElementById('auto-bet-value').textContent = e.target.value + ' chips';
    });

    // Start Auto-Pilot
    document.getElementById('start-autopilot').addEventListener('click', () => {
      const username = document.getElementById('streamer-username').value.trim();
      if (!username) {
        alert('Please enter your TikTok username!');
        return;
      }

      this.startAutoPilot(username);
    });

    // Stop Auto-Pilot
    document.getElementById('stop-autopilot').addEventListener('click', () => {
      this.stopAutoPilot();
    });
  }

  startAutoPilot(username) {
    this.autoPilot = {
      isActive: true,
      username: username,
      gameInterval: parseInt(document.getElementById('game-interval').value) * 1000,
      autoBet: parseInt(document.getElementById('auto-bet').value),
      riskLevel: document.getElementById('risk-level').value,
      autoChat: document.getElementById('auto-chat').checked,
      timer: null,
      currentGame: 0
    };

    // Reset chips if needed
    const startingChips = parseInt(document.getElementById('starting-chips').value);
    this.playerChips = startingChips;

    // Update UI
    document.getElementById('autopilot-status').innerHTML = `
      <span class="status-dot online"></span>
      <span class="status-text">Online & Active</span>
    `;
    document.getElementById('start-autopilot').disabled = true;
    document.getElementById('stop-autopilot').disabled = false;

    // Start automatic casino games
    this.addChatMessage('Auto-Pilot', `🎰 Casino Auto-Pilot activated! Welcome to ${username}'s live casino!`);

    // Start first game immediately
    this.autoPlayGame();

    // Set up automatic game rotation
    this.autoPilot.timer = setInterval(() => {
      if (this.autoPilot.isActive) {
        this.autoPlayGame();
      }
    }, this.autoPilot.gameInterval);

    // Auto responses
    if (this.autoPilot.autoChat) {
      this.startAutoResponses();
    }

    // Show live indicator
    document.getElementById('live-indicator').classList.add('active');
  }

  stopAutoPilot() {
    if (this.autoPilot) {
      this.autoPilot.isActive = false;
      if (this.autoPilot.timer) {
        clearInterval(this.autoPilot.timer);
      }
    }

    // Update UI
    document.getElementById('autopilot-status').innerHTML = `
      <span class="status-dot offline"></span>
      <span class="status-text">Offline</span>
    `;
    document.getElementById('start-autopilot').disabled = false;
    document.getElementById('stop-autopilot').disabled = true;

    this.addChatMessage('Auto-Pilot', '🎰 Casino Auto-Pilot stopped. Thanks for playing!');
    document.getElementById('live-indicator').classList.remove('active');
  }

  autoPlayGame() {
    const games = ['roulette', 'blackjack', 'slots', 'dice'];
    const currentGame = games[this.autoPilot.currentGame % games.length];
    this.autoPilot.currentGame++;

    // Switch to the game section
    this.switchSection(`${currentGame}-section`);

    // Announce the game
    this.addChatMessage(this.autoPilot.username, `🎮 Time for ${currentGame.toUpperCase()}! Let's see what luck brings us!`);

    // Play the game after a short delay
    setTimeout(() => {
      switch (currentGame) {
        case 'roulette':
          this.autoPlayRoulette();
          break;
        case 'blackjack':
          this.autoPlayBlackjack();
          break;
        case 'slots':
          this.autoPlaySlots();
          break;
        case 'dice':
          this.autoPlayDice();
          break;
      }
    }, 2000);
  }

  autoPlayRoulette() {
    // Place random bets based on risk level
    const betAmount = this.autoPilot.autoBet;
    document.getElementById('bet-amount').value = betAmount;

    const riskLevel = this.autoPilot.riskLevel;
    if (riskLevel === 'low') {
      // Conservative bets (outside bets)
      const outsideBets = ['red', 'black', 'even', 'odd'];
      const randomBet = outsideBets[Math.floor(Math.random() * outsideBets.length)];
      this.placeBet('outside', randomBet);
    } else if (riskLevel === 'high') {
      // Risky bets (single numbers)
      const randomNumber = Math.floor(Math.random() * 37);
      this.placeBet('number', randomNumber);
    } else {
      // Balanced approach
      if (Math.random() < 0.7) {
        const outsideBets = ['red', 'black', 'even', 'odd'];
        const randomBet = outsideBets[Math.floor(Math.random() * outsideBets.length)];
        this.placeBet('outside', randomBet);
      } else {
        const randomNumber = Math.floor(Math.random() * 37);
        this.placeBet('number', randomNumber);
      }
    }

    setTimeout(() => {
      this.spinRoulette();
    }, 1000);
  }

  autoPlayBlackjack() {
    document.getElementById('blackjack-bet').value = this.autoPilot.autoBet;
    this.dealBlackjack();
  }

  autoPlaySlots() {
    document.getElementById('slots-bet').value = this.autoPilot.autoBet;
    this.spinSlots();
  }

  autoPlayDice() {
    document.getElementById('dice-bet').value = this.autoPilot.autoBet;
    this.rollDice();
  }

  startAutoResponses() {
    const responses = [
      'The house edge is real, but so is the thrill! 🎰',
      'Feeling lucky tonight! 🍀',
      'Every spin could be THE spin! ✨',
      'Casino night is the best night! 🌟',
      'Let\'s see what the cards have in store! 🃏',
      'Rolling the dice of destiny! 🎲',
      'Fortune favors the bold! 💪',
      'The excitement never stops here! 🔥'
    ];

    setInterval(() => {
      if (this.autoPilot && this.autoPilot.isActive && Math.random() < 0.4) {
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        this.addChatMessage(this.autoPilot.username, randomResponse);
      }
    }, 8000);
  }

  // Browser Source Mode
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');
    document.getElementById('autopilot-toggle').style.display = 'none';
  }
}

// Initialize the Casino
document.addEventListener('DOMContentLoaded', () => {
  const casino = new TikTokLiveCasino();

  // Make globally available
  window.TikTokLiveCasino = casino;

  // Check for browser source mode
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    casino.enableBrowserSourceMode();
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          casino.switchSection('roulette-section');
          break;
        case '2':
          e.preventDefault();
          casino.switchSection('blackjack-section');
          break;
        case '3':
          e.preventDefault();
          casino.switchSection('slots-section');
          break;
        case '4':
          e.preventDefault();
          casino.switchSection('dice-section');
          break;
        case '5':
          e.preventDefault();
          casino.switchSection('stats-section');
          break;
        case '6':
          e.preventDefault();
          casino.switchSection('chat-section');
          break;
      }
    }
  });

  // ===== FUNCȚII TIKTOK LIVE =====

  // Conectare la TikTok Live
  casino.connectToTikTokLive = async function() {
    const usernameInput = document.getElementById('tiktok-username');
    if (!usernameInput) {
      this.addChatMessage('❌ ERROR', 'Nu găsesc câmpul pentru username!');
      return;
    }

    const username = usernameInput.value.trim();
    console.log(`🔍 Încerc conectarea cu username: "${username}"`);

    const validation = TikTokUtils.validateUsername(username);

    if (!validation.valid) {
      this.addChatMessage('❌ ERROR', validation.error);
      console.error('❌ Validare username eșuată:', validation.error);
      return;
    }

    console.log(`✅ Username valid: "${validation.username}"`);
    this.addChatMessage('🔄 CONNECTING', `Conectez la @${validation.username}...`);

    try {
      const connected = await this.tiktokConnector.connectToLive(validation.username);
      if (connected) {
        this.isLiveConnected = true;
        this.currentStreamer = validation.username;
        this.updateConnectionStatus(true);
        console.log('✅ Conectare reușită!');
      } else {
        console.log('❌ Conectare eșuată');
        // Activează modul demo pentru testare
        this.activateDemoMode(validation.username);
      }
    } catch (error) {
      console.error('❌ Eroare conectare:', error);
      this.addChatMessage('❌ ERROR', `Eroare: ${error.message}`);
      // Activează modul demo pentru testare
      this.activateDemoMode(validation.username);
    }
  };

  // Activează modul demo pentru testare
  casino.activateDemoMode = function(username) {
    console.log('🎮 Activez modul DEMO pentru testare...');
    this.addChatMessage('🎮 DEMO MODE', `Modul demo activat pentru @${username}`);
    this.addChatMessage('ℹ️ INFO', 'Poți testa jocurile local! Scrie comenzi în chat.');
    this.addChatMessage('🎯 COMENZI', '!spin, !blackjack, !slots, !dice, !balance, !help');

    // Simulează conexiunea
    this.isLiveConnected = true;
    this.currentStreamer = username;
    this.updateConnectionStatus(true);

    // Dă credite demo
    const demoPlayer = this.getOrCreatePlayer('DEMO_PLAYER');
    demoPlayer.credits = 1000;

    this.addChatMessage('💰 DEMO', 'DEMO_PLAYER a primit 1000 credite pentru testare!');

    // Simulează câțiva jucători demo
    setTimeout(() => {
      this.simulateDemoActivity();
    }, 2000);
  };

  // Simulează activitate demo
  casino.simulateDemoActivity = function() {
    const demoUsers = ['TestUser1', 'TestUser2', 'GamerPro', 'LuckyPlayer'];

    demoUsers.forEach((user, index) => {
      setTimeout(() => {
        const player = this.getOrCreatePlayer(user);
        player.credits = 500 + Math.floor(Math.random() * 500);
        this.addChatMessage('🎉 JOIN', `${user} s-a alăturat demo-ului!`);
        this.addChatMessage('💰 BONUS', `${user} a primit ${player.credits} credite demo!`);
      }, index * 1000);
    });

    // Simulează comenzi automate
    setTimeout(() => {
      this.addChatMessage('ℹ️ DEMO', 'Încearcă comenzile: !spin, !blackjack, !slots, !dice');
      this.addChatMessage('💡 TIP', 'Scrie "!spin" pentru a juca la ruletă!');
    }, 5000);
  };

  // Deconectare de la TikTok Live
  casino.disconnectFromTikTokLive = function() {
    if (this.tiktokConnector) {
      this.tiktokConnector.disconnect();
      this.isLiveConnected = false;
      this.currentStreamer = '';
      this.updateConnectionStatus(false);
      this.addChatMessage('🔌 DISCONNECTED', 'Deconectat de la TikTok Live');
    }
  };

  // Actualizează statusul conexiunii
  casino.updateConnectionStatus = function(connected) {
    const indicator = document.getElementById('live-indicator');
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');

    if (indicator) {
      indicator.textContent = connected ? '🔴 LIVE' : '⚫ OFFLINE';
      indicator.className = connected ? 'live-indicator live' : 'live-indicator offline';
    }

    if (connectBtn) connectBtn.style.display = connected ? 'none' : 'block';
    if (disconnectBtn) disconnectBtn.style.display = connected ? 'block' : 'none';
  };

  // Event handlers pentru TikTok Live
  casino.onTikTokConnected = function(username, state) {
    this.addChatMessage('✅ CONNECTED', `Conectat la @${username}!`);
    this.addChatMessage('🎰 CASINO', 'Casino-ul este acum LIVE! Jucătorii pot începe să joace!');
    this.viewerCount = state.roomInfo?.viewerCount || 0;
    this.updateDisplay();
  };

  casino.onTikTokDisconnected = function() {
    this.addChatMessage('❌ DISCONNECTED', 'Conexiunea TikTok Live s-a închis');
    this.isLiveConnected = false;
    this.updateConnectionStatus(false);
  };

  casino.onTikTokError = function(error) {
    this.addChatMessage('❌ ERROR', `TikTok Live: ${error}`);
  };

  // Gestionează comentariile din TikTok Live
  casino.handleTikTokComment = function(username, message) {
    // Afișează comentariul
    this.addChatMessage(username, message);

    // Procesează comenzile de joc
    const msg = message.toLowerCase();

    if (msg.includes('!spin') || msg.includes('!roulette')) {
      this.handleGameCommand(username, 'roulette');
    } else if (msg.includes('!blackjack') || msg.includes('!cards')) {
      this.handleGameCommand(username, 'blackjack');
    } else if (msg.includes('!slots') || msg.includes('!slot')) {
      this.handleGameCommand(username, 'slots');
    } else if (msg.includes('!dice') || msg.includes('!zaruri')) {
      this.handleGameCommand(username, 'dice');
    } else if (msg.includes('!balance') || msg.includes('!credite')) {
      this.showPlayerBalance(username);
    } else if (msg.includes('!help') || msg.includes('!comenzi')) {
      this.showGameCommands(username);
    }
  };

  // Gestionează like-urile
  casino.handleTikTokLikes = function(username, likeCount) {
    const player = this.getOrCreatePlayer(username);
    const creditsEarned = likeCount * this.creditSystem.likeCredits;

    player.credits += creditsEarned;
    this.addChatMessage('❤️ LIKES', `${username} +${creditsEarned} credite pentru ${likeCount} like-uri!`);
  };

  // Gestionează cadourile
  casino.handleTikTokGift = function(username, giftName, giftId, repeatCount, diamondCount) {
    const player = this.getOrCreatePlayer(username);
    const creditsEarned = TikTokUtils.calculateGiftCredits(giftName, diamondCount, repeatCount);

    player.credits += creditsEarned;
    player.totalGifts += repeatCount;

    this.addChatMessage('🎁 GIFT', `${username} a trimis ${repeatCount}x ${giftName}!`);
    this.addChatMessage('💰 BONUS', `${username} +${creditsEarned} credite!`);

    // Joacă automat un joc pentru cadouri mari
    if (creditsEarned >= 100) {
      setTimeout(() => {
        this.handleGameCommand(username, 'roulette', creditsEarned);
      }, 2000);
    }
  };

  // Gestionează urmăritorii noi
  casino.handleTikTokFollow = function(username) {
    const player = this.getOrCreatePlayer(username);
    player.credits += this.creditSystem.followCredits;

    this.addChatMessage('👥 FOLLOW', `${username} te urmărește acum!`);
    this.addChatMessage('🎉 BONUS', `${username} +${this.creditSystem.followCredits} credite bonus!`);
  };

  // Gestionează share-urile
  casino.handleTikTokShare = function(username) {
    const player = this.getOrCreatePlayer(username);
    player.credits += this.creditSystem.shareCredits;

    this.addChatMessage('📤 SHARE', `${username} a distribuit live-ul!`);
    this.addChatMessage('💰 BONUS', `${username} +${this.creditSystem.shareCredits} credite!`);
  };

  // Gestionează join-urile
  casino.handleTikTokJoin = function(username) {
    const player = this.getOrCreatePlayer(username);
    player.credits += this.creditSystem.joinCredits;

    this.addChatMessage('🎉 JOIN', `Bun venit ${username}! +${this.creditSystem.joinCredits} credite!`);
  };

  // Obține sau creează jucător
  casino.getOrCreatePlayer = function(username) {
    if (!this.players.has(username)) {
      this.players.set(username, {
        username,
        credits: 0,
        totalGames: 0,
        totalWins: 0,
        totalGifts: 0,
        joinedAt: Date.now()
      });
    }
    return this.players.get(username);
  };

  // Afișează balanța jucătorului
  casino.showPlayerBalance = function(username) {
    const player = this.getOrCreatePlayer(username);
    this.addChatMessage('💰 BALANCE',
      `${username}: ${player.credits} credite | ` +
      `${player.totalGames} jocuri | ` +
      `${player.totalWins} câștiguri`
    );
  };

  // Afișează comenzile disponibile
  casino.showGameCommands = function(username) {
    this.addChatMessage('ℹ️ COMENZI', `${username}, comenzi disponibile:`);
    this.addChatMessage('🎮 JOCURI', '!spin, !blackjack, !slots, !dice');
    this.addChatMessage('💰 INFO', '!balance, !help');
    this.addChatMessage('🎁 BONUS', 'Trimite cadouri, like-uri, follow pentru credite!');
  };

  // Gestionează comenzile de joc
  casino.handleGameCommand = function(username, gameType, betAmount = null) {
    const player = this.getOrCreatePlayer(username);

    if (player.credits <= 0) {
      this.addChatMessage('💸 FĂRĂ CREDITE', `${username}, nu ai credite! Trimite like-uri sau cadouri pentru credite!`);
      return;
    }

    // Calculează pariul
    const bet = betAmount || Math.min(player.credits, 50); // Pariu implicit 50 sau toate creditele

    if (bet > player.credits) {
      this.addChatMessage('💸 PARIU PREA MARE', `${username}, ai doar ${player.credits} credite!`);
      return;
    }

    // Scade creditele pentru pariu
    player.credits -= bet;
    player.totalGames++;

    this.addChatMessage('🎮 JOC', `${username} joacă ${gameType} cu ${bet} credite!`);

    // Joacă jocul specific
    switch (gameType) {
      case 'roulette':
        this.playRouletteForPlayer(username, bet);
        break;
      case 'blackjack':
        this.playBlackjackForPlayer(username, bet);
        break;
      case 'slots':
        this.playSlotsForPlayer(username, bet);
        break;
      case 'dice':
        this.playDiceForPlayer(username, bet);
        break;
    }
  };

  // Joacă ruleta pentru jucător
  casino.playRouletteForPlayer = function(username, bet) {
    const player = this.getOrCreatePlayer(username);

    // Simulează ruleta
    const numbers = Array.from({length: 37}, (_, i) => i); // 0-36
    const winningNumber = numbers[Math.floor(Math.random() * numbers.length)];

    // Calculează câștigul (strategia casei - jucătorii câștigă rar)
    const winChance = this.calculatePlayerWinChance(player);
    const isWin = Math.random() < winChance;

    setTimeout(() => {
      this.addChatMessage('🎯 REZULTAT', `Ruleta s-a oprit pe ${winningNumber}!`);

      if (isWin) {
        const winAmount = bet * 2; // Dublează pariul
        player.credits += winAmount;
        player.totalWins++;

        this.addChatMessage('🎉 CÂȘTIG', `${username} a câștigat ${winAmount} credite!`);
        this.addChatMessage('💰 TOTAL', `${username} are acum ${player.credits} credite!`);
      } else {
        this.addChatMessage('😢 PIERDERE', `${username} a pierdut ${bet} credite...`);
        this.addChatMessage('💪 ÎNCURAJARE', `${username}, încearcă din nou! Trimite cadouri pentru mai multe credite!`);
      }
    }, 3000);
  };

  // Joacă blackjack pentru jucător
  casino.playBlackjackForPlayer = function(username, bet) {
    const player = this.getOrCreatePlayer(username);

    // Simulează blackjack simplu
    const playerScore = Math.floor(Math.random() * 11) + 15; // 15-25
    const dealerScore = Math.floor(Math.random() * 11) + 15; // 15-25

    const winChance = this.calculatePlayerWinChance(player);
    const isWin = Math.random() < winChance;

    setTimeout(() => {
      this.addChatMessage('🃏 BLACKJACK', `${username}: ${playerScore} | Dealer: ${dealerScore}`);

      if (isWin && playerScore <= 21) {
        const winAmount = bet * 1.5; // 1.5x pentru blackjack
        player.credits += winAmount;
        player.totalWins++;

        this.addChatMessage('🎉 CÂȘTIG', `${username} a câștigat ${winAmount} credite!`);
      } else {
        this.addChatMessage('😢 PIERDERE', `${username} a pierdut ${bet} credite...`);
      }
    }, 2000);
  };

  // Joacă slots pentru jucător
  casino.playSlotsForPlayer = function(username, bet) {
    const player = this.getOrCreatePlayer(username);

    const symbols = ['🍒', '🍋', '🍊', '🍇', '🍎', '💎', '⭐', '🔔'];
    const reel1 = symbols[Math.floor(Math.random() * symbols.length)];
    const reel2 = symbols[Math.floor(Math.random() * symbols.length)];
    const reel3 = symbols[Math.floor(Math.random() * symbols.length)];

    const winChance = this.calculatePlayerWinChance(player);
    const isWin = Math.random() < winChance;

    setTimeout(() => {
      this.addChatMessage('🎰 SLOTS', `${username}: ${reel1} ${reel2} ${reel3}`);

      if (isWin && reel1 === reel2 && reel2 === reel3) {
        const winAmount = bet * 5; // 5x pentru 3 simboluri identice
        player.credits += winAmount;
        player.totalWins++;

        this.addChatMessage('🎉 JACKPOT', `${username} a câștigat ${winAmount} credite!`);
      } else if (isWin && (reel1 === reel2 || reel2 === reel3)) {
        const winAmount = bet * 2; // 2x pentru 2 simboluri
        player.credits += winAmount;
        player.totalWins++;

        this.addChatMessage('🎉 CÂȘTIG', `${username} a câștigat ${winAmount} credite!`);
      } else {
        this.addChatMessage('😢 PIERDERE', `${username} a pierdut ${bet} credite...`);
      }
    }, 2500);
  };

  // Joacă zaruri pentru jucător
  casino.playDiceForPlayer = function(username, bet) {
    const player = this.getOrCreatePlayer(username);

    const dice1 = Math.floor(Math.random() * 6) + 1;
    const dice2 = Math.floor(Math.random() * 6) + 1;
    const total = dice1 + dice2;

    const winChance = this.calculatePlayerWinChance(player);
    const isWin = Math.random() < winChance;

    setTimeout(() => {
      this.addChatMessage('🎲 ZARURI', `${username}: ${dice1} + ${dice2} = ${total}`);

      if (isWin && total >= 8) {
        const winAmount = bet * 2;
        player.credits += winAmount;
        player.totalWins++;

        this.addChatMessage('🎉 CÂȘTIG', `${username} a câștigat ${winAmount} credite!`);
      } else {
        this.addChatMessage('😢 PIERDERE', `${username} a pierdut ${bet} credite...`);
      }
    }, 1500);
  };

  // Calculează șansele de câștig pentru jucător (strategia casei)
  casino.calculatePlayerWinChance = function(player) {
    let baseChance = 0.25; // 25% șanse de bază

    // Bonus pentru jucători noi
    if (player.totalGames < 3) {
      baseChance += 0.15; // +15% pentru primele 3 jocuri
    }

    // Bonus pentru jucători care dau cadouri
    if (player.totalGifts > 0) {
      baseChance += 0.10; // +10% pentru cei care dau cadouri
    }

    // Scade șansele dacă jucătorul a câștigat recent
    if (player.totalWins > player.totalGames * 0.4) {
      baseChance -= 0.15; // -15% dacă câștigă prea mult
    }

    return Math.max(0.05, Math.min(0.45, baseChance)); // Între 5% și 45%
  };

  // Actualizează afișajul cu numărul de jucători
  casino.updateViewerCount = function(count) {
    this.viewerCount = count;
    const viewerElement = document.getElementById('viewer-count');
    if (viewerElement) {
      viewerElement.textContent = `${count} viewers`;
    }
  };

  // ===== FUNCȚII DE JOC PRINCIPALE =====

  // Ruletă
  casino.spinRoulette = function() {
    console.log('🎯 Învârt ruleta...');
    this.addChatMessage('🎯 ROULETTE', 'Ruleta se învârte...');

    const spinButton = document.getElementById('spin-roulette');
    if (spinButton) {
      spinButton.disabled = true;
      spinButton.textContent = 'SE ÎNVÂRTE...';
    }

    setTimeout(() => {
      const winningNumber = Math.floor(Math.random() * 37); // 0-36
      const isRed = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(winningNumber);
      const color = winningNumber === 0 ? 'verde' : (isRed ? 'roșu' : 'negru');

      this.addChatMessage('🎯 REZULTAT', `Bila s-a oprit pe ${winningNumber} (${color})!`);

      // Simulează câștig/pierdere
      const isWin = Math.random() < 0.3; // 30% șanse
      if (isWin) {
        const winAmount = 50 + Math.floor(Math.random() * 100);
        this.playerChips += winAmount;
        this.addChatMessage('🎉 CÂȘTIG', `Ai câștigat ${winAmount} chips!`);
      } else {
        this.addChatMessage('😢 PIERDERE', 'Mai încearcă o dată!');
      }

      this.updateDisplay();

      if (spinButton) {
        spinButton.disabled = false;
        spinButton.textContent = 'SPIN ROULETTE!';
      }
    }, 3000);
  };

  // Blackjack
  casino.dealCards = function() {
    console.log('🃏 Împart cărțile...');
    this.addChatMessage('🃏 BLACKJACK', 'Împart cărțile...');

    const playerScore = Math.floor(Math.random() * 11) + 15; // 15-25
    const dealerScore = Math.floor(Math.random() * 11) + 15; // 15-25

    setTimeout(() => {
      this.addChatMessage('🃏 CĂRȚI', `Tu: ${playerScore} | Dealer: ${dealerScore}`);

      if (playerScore > 21) {
        this.addChatMessage('💥 BUST', 'Ai depășit 21! Ai pierdut.');
      } else if (dealerScore > 21) {
        const winAmount = 75;
        this.playerChips += winAmount;
        this.addChatMessage('🎉 CÂȘTIG', `Dealer-ul a făcut bust! +${winAmount} chips!`);
      } else if (playerScore > dealerScore) {
        const winAmount = 60;
        this.playerChips += winAmount;
        this.addChatMessage('🎉 CÂȘTIG', `Ai câștigat! +${winAmount} chips!`);
      } else {
        this.addChatMessage('😢 PIERDERE', 'Dealer-ul a câștigat!');
      }

      this.updateDisplay();
    }, 2000);
  };

  casino.hit = function() {
    this.addChatMessage('🃏 HIT', 'Iei o carte...');
    // Implementare simplă pentru demo
    setTimeout(() => {
      const card = Math.floor(Math.random() * 10) + 1;
      this.addChatMessage('🃏 CARTE', `Ai primit: ${card}`);
    }, 1000);
  };

  casino.stand = function() {
    this.addChatMessage('🃏 STAND', 'Te oprești. Dealer-ul joacă...');
    this.dealCards(); // Finalizează jocul
  };

  // Slots
  casino.spinSlots = function() {
    console.log('🎰 Învârt păcănelele...');
    this.addChatMessage('🎰 SLOTS', 'Păcănelele se învârt...');

    const symbols = ['🍒', '🍋', '🍊', '🍇', '🍎', '💎', '⭐', '🔔'];

    setTimeout(() => {
      const reel1 = symbols[Math.floor(Math.random() * symbols.length)];
      const reel2 = symbols[Math.floor(Math.random() * symbols.length)];
      const reel3 = symbols[Math.floor(Math.random() * symbols.length)];

      this.addChatMessage('🎰 REZULTAT', `${reel1} ${reel2} ${reel3}`);

      if (reel1 === reel2 && reel2 === reel3) {
        const winAmount = 200;
        this.playerChips += winAmount;
        this.addChatMessage('🎉 JACKPOT', `TREI IDENTICE! +${winAmount} chips!`);
      } else if (reel1 === reel2 || reel2 === reel3 || reel1 === reel3) {
        const winAmount = 50;
        this.playerChips += winAmount;
        this.addChatMessage('🎉 CÂȘTIG', `DOUĂ IDENTICE! +${winAmount} chips!`);
      } else {
        this.addChatMessage('😢 PIERDERE', 'Încearcă din nou!');
      }

      this.updateDisplay();
    }, 2500);
  };

  // Dice
  casino.rollDice = function() {
    console.log('🎲 Arunc zarurile...');
    this.addChatMessage('🎲 DICE', 'Zarurile se rostogolesc...');

    setTimeout(() => {
      const dice1 = Math.floor(Math.random() * 6) + 1;
      const dice2 = Math.floor(Math.random() * 6) + 1;
      const total = dice1 + dice2;

      this.addChatMessage('🎲 REZULTAT', `${dice1} + ${dice2} = ${total}`);

      if (total >= 8) {
        const winAmount = 40;
        this.playerChips += winAmount;
        this.addChatMessage('🎉 CÂȘTIG', `Total ≥ 8! +${winAmount} chips!`);
      } else {
        this.addChatMessage('😢 PIERDERE', 'Ai nevoie de 8 sau mai mult!');
      }

      this.updateDisplay();
    }, 1500);
  };

  // Actualizează afișajul
  casino.updateDisplay = function() {
    console.log(`💰 Actualizez afișajul: ${this.playerChips} chips`);

    const chipsDisplay = document.getElementById('total-chips');
    if (chipsDisplay) {
      chipsDisplay.textContent = `💰 ${this.playerChips} chips`;
    }

    const viewerDisplay = document.getElementById('viewer-count');
    if (viewerDisplay) {
      viewerDisplay.textContent = `${this.viewerCount} viewers`;
    }
  };
});

// Initialize the casino when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('🎰 Inițializez TikTok Casino PRO...');

  // Create global casino instance
  window.casino = new TikTokLiveCasino();

  // Setup navigation functionality
  setupNavigation();

  // Setup game buttons
  setupGameButtons();

  // Setup chat functionality for testing
  setupChatFunctionality();

  // Initialize display
  window.casino.updateDisplay();

  console.log('✅ Casino inițializat cu succes!');
  console.log('🔗 Pentru a te conecta la TikTok Live, introdu username-ul și apasă CONECTEAZĂ LIVE');
  console.log('🎮 Sau testează local cu comenzile: !spin, !blackjack, !slots, !dice');
});

// Setup chat functionality for local testing
function setupChatFunctionality() {
  const chatInput = document.getElementById('chat-input');
  const sendButton = document.getElementById('send-chat');
  const chatDisplay = document.getElementById('chat-display');

  if (!chatInput || !sendButton) {
    console.log('⚠️ Elementele de chat nu au fost găsite');
    return;
  }

  // Function to send chat message
  function sendChatMessage() {
    const message = chatInput.value.trim();
    if (!message) return;

    console.log(`💬 Mesaj chat: "${message}"`);

    // Simulează un utilizator demo
    const username = window.casino.isLiveConnected ? 'DEMO_PLAYER' : 'TestUser';

    // Procesează mesajul ca și cum ar veni de la TikTok Live
    window.casino.handleTikTokComment(username, message);

    // Curăță input-ul
    chatInput.value = '';
  }

  // Event listeners
  sendButton.addEventListener('click', sendChatMessage);

  chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      sendChatMessage();
    }
  });

  // Update chat display function
  if (chatDisplay) {
    window.casino.addChatMessage = function(username, message) {
      console.log(`💬 ${username}: ${message}`);

      // Add to chat display
      const messageElement = document.createElement('div');
      messageElement.className = 'chat-message';

      const timestamp = new Date().toLocaleTimeString();
      messageElement.innerHTML = `
        <span class="chat-username">${username}</span>
        <span class="chat-time">[${timestamp}]</span>
        <div class="chat-text">${message}</div>
      `;

      chatDisplay.appendChild(messageElement);
      chatDisplay.scrollTop = chatDisplay.scrollHeight;

      // Keep only last 50 messages
      while (chatDisplay.children.length > 50) {
        chatDisplay.removeChild(chatDisplay.firstChild);
      }
    };
  }

  console.log('✅ Chat funcțional configurat pentru testare');
}

// Setup navigation between game sections
function setupNavigation() {
  console.log('🧭 Configurez navigarea...');

  const navButtons = document.querySelectorAll('.nav-btn');
  const sections = document.querySelectorAll('.casino-section');

  console.log(`📋 Găsit ${navButtons.length} butoane de navigare`);
  console.log(`📄 Găsit ${sections.length} secțiuni`);

  navButtons.forEach(button => {
    button.addEventListener('click', () => {
      const targetSection = button.dataset.section;
      console.log(`🎯 Navighez la secțiunea: ${targetSection}`);

      // Remove active class from all buttons and sections
      navButtons.forEach(btn => btn.classList.remove('active'));
      sections.forEach(section => section.classList.remove('active'));

      // Add active class to clicked button
      button.classList.add('active');

      // Show target section
      const target = document.getElementById(targetSection);
      if (target) {
        target.classList.add('active');
        console.log(`✅ Secțiunea ${targetSection} activată`);
      } else {
        console.error(`❌ Secțiunea ${targetSection} nu a fost găsită`);
      }
    });
  });

  console.log('✅ Navigarea configurată cu succes!');
}

// Setup game buttons functionality
function setupGameButtons() {
  console.log('🎮 Configurez butoanele de joc...');

  // Roulette spin button
  const spinButton = document.getElementById('spin-roulette');
  if (spinButton) {
    spinButton.addEventListener('click', () => {
      console.log('🎯 Buton SPIN apăsat!');
      if (window.casino && typeof window.casino.spinRoulette === 'function') {
        window.casino.spinRoulette();
      } else {
        console.error('❌ Funcția spinRoulette nu există!');
        alert('Funcția de ruletă nu este disponibilă!');
      }
    });
    console.log('✅ Buton SPIN configurat');
  } else {
    console.warn('⚠️ Butonul SPIN nu a fost găsit');
  }

  // Blackjack buttons (cu ID-urile corecte din HTML)
  const hitButton = document.getElementById('hit-card');
  const standButton = document.getElementById('stand-cards');
  const dealButton = document.getElementById('deal-cards');

  if (hitButton) {
    hitButton.addEventListener('click', () => {
      console.log('🃏 Buton HIT apăsat!');
      if (window.casino && typeof window.casino.hit === 'function') {
        window.casino.hit();
      }
    });
    console.log('✅ Buton HIT configurat');
  } else {
    console.warn('⚠️ Butonul HIT nu a fost găsit');
  }

  if (standButton) {
    standButton.addEventListener('click', () => {
      console.log('🃏 Buton STAND apăsat!');
      if (window.casino && typeof window.casino.stand === 'function') {
        window.casino.stand();
      }
    });
    console.log('✅ Buton STAND configurat');
  } else {
    console.warn('⚠️ Butonul STAND nu a fost găsit');
  }

  if (dealButton) {
    dealButton.addEventListener('click', () => {
      console.log('🃏 Buton DEAL apăsat!');
      if (window.casino && typeof window.casino.dealCards === 'function') {
        window.casino.dealCards();
      }
    });
    console.log('✅ Buton DEAL configurat');
  } else {
    console.warn('⚠️ Butonul DEAL nu a fost găsit');
  }

  // Slots button
  const spinSlotsButton = document.getElementById('spin-slots');
  if (spinSlotsButton) {
    spinSlotsButton.addEventListener('click', () => {
      console.log('🎰 Buton SLOTS apăsat!');
      if (window.casino && typeof window.casino.spinSlots === 'function') {
        window.casino.spinSlots();
      }
    });
  }

  // Dice button
  const rollDiceButton = document.getElementById('roll-dice');
  if (rollDiceButton) {
    rollDiceButton.addEventListener('click', () => {
      console.log('🎲 Buton DICE apăsat!');
      if (window.casino && typeof window.casino.rollDice === 'function') {
        window.casino.rollDice();
      }
    });
  }

  // Connect/Disconnect buttons
  const connectButton = document.getElementById('connect-btn');
  const disconnectButton = document.getElementById('disconnect-btn');

  if (connectButton) {
    connectButton.addEventListener('click', () => {
      console.log('🔴 Buton CONECTEAZĂ apăsat!');
      if (window.casino && typeof window.casino.connectToTikTokLive === 'function') {
        window.casino.connectToTikTokLive();
      }
    });
  }

  if (disconnectButton) {
    disconnectButton.addEventListener('click', () => {
      console.log('🔌 Buton DECONECTEAZĂ apăsat!');
      if (window.casino && typeof window.casino.disconnectFromTikTokLive === 'function') {
        window.casino.disconnectFromTikTokLive();
      }
    });
  }

  console.log('✅ Butoanele de joc configurate!');
}

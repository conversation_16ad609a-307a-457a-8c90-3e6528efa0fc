"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.0.3
//   protoc               v3.21.12
// source: google/protobuf/compiler/plugin.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGeneratorResponse_File = exports.CodeGeneratorResponse = exports.CodeGeneratorRequest = exports.Version = exports.CodeGeneratorResponse_Feature = void 0;
exports.codeGeneratorResponse_FeatureFromJSON = codeGeneratorResponse_FeatureFromJSON;
exports.codeGeneratorResponse_FeatureToJSON = codeGeneratorResponse_FeatureToJSON;
/* eslint-disable */
const wire_1 = require("@bufbuild/protobuf/wire");
const descriptor_1 = require("../descriptor");
/** Sync with code_generator.h. */
var CodeGeneratorResponse_Feature;
(function (CodeGeneratorResponse_Feature) {
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["FEATURE_NONE"] = 0] = "FEATURE_NONE";
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["FEATURE_PROTO3_OPTIONAL"] = 1] = "FEATURE_PROTO3_OPTIONAL";
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(CodeGeneratorResponse_Feature || (exports.CodeGeneratorResponse_Feature = CodeGeneratorResponse_Feature = {}));
function codeGeneratorResponse_FeatureFromJSON(object) {
    switch (object) {
        case 0:
        case "FEATURE_NONE":
            return CodeGeneratorResponse_Feature.FEATURE_NONE;
        case 1:
        case "FEATURE_PROTO3_OPTIONAL":
            return CodeGeneratorResponse_Feature.FEATURE_PROTO3_OPTIONAL;
        case -1:
        case "UNRECOGNIZED":
        default:
            return CodeGeneratorResponse_Feature.UNRECOGNIZED;
    }
}
function codeGeneratorResponse_FeatureToJSON(object) {
    switch (object) {
        case CodeGeneratorResponse_Feature.FEATURE_NONE:
            return "FEATURE_NONE";
        case CodeGeneratorResponse_Feature.FEATURE_PROTO3_OPTIONAL:
            return "FEATURE_PROTO3_OPTIONAL";
        case CodeGeneratorResponse_Feature.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseVersion() {
    return { major: 0, minor: 0, patch: 0, suffix: "" };
}
exports.Version = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.major !== 0) {
            writer.uint32(8).int32(message.major);
        }
        if (message.minor !== 0) {
            writer.uint32(16).int32(message.minor);
        }
        if (message.patch !== 0) {
            writer.uint32(24).int32(message.patch);
        }
        if (message.suffix !== "") {
            writer.uint32(34).string(message.suffix);
        }
        if (message._unknownFields !== undefined) {
            for (const [key, values] of Object.entries(message._unknownFields)) {
                const tag = parseInt(key, 10);
                for (const value of values) {
                    writer.uint32(tag).raw(value);
                }
            }
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        const end = length === undefined ? reader.len : reader.pos + length;
        const message = Object.create(createBaseVersion());
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.major = reader.int32();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.minor = reader.int32();
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.patch = reader.int32();
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.suffix = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            const buf = reader.skip(tag & 7);
            if (message._unknownFields === undefined) {
                message._unknownFields = {};
            }
            const list = message._unknownFields[tag];
            if (list === undefined) {
                message._unknownFields[tag] = [buf];
            }
            else {
                list.push(buf);
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            major: isSet(object.major) ? globalThis.Number(object.major) : 0,
            minor: isSet(object.minor) ? globalThis.Number(object.minor) : 0,
            patch: isSet(object.patch) ? globalThis.Number(object.patch) : 0,
            suffix: isSet(object.suffix) ? globalThis.String(object.suffix) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.major !== 0) {
            obj.major = Math.round(message.major);
        }
        if (message.minor !== 0) {
            obj.minor = Math.round(message.minor);
        }
        if (message.patch !== 0) {
            obj.patch = Math.round(message.patch);
        }
        if (message.suffix !== "") {
            obj.suffix = message.suffix;
        }
        return obj;
    },
    create(base) {
        return exports.Version.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = Object.create(createBaseVersion());
        message.major = object.major ?? 0;
        message.minor = object.minor ?? 0;
        message.patch = object.patch ?? 0;
        message.suffix = object.suffix ?? "";
        return message;
    },
};
function createBaseCodeGeneratorRequest() {
    return { fileToGenerate: [], parameter: "", protoFile: [], compilerVersion: undefined };
}
exports.CodeGeneratorRequest = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.fileToGenerate) {
            writer.uint32(10).string(v);
        }
        if (message.parameter !== "") {
            writer.uint32(18).string(message.parameter);
        }
        for (const v of message.protoFile) {
            descriptor_1.FileDescriptorProto.encode(v, writer.uint32(122).fork()).join();
        }
        if (message.compilerVersion !== undefined) {
            exports.Version.encode(message.compilerVersion, writer.uint32(26).fork()).join();
        }
        if (message._unknownFields !== undefined) {
            for (const [key, values] of Object.entries(message._unknownFields)) {
                const tag = parseInt(key, 10);
                for (const value of values) {
                    writer.uint32(tag).raw(value);
                }
            }
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        const end = length === undefined ? reader.len : reader.pos + length;
        const message = Object.create(createBaseCodeGeneratorRequest());
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.fileToGenerate.push(reader.string());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.parameter = reader.string();
                    continue;
                case 15:
                    if (tag !== 122) {
                        break;
                    }
                    message.protoFile.push(descriptor_1.FileDescriptorProto.decode(reader, reader.uint32()));
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.compilerVersion = exports.Version.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            const buf = reader.skip(tag & 7);
            if (message._unknownFields === undefined) {
                message._unknownFields = {};
            }
            const list = message._unknownFields[tag];
            if (list === undefined) {
                message._unknownFields[tag] = [buf];
            }
            else {
                list.push(buf);
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            fileToGenerate: globalThis.Array.isArray(object?.fileToGenerate)
                ? object.fileToGenerate.map((e) => globalThis.String(e))
                : [],
            parameter: isSet(object.parameter) ? globalThis.String(object.parameter) : "",
            protoFile: globalThis.Array.isArray(object?.protoFile)
                ? object.protoFile.map((e) => descriptor_1.FileDescriptorProto.fromJSON(e))
                : [],
            compilerVersion: isSet(object.compilerVersion) ? exports.Version.fromJSON(object.compilerVersion) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.fileToGenerate?.length) {
            obj.fileToGenerate = message.fileToGenerate;
        }
        if (message.parameter !== "") {
            obj.parameter = message.parameter;
        }
        if (message.protoFile?.length) {
            obj.protoFile = message.protoFile.map((e) => descriptor_1.FileDescriptorProto.toJSON(e));
        }
        if (message.compilerVersion !== undefined) {
            obj.compilerVersion = exports.Version.toJSON(message.compilerVersion);
        }
        return obj;
    },
    create(base) {
        return exports.CodeGeneratorRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = Object.create(createBaseCodeGeneratorRequest());
        message.fileToGenerate = object.fileToGenerate?.map((e) => e) || [];
        message.parameter = object.parameter ?? "";
        message.protoFile = object.protoFile?.map((e) => descriptor_1.FileDescriptorProto.fromPartial(e)) || [];
        message.compilerVersion = (object.compilerVersion !== undefined && object.compilerVersion !== null)
            ? exports.Version.fromPartial(object.compilerVersion)
            : undefined;
        return message;
    },
};
function createBaseCodeGeneratorResponse() {
    return { error: "", supportedFeatures: 0, file: [] };
}
exports.CodeGeneratorResponse = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.error !== "") {
            writer.uint32(10).string(message.error);
        }
        if (message.supportedFeatures !== 0) {
            writer.uint32(16).uint64(message.supportedFeatures);
        }
        for (const v of message.file) {
            exports.CodeGeneratorResponse_File.encode(v, writer.uint32(122).fork()).join();
        }
        if (message._unknownFields !== undefined) {
            for (const [key, values] of Object.entries(message._unknownFields)) {
                const tag = parseInt(key, 10);
                for (const value of values) {
                    writer.uint32(tag).raw(value);
                }
            }
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        const end = length === undefined ? reader.len : reader.pos + length;
        const message = Object.create(createBaseCodeGeneratorResponse());
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.error = reader.string();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.supportedFeatures = longToNumber(reader.uint64());
                    continue;
                case 15:
                    if (tag !== 122) {
                        break;
                    }
                    message.file.push(exports.CodeGeneratorResponse_File.decode(reader, reader.uint32()));
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            const buf = reader.skip(tag & 7);
            if (message._unknownFields === undefined) {
                message._unknownFields = {};
            }
            const list = message._unknownFields[tag];
            if (list === undefined) {
                message._unknownFields[tag] = [buf];
            }
            else {
                list.push(buf);
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            error: isSet(object.error) ? globalThis.String(object.error) : "",
            supportedFeatures: isSet(object.supportedFeatures) ? globalThis.Number(object.supportedFeatures) : 0,
            file: globalThis.Array.isArray(object?.file)
                ? object.file.map((e) => exports.CodeGeneratorResponse_File.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.error !== "") {
            obj.error = message.error;
        }
        if (message.supportedFeatures !== 0) {
            obj.supportedFeatures = Math.round(message.supportedFeatures);
        }
        if (message.file?.length) {
            obj.file = message.file.map((e) => exports.CodeGeneratorResponse_File.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return exports.CodeGeneratorResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = Object.create(createBaseCodeGeneratorResponse());
        message.error = object.error ?? "";
        message.supportedFeatures = object.supportedFeatures ?? 0;
        message.file = object.file?.map((e) => exports.CodeGeneratorResponse_File.fromPartial(e)) || [];
        return message;
    },
};
function createBaseCodeGeneratorResponse_File() {
    return { name: "", insertionPoint: "", content: "", generatedCodeInfo: undefined };
}
exports.CodeGeneratorResponse_File = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.insertionPoint !== "") {
            writer.uint32(18).string(message.insertionPoint);
        }
        if (message.content !== "") {
            writer.uint32(122).string(message.content);
        }
        if (message.generatedCodeInfo !== undefined) {
            descriptor_1.GeneratedCodeInfo.encode(message.generatedCodeInfo, writer.uint32(130).fork()).join();
        }
        if (message._unknownFields !== undefined) {
            for (const [key, values] of Object.entries(message._unknownFields)) {
                const tag = parseInt(key, 10);
                for (const value of values) {
                    writer.uint32(tag).raw(value);
                }
            }
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        const end = length === undefined ? reader.len : reader.pos + length;
        const message = Object.create(createBaseCodeGeneratorResponse_File());
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.name = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.insertionPoint = reader.string();
                    continue;
                case 15:
                    if (tag !== 122) {
                        break;
                    }
                    message.content = reader.string();
                    continue;
                case 16:
                    if (tag !== 130) {
                        break;
                    }
                    message.generatedCodeInfo = descriptor_1.GeneratedCodeInfo.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            const buf = reader.skip(tag & 7);
            if (message._unknownFields === undefined) {
                message._unknownFields = {};
            }
            const list = message._unknownFields[tag];
            if (list === undefined) {
                message._unknownFields[tag] = [buf];
            }
            else {
                list.push(buf);
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            name: isSet(object.name) ? globalThis.String(object.name) : "",
            insertionPoint: isSet(object.insertionPoint) ? globalThis.String(object.insertionPoint) : "",
            content: isSet(object.content) ? globalThis.String(object.content) : "",
            generatedCodeInfo: isSet(object.generatedCodeInfo)
                ? descriptor_1.GeneratedCodeInfo.fromJSON(object.generatedCodeInfo)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.name !== "") {
            obj.name = message.name;
        }
        if (message.insertionPoint !== "") {
            obj.insertionPoint = message.insertionPoint;
        }
        if (message.content !== "") {
            obj.content = message.content;
        }
        if (message.generatedCodeInfo !== undefined) {
            obj.generatedCodeInfo = descriptor_1.GeneratedCodeInfo.toJSON(message.generatedCodeInfo);
        }
        return obj;
    },
    create(base) {
        return exports.CodeGeneratorResponse_File.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = Object.create(createBaseCodeGeneratorResponse_File());
        message.name = object.name ?? "";
        message.insertionPoint = object.insertionPoint ?? "";
        message.content = object.content ?? "";
        message.generatedCodeInfo = (object.generatedCodeInfo !== undefined && object.generatedCodeInfo !== null)
            ? descriptor_1.GeneratedCodeInfo.fromPartial(object.generatedCodeInfo)
            : undefined;
        return message;
    },
};
function longToNumber(int64) {
    const num = globalThis.Number(int64.toString());
    if (num > globalThis.Number.MAX_SAFE_INTEGER) {
        throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
    }
    if (num < globalThis.Number.MIN_SAFE_INTEGER) {
        throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
    }
    return num;
}
function isSet(value) {
    return value !== null && value !== undefined;
}

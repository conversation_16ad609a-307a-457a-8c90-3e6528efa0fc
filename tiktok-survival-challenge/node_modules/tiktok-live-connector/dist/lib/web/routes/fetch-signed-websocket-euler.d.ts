import { Route } from '../../../types/route';
import { WebcastResponse } from '../../../types/tiktok-schema';
import { FetchSignedWebSocketParams } from '../../../types/client';
export type FetchSignedWebSocketFromEulerRouteParams = FetchSignedWebSocketParams;
export declare class FetchSignedWebSocketFromEulerRoute extends Route<FetchSignedWebSocketFromEulerRouteParams, WebcastResponse> {
    call({ roomId, uniqueId, preferredAgentIds, sessionId }: FetchSignedWebSocketFromEulerRouteParams): Promise<WebcastResponse>;
}
//# sourceMappingURL=fetch-signed-websocket-euler.d.ts.map
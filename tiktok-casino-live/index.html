<!doctype html>
<html lang="ro">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🎰 TikTok Casino Live - Joacă în timp real!</title>
  </head>
  <body>
    <div class="casino-container">
      <!-- Header -->
      <header class="casino-header">
        <div class="casino-title">🎰 TikTok Live Casino</div>
        <div class="casino-stats">
          <div class="stat-item" id="viewer-count">0 players</div>
          <div class="stat-item" id="credits-display">0 credite</div>
          <div class="live-indicator" id="live-indicator">OFFLINE</div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="casino-main">
        <!-- Game Area -->
        <div class="game-area">
          <!-- Game Tabs -->
          <div class="game-tabs">
            <button class="game-tab active" data-game="roulette">🎯 Ruletă</button>
            <button class="game-tab" data-game="blackjack">🃏 Blackjack</button>
            <button class="game-tab" data-game="slots">🎰 Păcănele</button>
            <button class="game-tab" data-game="dice">🎲 Zaruri</button>
          </div>

          <!-- Roulette Game -->
          <div class="game-section active" id="roulette-section">
            <div class="roulette-container">
              <h2>🎯 Ruletă Europeană</h2>
              <div class="roulette-wheel">
                <div class="roulette-ball" id="roulette-ball"></div>
              </div>
              <div class="last-number" id="last-number">Ultimul număr: -</div>

              <div class="betting-area">
                <div class="bet-controls">
                  <label>Suma pariu:</label>
                  <input type="number" id="bet-amount" class="bet-amount" value="10" min="1" max="1000">
                  <button class="action-btn" id="clear-bets">Șterge pariuri</button>
                  <button class="action-btn" id="max-bet">Pariu maxim</button>
                </div>

                <div class="bet-buttons">
                  <button class="bet-btn" onclick="casino.placeBet('outside', 'red')">🔴 Roșu (1:1)</button>
                  <button class="bet-btn" onclick="casino.placeBet('outside', 'black')">⚫ Negru (1:1)</button>
                  <button class="bet-btn" onclick="casino.placeBet('outside', 'even')">📊 Par (1:1)</button>
                  <button class="bet-btn" onclick="casino.placeBet('outside', 'odd')">📈 Impar (1:1)</button>
                  <button class="bet-btn" onclick="casino.placeBet('outside', '1-18')">1-18 (1:1)</button>
                  <button class="bet-btn" onclick="casino.placeBet('outside', '19-36')">19-36 (1:1)</button>
                </div>

                <button class="action-btn" id="spin-roulette" onclick="casino.spinRoulette()">🎯 ÎNVÂRTE RULETA!</button>
              </div>
            </div>
          </div>

          <!-- Blackjack Game -->
          <div class="game-section" id="blackjack-section">
            <div class="blackjack-container">
              <h2>🃏 Blackjack</h2>
              <div class="blackjack-table">
                <div class="dealer-area">
                  <h3>Dealer</h3>
                  <div class="cards" id="dealer-cards"></div>
                  <div class="score" id="dealer-score">Score: 0</div>
                </div>

                <div class="player-area">
                  <h3>Tu</h3>
                  <div class="cards" id="player-cards"></div>
                  <div class="score" id="player-score">Score: 0</div>
                </div>
              </div>

              <div class="blackjack-controls">
                <button class="action-btn" id="deal-blackjack" onclick="casino.dealBlackjack()">🃏 Împarte cărțile</button>
                <button class="action-btn" id="hit-blackjack" onclick="casino.hitBlackjack()" disabled>📈 Carte</button>
                <button class="action-btn" id="stand-blackjack" onclick="casino.standBlackjack()" disabled>✋ Stai</button>
              </div>
            </div>
          </div>

          <!-- Slots Game -->
          <div class="game-section" id="slots-section">
            <div class="slots-container">
              <h2>🎰 Păcănele</h2>
              <div class="slots-machine">
                <div class="slots-reels">
                  <div class="reel" id="reel1">🍒</div>
                  <div class="reel" id="reel2">🍋</div>
                  <div class="reel" id="reel3">🍊</div>
                </div>
              </div>

              <div class="slots-controls">
                <button class="action-btn" id="spin-slots" onclick="casino.spinSlots()">🎰 SPIN!</button>
              </div>
            </div>
          </div>

          <!-- Dice Game -->
          <div class="game-section" id="dice-section">
            <div class="dice-container">
              <h2>🎲 Zaruri</h2>
              <div class="dice-area">
                <div class="dice" id="dice1">⚀</div>
                <div class="dice" id="dice2">⚀</div>
              </div>
              <div class="dice-result" id="dice-result">Total: 2</div>

              <div class="dice-controls">
                <button class="action-btn" id="roll-dice" onclick="casino.rollDice()">🎲 Aruncă zarurile!</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
          <!-- Connection Panel -->
          <div class="connection-panel">
            <h3>🔴 Conectare TikTok Live</h3>
            <div class="connection-form">
              <div class="form-group">
                <label>Username TikTok (fără @):</label>
                <input type="text" id="tiktok-username" class="form-input" placeholder="username_tau">
              </div>
              <button class="action-btn" id="connect-tiktok">🔴 Conectează Live</button>
              <button class="action-btn" id="disconnect-tiktok" style="display: none;">❌ Deconectează</button>
            </div>
          </div>

          <!-- Credits Panel -->
          <div class="connection-panel">
            <h3>💰 Credite</h3>
            <div class="credits-display" id="credits-balance">0 credite</div>
            <div class="credits-info">
              <p>🆓 <strong>Credite gratuite:</strong></p>
              <p>• 👆 3000 tap-uri = 100 credite</p>
              <p>• 📤 30 distribuiri = 100 credite</p>
              <p>🎁 <strong>Cadouri retrăgabile:</strong></p>
              <p>• 🌹 Rose = 500 credite</p>
              <p>• ❤️ Heart = 200 credite</p>
              <p>• 💎 Diamond = 1000 credite</p>
            </div>
            <button class="action-btn" id="withdraw-credits">💸 Retrage credite</button>
          </div>

          <!-- Chat -->
          <div class="chat-container">
            <h3>💬 Chat Live</h3>
            <div class="chat-messages" id="chat-messages"></div>
          </div>
        </div>
      </main>
    </div>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>

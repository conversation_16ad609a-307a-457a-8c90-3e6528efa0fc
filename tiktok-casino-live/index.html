<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>� TikTok Live Casino</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <!-- Casino Container -->
      <div id="casino-container">
        <!-- Header -->
        <header class="casino-header">
          <h1>� TikTok Live Casino</h1>
          <div class="casino-status">
            <span id="viewer-count">0 viewers</span>
            <span id="total-chips">💰 1000 chips</span>
            <span id="house-edge">🏠 House: 2.5%</span>
          </div>
        </header>

        <!-- Auto-Pilot Control Button -->
        <button id="autopilot-toggle" class="autopilot-toggle">🤖</button>

        <!-- Main Casino Area -->
        <main class="casino-main">
          <!-- Roulette Section -->
          <section id="roulette-section" class="casino-section active">
            <div class="roulette-container">
              <div class="roulette-wheel-container">
                <canvas id="roulette-wheel" width="400" height="400"></canvas>
                <div class="roulette-ball" id="roulette-ball"></div>
                <button id="spin-roulette" class="spin-button">SPIN ROULETTE!</button>
              </div>

              <div class="roulette-betting">
                <h3>Place Your Bets</h3>
                <div class="betting-board">
                  <div class="number-grid">
                    <!-- Numbers 0-36 will be generated by JS -->
                  </div>
                  <div class="outside-bets">
                    <button class="bet-btn" data-bet="red">Red (1:1)</button>
                    <button class="bet-btn" data-bet="black">Black (1:1)</button>
                    <button class="bet-btn" data-bet="even">Even (1:1)</button>
                    <button class="bet-btn" data-bet="odd">Odd (1:1)</button>
                    <button class="bet-btn" data-bet="low">1-18 (1:1)</button>
                    <button class="bet-btn" data-bet="high">19-36 (1:1)</button>
                  </div>
                </div>
                <div class="betting-controls">
                  <input type="number" id="bet-amount" placeholder="Bet amount" min="1" max="100" value="10">
                  <button id="clear-bets" class="control-btn">Clear Bets</button>
                  <button id="max-bet" class="control-btn">Max Bet</button>
                </div>
              </div>
            </div>
          </section>

          <!-- Blackjack Section -->
          <section id="blackjack-section" class="casino-section">
            <div class="blackjack-container">
              <div class="blackjack-table">
                <div class="dealer-area">
                  <h3>Dealer</h3>
                  <div id="dealer-cards" class="card-area">
                    <div class="card-placeholder">Dealer's Cards</div>
                  </div>
                  <div id="dealer-score" class="score">Score: 0</div>
                </div>

                <div class="player-area">
                  <h3>Player</h3>
                  <div id="player-cards" class="card-area">
                    <div class="card-placeholder">Your Cards</div>
                  </div>
                  <div id="player-score" class="score">Score: 0</div>
                </div>
              </div>

              <div class="blackjack-controls">
                <button id="deal-cards" class="game-btn">Deal Cards</button>
                <button id="hit-card" class="game-btn" disabled>Hit</button>
                <button id="stand-cards" class="game-btn" disabled>Stand</button>
                <button id="double-down" class="game-btn" disabled>Double Down</button>
                <div class="bet-controls">
                  <input type="number" id="blackjack-bet" placeholder="Bet" min="1" max="100" value="10">
                  <span id="blackjack-result" class="result-text"></span>
                </div>
              </div>
            </div>
          </section>

          <!-- Slot Machine Section -->
          <section id="slots-section" class="casino-section">
            <div class="slots-container">
              <div class="slot-machine">
                <div class="slots-display">
                  <div class="reel" id="reel1">
                    <div class="symbol">🍒</div>
                    <div class="symbol">🍋</div>
                    <div class="symbol">🍊</div>
                  </div>
                  <div class="reel" id="reel2">
                    <div class="symbol">🍒</div>
                    <div class="symbol">🍋</div>
                    <div class="symbol">�</div>
                  </div>
                  <div class="reel" id="reel3">
                    <div class="symbol">🍒</div>
                    <div class="symbol">🍋</div>
                    <div class="symbol">🍊</div>
                  </div>
                </div>

                <div class="slots-controls">
                  <button id="spin-slots" class="spin-button">SPIN SLOTS!</button>
                  <div class="bet-selector">
                    <label>Bet per line:</label>
                    <select id="slots-bet">
                      <option value="1">1 chip</option>
                      <option value="5">5 chips</option>
                      <option value="10" selected>10 chips</option>
                      <option value="25">25 chips</option>
                      <option value="50">50 chips</option>
                    </select>
                  </div>
                </div>

                <div class="paytable">
                  <h4>Paytable</h4>
                  <div class="payout-line">🍒🍒🍒 = 100x</div>
                  <div class="payout-line">🍋🍋🍋 = 50x</div>
                  <div class="payout-line">🍊🍊� = 25x</div>
                  <div class="payout-line">💎💎💎 = 500x</div>
                  <div class="payout-line">⭐⭐⭐ = 1000x</div>
                </div>
              </div>
            </div>
          </section>

          <!-- Dice Game Section -->
          <section id="dice-section" class="casino-section">
            <div class="dice-container">
              <div class="dice-game">
                <h3>🎲 Dice Roll Game</h3>
                <div class="dice-display">
                  <div class="dice" id="dice1">⚀</div>
                  <div class="dice" id="dice2">⚀</div>
                  <div class="total-display">Total: <span id="dice-total">2</span></div>
                </div>

                <div class="dice-betting">
                  <div class="bet-options">
                    <button class="bet-btn" data-bet="under7">Under 7 (1:1)</button>
                    <button class="bet-btn" data-bet="seven">Seven (4:1)</button>
                    <button class="bet-btn" data-bet="over7">Over 7 (1:1)</button>
                    <button class="bet-btn" data-bet="doubles">Doubles (5:1)</button>
                  </div>
                  <div class="dice-controls">
                    <input type="number" id="dice-bet" placeholder="Bet amount" min="1" max="100" value="10">
                    <button id="roll-dice" class="game-btn">Roll Dice!</button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Statistics & Leaderboard -->
          <section id="stats-section" class="casino-section">
            <div class="stats-container">
              <div class="casino-stats">
                <h3>🏆 Casino Statistics</h3>
                <div class="stats-grid">
                  <div class="stat-card">
                    <div class="stat-number" id="total-games">0</div>
                    <div class="stat-label">Games Played</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-number" id="total-winnings">0</div>
                    <div class="stat-label">Total Winnings</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-number" id="biggest-win">0</div>
                    <div class="stat-label">Biggest Win</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-number" id="win-rate">0%</div>
                    <div class="stat-label">Win Rate</div>
                  </div>
                </div>
              </div>

              <div class="leaderboard">
                <h3>🎯 High Rollers</h3>
                <div id="casino-leaderboard" class="leaderboard-list">
                  <!-- Leaderboard entries will be populated here -->
                </div>
              </div>
            </div>
          </section>

          <!-- Chat Section -->
          <section id="chat-section" class="casino-section">
            <div class="chat-container">
              <h2>� Casino Chat</h2>
              <div id="chat-display" class="chat-display"></div>
              <div class="chat-controls">
                <input type="text" id="chat-input" placeholder="Chat with other players...">
                <button id="send-chat" class="control-btn">Send</button>
              </div>
            </div>
          </section>
        </main>

        <!-- Navigation -->
        <nav class="casino-nav">
          <button class="nav-btn active" data-section="roulette-section">🎯 Roulette</button>
          <button class="nav-btn" data-section="blackjack-section">🃏 Blackjack</button>
          <button class="nav-btn" data-section="slots-section">🎰 Slots</button>
          <button class="nav-btn" data-section="dice-section">🎲 Dice</button>
          <button class="nav-btn" data-section="stats-section">📊 Stats</button>
          <button class="nav-btn" data-section="chat-section">💬 Chat</button>
        </nav>

        <!-- Live Indicator -->
        <div id="live-indicator" class="live-indicator">LIVE CASINO</div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

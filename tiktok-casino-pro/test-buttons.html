<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Butoane Casino</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 2rem;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: #16213e;
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #ffd700;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .test-button {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #ffd700;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { border-left-color: #00ff00; }
        .error { border-left-color: #ff0000; }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #ffd700;
            border-radius: 10px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎰 Test Butoane Casino - LIVE</h1>
        <p>Testează toate butoanele din aplicația casino în timp real!</p>
        
        <div class="test-section">
            <h3>📱 Aplicația Casino LIVE</h3>
            <div class="iframe-container">
                <iframe src="http://localhost:5173/" title="TikTok Casino PRO"></iframe>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 Instrucțiuni de Testare</h3>
            <div class="result">
                <strong>1. NAVIGARE ÎNTRE JOCURI:</strong><br>
                • Apasă butoanele din partea de jos: 🎯 Roulette, 🃏 Blackjack, 🎰 Slots, 🎲 Dice<br>
                • Verifică dacă secțiunile se schimbă<br><br>
                
                <strong>2. CONECTARE TIKTOK:</strong><br>
                • Introdu username-ul: xo.xo.xo.xo.xo.xo.xo.xol<br>
                • Apasă "🔴 CONECTEAZĂ LIVE"<br>
                • Ar trebui să activeze modul DEMO<br><br>
                
                <strong>3. TESTARE JOCURI:</strong><br>
                • <strong>Ruletă:</strong> Apasă "SPIN ROULETTE!"<br>
                • <strong>Blackjack:</strong> Apasă "Deal Cards", apoi "Hit" sau "Stand"<br>
                • <strong>Slots:</strong> Apasă "SPIN SLOTS!"<br>
                • <strong>Dice:</strong> Apasă "Roll Dice!"<br><br>
                
                <strong>4. CHAT COMENZI:</strong><br>
                • Mergi la secțiunea 💬 Chat<br>
                • Scrie: !spin, !blackjack, !slots, !dice<br>
                • Verifică răspunsurile în chat<br><br>
                
                <strong>5. VERIFICĂ CONSOLA:</strong><br>
                • Apasă F12 pentru Developer Tools<br>
                • Mergi la tab-ul "Console"<br>
                • Verifică mesajele de debug<br>
            </div>
        </div>
        
        <div class="test-section">
            <h3>✅ Checklist Funcționalitate</h3>
            <div class="result">
                <input type="checkbox" id="nav"> <label for="nav">Navigarea între jocuri funcționează</label><br>
                <input type="checkbox" id="connect"> <label for="connect">Butonul de conectare funcționează</label><br>
                <input type="checkbox" id="roulette"> <label for="roulette">Ruleta se învârte</label><br>
                <input type="checkbox" id="blackjack"> <label for="blackjack">Blackjack-ul funcționează</label><br>
                <input type="checkbox" id="slots"> <label for="slots">Păcănelele se învârt</label><br>
                <input type="checkbox" id="dice"> <label for="dice">Zarurile se rostogolesc</label><br>
                <input type="checkbox" id="chat"> <label for="chat">Chat-ul răspunde la comenzi</label><br>
                <input type="checkbox" id="display"> <label for="display">Afișajul se actualizează</label><br>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚨 Dacă Ceva Nu Funcționează</h3>
            <div class="result error">
                <strong>PAȘI DE DEPANARE:</strong><br><br>
                
                1. <strong>Reîmprospătează pagina</strong> (F5 sau Ctrl+R)<br>
                2. <strong>Verifică consola</strong> (F12 → Console) pentru erori<br>
                3. <strong>Verifică Network</strong> (F12 → Network) pentru resurse care nu se încarcă<br>
                4. <strong>Încearcă în modul incognito</strong><br>
                5. <strong>Verifică dacă serverul rulează</strong> pe localhost:5173<br><br>
                
                <strong>ERORI COMUNE:</strong><br>
                • "Cannot read property" → JavaScript nu s-a încărcat complet<br>
                • "404 Not Found" → Serverul nu rulează<br>
                • "CORS error" → Problemă de securitate browser<br>
                • Butoanele nu răspund → Event listeners nu sunt configurați<br>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Test Rapid Username</h3>
            <button class="test-button" onclick="testUsername()">Testează Username</button>
            <div id="username-test" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function testUsername() {
            const result = document.getElementById('username-test');
            result.style.display = 'block';
            result.className = 'result success';
            
            const username = 'xo.xo.xo.xo.xo.xo.xo.xol';
            
            result.innerHTML = `
                ✅ <strong>Username testat:</strong> ${username}<br>
                ✅ <strong>Lungime:</strong> ${username.length} caractere (OK)<br>
                ✅ <strong>Caractere permise:</strong> Doar litere, cifre, punct, underscore (OK)<br>
                ✅ <strong>Format:</strong> Valid pentru TikTok (OK)<br><br>
                
                <strong>Acest username va funcționa perfect în aplicație!</strong>
            `;
        }
        
        // Auto-refresh iframe every 30 seconds to catch updates
        setInterval(() => {
            const iframe = document.querySelector('iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }, 30000);
    </script>
</body>
</html>

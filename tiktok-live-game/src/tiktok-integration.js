// TikTok Live Integration Module
// This module provides integration with TikTok Live features and simulates real-time interactions

export class TikTokLiveIntegration {
  constructor(gameInstance) {
    this.game = gameInstance;
    this.isConnected = false;
    this.connectionStatus = 'disconnected';
    this.liveStreamData = {
      streamKey: '',
      rtmpUrl: '',
      viewerCount: 0,
      isLive: false
    };
    this.chatCommands = {
      '!spin': () => this.game.spinWheel(),
      '!vote': (args) => this.handleVoteCommand(args),
      '!story': () => this.game.switchSection('story-section'),
      '!wheel': () => this.game.switchSection('wheel-section'),
      '!chat': () => this.game.switchSection('chat-section'),
      '!help': () => this.showHelpCommands()
    };
    this.giftEffects = {
      'rose': { points: 1, effect: 'sparkle' },
      'heart': { points: 5, effect: 'hearts' },
      'diamond': { points: 10, effect: 'diamond_rain' },
      'rocket': { points: 50, effect: 'rocket_boost' },
      'lion': { points: 100, effect: 'golden_lion' }
    };
    
    this.init();
  }

  init() {
    this.setupUI();
    this.simulateConnection();
    this.startChatMonitoring();
    this.setupGiftHandling();
  }

  setupUI() {
    // Add TikTok Live connection panel to the game
    const connectionPanel = document.createElement('div');
    connectionPanel.id = 'tiktok-connection-panel';
    connectionPanel.className = 'tiktok-panel';
    connectionPanel.innerHTML = `
      <div class="connection-header">
        <h3>📱 TikTok Live Connection</h3>
        <div class="connection-status ${this.connectionStatus}">
          <span class="status-indicator"></span>
          <span class="status-text">${this.connectionStatus}</span>
        </div>
      </div>
      <div class="connection-controls">
        <button id="connect-tiktok" class="control-btn">Connect to TikTok Live</button>
        <button id="start-stream" class="control-btn" disabled>Start Stream</button>
        <button id="stop-stream" class="control-btn" disabled>Stop Stream</button>
      </div>
      <div class="stream-info">
        <div class="info-item">
          <label>Stream Key:</label>
          <input type="text" id="stream-key" placeholder="Enter your TikTok Live stream key" readonly>
        </div>
        <div class="info-item">
          <label>RTMP URL:</label>
          <input type="text" id="rtmp-url" value="rtmp://push.tiktokcdn.com/live/" readonly>
        </div>
        <div class="info-item">
          <label>Live Viewers:</label>
          <span id="live-viewer-count">0</span>
        </div>
      </div>
      <div class="chat-commands">
        <h4>Available Chat Commands:</h4>
        <div class="command-list">
          <span class="command">!spin</span> - Spin the adventure wheel
          <span class="command">!vote</span> - Start a vote
          <span class="command">!story</span> - View story
          <span class="command">!help</span> - Show all commands
        </div>
      </div>
    `;

    // Add to settings panel or create new panel
    const gameContainer = document.getElementById('game-container');
    gameContainer.appendChild(connectionPanel);

    // Setup event listeners
    document.getElementById('connect-tiktok').addEventListener('click', () => this.connectToTikTok());
    document.getElementById('start-stream').addEventListener('click', () => this.startStream());
    document.getElementById('stop-stream').addEventListener('click', () => this.stopStream());
  }

  simulateConnection() {
    // Simulate TikTok Live connection for demo purposes
    setTimeout(() => {
      this.connectionStatus = 'connecting';
      this.updateConnectionStatus();
      
      setTimeout(() => {
        this.connectionStatus = 'connected';
        this.isConnected = true;
        this.updateConnectionStatus();
        this.enableStreamControls();
        this.game.addChatMessage('TikTok Live', 'Connected to TikTok Live! Ready to stream.');
      }, 2000);
    }, 1000);
  }

  updateConnectionStatus() {
    const statusElement = document.querySelector('.connection-status');
    const statusText = document.querySelector('.status-text');
    
    statusElement.className = `connection-status ${this.connectionStatus}`;
    statusText.textContent = this.connectionStatus;
  }

  enableStreamControls() {
    document.getElementById('start-stream').disabled = false;
    document.getElementById('stream-key').value = 'live_' + Math.random().toString(36).substr(2, 9);
  }

  connectToTikTok() {
    if (this.isConnected) return;
    
    this.connectionStatus = 'connecting';
    this.updateConnectionStatus();
    
    // Simulate connection process
    setTimeout(() => {
      this.isConnected = true;
      this.connectionStatus = 'connected';
      this.updateConnectionStatus();
      this.enableStreamControls();
      this.game.addChatMessage('System', 'Successfully connected to TikTok Live!');
    }, 3000);
  }

  startStream() {
    if (!this.isConnected) return;
    
    this.liveStreamData.isLive = true;
    document.getElementById('start-stream').disabled = true;
    document.getElementById('stop-stream').disabled = false;
    
    this.game.addChatMessage('TikTok Live', '🔴 Stream started! You are now live on TikTok!');
    this.startViewerSimulation();
  }

  stopStream() {
    this.liveStreamData.isLive = false;
    document.getElementById('start-stream').disabled = false;
    document.getElementById('stop-stream').disabled = true;
    
    this.game.addChatMessage('TikTok Live', '⏹️ Stream ended. Thanks for watching!');
  }

  startViewerSimulation() {
    // Simulate viewer count changes
    setInterval(() => {
      if (this.liveStreamData.isLive) {
        const change = Math.floor(Math.random() * 10) - 5;
        this.liveStreamData.viewerCount = Math.max(0, this.liveStreamData.viewerCount + change);
        document.getElementById('live-viewer-count').textContent = this.liveStreamData.viewerCount;
        this.game.viewerCount = this.liveStreamData.viewerCount;
        this.game.updateViewerCount();
      }
    }, 5000);
  }

  startChatMonitoring() {
    // Monitor for chat commands
    const originalAddChatMessage = this.game.addChatMessage.bind(this.game);
    this.game.addChatMessage = (username, message) => {
      originalAddChatMessage(username, message);
      this.processChatCommand(username, message);
    };
  }

  processChatCommand(username, message) {
    if (!message.startsWith('!')) return;
    
    const parts = message.split(' ');
    const command = parts[0].toLowerCase();
    const args = parts.slice(1);
    
    if (this.chatCommands[command]) {
      this.chatCommands[command](args);
      this.game.addChatMessage('System', `Command ${command} executed by ${username}`);
    }
  }

  handleVoteCommand(args) {
    if (args.length === 0) {
      this.game.switchSection('voting-section');
      this.game.startVote();
    } else {
      // Custom vote with provided options
      const question = args.join(' ');
      this.game.switchSection('voting-section');
      // You could extend this to handle custom vote options
    }
  }

  showHelpCommands() {
    const commands = Object.keys(this.chatCommands).join(', ');
    this.game.addChatMessage('System', `Available commands: ${commands}`);
  }

  setupGiftHandling() {
    // Simulate gift receiving
    setInterval(() => {
      if (this.liveStreamData.isLive && Math.random() < 0.1) { // 10% chance every interval
        this.simulateGiftReceived();
      }
    }, 10000);
  }

  simulateGiftReceived() {
    const gifts = Object.keys(this.giftEffects);
    const randomGift = gifts[Math.floor(Math.random() * gifts.length)];
    const giftData = this.giftEffects[randomGift];
    const viewer = `Viewer${Math.floor(Math.random() * 1000)}`;
    
    this.handleGiftReceived(viewer, randomGift, giftData);
  }

  handleGiftReceived(username, giftType, giftData) {
    // Add chat message about gift
    this.game.addChatMessage('TikTok Gifts', `${username} sent ${giftType}! (+${giftData.points} points)`);
    
    // Trigger visual effect
    this.triggerGiftEffect(giftData.effect);
    
    // Add to story if significant gift
    if (giftData.points >= 50) {
      this.game.addToStory(`${username} sent an amazing ${giftType} gift! The adventure becomes more exciting!`);
    }
  }

  triggerGiftEffect(effectType) {
    const gameContainer = document.getElementById('game-container');
    const effect = document.createElement('div');
    effect.className = `gift-effect ${effectType}`;
    
    switch (effectType) {
      case 'sparkle':
        effect.innerHTML = '✨✨✨';
        break;
      case 'hearts':
        effect.innerHTML = '💖💖💖';
        break;
      case 'diamond_rain':
        effect.innerHTML = '💎💎💎';
        break;
      case 'rocket_boost':
        effect.innerHTML = '🚀🚀🚀';
        break;
      case 'golden_lion':
        effect.innerHTML = '🦁👑🦁';
        break;
    }
    
    effect.style.cssText = `
      position: absolute;
      top: 20%;
      left: 50%;
      transform: translateX(-50%);
      font-size: 2rem;
      z-index: 1000;
      animation: giftEffect 3s ease-out forwards;
      pointer-events: none;
    `;
    
    gameContainer.appendChild(effect);
    
    setTimeout(() => {
      gameContainer.removeChild(effect);
    }, 3000);
  }

  // Method to get current stream statistics
  getStreamStats() {
    return {
      isLive: this.liveStreamData.isLive,
      viewerCount: this.liveStreamData.viewerCount,
      connectionStatus: this.connectionStatus,
      isConnected: this.isConnected
    };
  }

  // Method to manually trigger events for testing
  triggerTestEvent(eventType) {
    switch (eventType) {
      case 'gift':
        this.simulateGiftReceived();
        break;
      case 'viewer_join':
        this.liveStreamData.viewerCount += Math.floor(Math.random() * 5) + 1;
        this.game.addChatMessage('System', `New viewers joined! Total: ${this.liveStreamData.viewerCount}`);
        break;
      case 'command':
        this.processChatCommand('TestUser', '!spin');
        break;
    }
  }
}

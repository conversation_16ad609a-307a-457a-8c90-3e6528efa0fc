import { Route } from '../../../types/route';
import { IWebcastRoomChatPayload, IWebcastRoomChatRouteResponse } from '@eulerstream/euler-api-sdk';
import { AxiosRequestConfig } from 'axios';
export type SendRoomChatFromEulerRouteParams = IWebcastRoomChatPayload & AxiosRequestConfig;
export declare class SendRoomChatFromEulerRoute extends Route<SendRoomChatFromEulerRouteParams, IWebcastRoomChatRouteResponse> {
    call({ roomId, content, sessionId, options }: {
        roomId: any;
        content: any;
        sessionId: any;
        options: any;
    }): Promise<IWebcastRoomChatRouteResponse>;
}
//# sourceMappingURL=send-room-chat-euler.d.ts.map
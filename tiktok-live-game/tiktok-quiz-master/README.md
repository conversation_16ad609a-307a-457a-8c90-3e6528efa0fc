# 🏆 TikTok Live Quiz Master

Un joc de trivia interactiv complet automatizat pentru streamerii TikTok Live!

## ✨ Caracteristici Principale

### 🤖 **Auto-Pilot System - COMPLET AUTOMATIZAT!**
- **Pornești live-ul, pui username-ul și totul merge automat!**
- Generează întrebări automat din multiple categorii
- Răspunde inteligent la chat-ul publicului
- Gestionează clasamentul și punctajele automat
- Creează atmosferă competitivă și angajantă

### 🎯 **Sistem Quiz Complet**
- **5 categorii**: General Knowledge, Science, Entertainment, Sports, Technology
- **Întrebări cu 4 variante** de răspuns (A, B, C, D)
- **Timer configurabil** pentru fiecare întrebare
- **Sistem de punctaj** automat
- **Clasament live** cu top jucători

### 🏆 **Leaderboard & Statistics**
- Clasament în timp real
- Statistici detaliate de participare
- Tracking pentru răspunsuri corecte
- Rata de participare a publicului

### 💬 **Chat Integration**
- Monitorizare automată a chat-ului TikTok Live
- Comenzi interactive pentru public
- Răspunsuri automate la întrebări
- Detectare răspunsuri prin chat (a, b, c, d)

### 🎥 **Streaming Optimization**
- Integrare perfectă cu OBS Studio
- Mod Browser Source pentru streaming
- Design optimizat pentru live streaming
- Animații fluide și efecte vizuale

## 🚀 Instalare Rapidă

```bash
# Navighează în directorul quiz-ului
cd tiktok-quiz-master

# Instalează dependențele
npm install

# Pornește aplicația
npm run dev
```

Aplicația va fi disponibilă la: `http://localhost:5174/`

## 🎯 Cum să Folosești Auto-Pilot

### 1. **Pornește Aplicația**
```bash
npm run dev
```

### 2. **Configurează Auto-Pilot**
- Apasă butonul **🤖** din centrul ecranului
- Introdu **username-ul tău TikTok**
- Alege **categoria quiz-ului** (General Knowledge, Science, etc.)
- Setează **intervalul întrebărilor** (20-120 secunde)
- Configurează **timpul de răspuns** (15-60 secunde)

### 3. **Pornește Automatizarea**
- Apasă **"🚀 Pornește Auto-Pilot"**
- **GATA! Quiz-ul rulează complet automat!**

### 4. **Pentru OBS/TikTok Live Studio**
- Adaugă **Browser Source** cu URL: `http://localhost:5174/?mode=browser-source`
- Dimensiuni: 1920x1080
- **Quiz-ul va interacționa automat cu publicul!**

## 🎮 Ce Face Auto-Pilot Automat

### 📝 **Managementul Întrebărilor**
- Selectează întrebări random din categoria aleasă
- Afișează întrebarea cu 4 variante de răspuns
- Pornește timer-ul automat
- Trece la următoarea întrebare automat

### 💬 **Răspunsuri Inteligente la Chat**
- Detectează răspunsurile publicului (a, b, c, d)
- Acordă puncte pentru răspunsuri corecte
- Generează comentarii motivaționale
- Anunță răspunsurile corecte

### 🏆 **Gestionarea Clasamentului**
- Actualizează punctajele automat
- Afișează clasamentul live
- Anunță liderii
- Motivează participarea

### 📊 **Statistici Live**
- Urmărește numărul de întrebări
- Calculează rata de participare
- Monitorizează răspunsurile corecte
- Afișează timpul mediu de răspuns

## 🎨 Categorii Disponibile

### 🧠 **General Knowledge**
- Întrebări despre România
- Cultură generală
- Istorie și geografie
- Cunoștințe de bază

### 🔬 **Science**
- Chimie și fizică
- Biologie și astronomie
- Descoperiri științifice
- Fenomene naturale

### 🎬 **Entertainment**
- Social media și tehnologie
- Muzică și filme
- Celebrități și evenimente
- Cultura pop

### ⚽ **Sports**
- Fotbal și sporturi populare
- Reguli și terminologie
- Competiții și recorduri
- Sporturi olimpice

### 💻 **Technology**
- Inteligență artificială
- Companii tech
- Inovații și gadget-uri
- Internet și aplicații

## 📊 Comenzi Chat pentru Public

Publicul poate folosi aceste comenzi:
- `a`, `b`, `c`, `d` - Răspunde la întrebări
- `!quiz` sau `!start` - Pornește o nouă întrebare
- `!skip` - Sare peste întrebarea curentă
- `!leaderboard` sau `!top` - Arată clasamentul
- `!stats` - Afișează statisticile

## 🎥 Integrare OBS Studio

### **Browser Source Setup:**
1. Adaugă **Browser Source** în OBS
2. URL: `http://localhost:5174/?mode=browser-source`
3. Dimensiuni: 1920x1080 (sau 1280x720)
4. FPS: 30

### **Setări Recomandate:**
- **Rezoluție**: 1920x1080
- **Bitrate**: 2500-6000 kbps
- **Encoder**: x264 sau NVENC

## ⚙️ Setări Auto-Pilot

### **Intervale Configurabile:**
- **Întrebări**: 20-120 secunde între întrebări
- **Răspuns**: 15-60 secunde timp de răspuns
- **Dificultate**: Ușor, Mediu, Greu, Mixt

### **Opțiuni Avansate:**
- **Răspunsuri automate**: Activează/dezactivează răspunsurile automate la chat
- **Categoria quiz**: Alege categoria preferată
- **Nivel dificultate**: Controlează complexitatea întrebărilor

## 🎯 Sfaturi pentru Streameri

### **Înainte de Live:**
1. Testează toate funcțiile quiz-ului
2. Configurează Auto-Pilot cu username-ul corect
3. Verifică integrarea OBS
4. Alege categoria potrivită pentru audiența ta

### **În timpul Live-ului:**
1. Lasă Auto-Pilot să gestioneze quiz-ul
2. Încurajează publicul să răspundă în chat
3. Reacționează la clasament și statistici
4. Felicită câștigătorii

### **Pentru Engagement Maxim:**
- Explică publicului cum să răspundă (a, b, c, d)
- Creează suspans înainte de a dezvălui răspunsul
- Felicită participanții activi
- Folosește clasamentul pentru a motiva competiția

## 🔧 Troubleshooting

### **Quiz-ul nu pornește:**
- Verifică că aplicația rulează pe portul corect
- Asigură-te că Auto-Pilot este configurat
- Verifică consola browser-ului pentru erori

### **Răspunsurile nu sunt detectate:**
- Verifică că Auto-Pilot este activ
- Asigură-te că publicul folosește formatul corect (a, b, c, d)
- Verifică că chat-ul este monitorizat

### **OBS Browser Source nu funcționează:**
- Restartează OBS
- Verifică URL-ul Browser Source
- Asigură-te că aplicația rulează

## 📈 Caracteristici Avansate

### **Personalizare Întrebări:**
- Editează `src/main.js` pentru a adăuga întrebări noi
- Modifică categoriile existente
- Ajustează dificultatea întrebărilor

### **Customizare Vizuală:**
- Editează `src/style.css` pentru stiluri personalizate
- Modifică culorile și animațiile
- Adaptează design-ul pentru brandul tău

## 🎊 Demo Live

Pentru a vedea quiz-ul în acțiune:
1. Pornește aplicația: `npm run dev`
2. Deschide: `http://localhost:5174/`
3. Apasă butonul **🤖** și configurează Auto-Pilot
4. Urmărește cum funcționează automat!

---

**🏆 Transformă-ți live-urile TikTok în competiții de trivia captivante! ✨**

*Doar pornești, pui username-ul și quiz-ul rulează complet automat! 🚀*

{"name": "protobufjs", "version": "6.11.4", "versionScheme": "~", "description": "Protocol Buffers for JavaScript (& TypeScript).", "author": "<PERSON> <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "repository": "protobufjs/protobuf.js", "bugs": "https://github.com/dcodeIO/protobuf.js/issues", "homepage": "https://protobufjs.github.io/protobuf.js/", "keywords": ["protobuf", "protocol-buffers", "serialization", "typescript"], "main": "index.js", "types": "index.d.ts", "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}, "scripts": {"bench": "node bench", "build": "npm run build:bundle && npm run build:types", "build:bundle": "gulp --gulpfile scripts/gulpfile.js", "build:types": "node bin/pbts --main --global protobuf --out index.d.ts src/ lib/aspromise/index.js lib/base64/index.js lib/codegen/index.js lib/eventemitter/index.js lib/float/index.js lib/fetch/index.js lib/inquire/index.js lib/path/index.js lib/pool/index.js lib/utf8/index.js", "changelog": "node scripts/changelog -w", "coverage": "nyc tape -r ./lib/tape-adapter tests/*.js tests/node/*.js", "docs": "jsdoc -c config/jsdoc.json -R README.md --verbose --pedantic", "lint": "npm run lint:sources && npm run lint:types", "lint:sources": "eslint \"**/*.js\" -c config/eslint.json", "lint:types": "tslint \"**/*.d.ts\" -e \"**/node_modules/**\" -t stylish -c config/tslint.json", "pages": "node scripts/pages", "prepublish": "node scripts/prepublish", "postinstall": "node scripts/postinstall", "prof": "node bench/prof", "test": "npm run test:sources && npm run test:types", "test:sources": "tape -r ./lib/tape-adapter tests/*.js tests/node/*.js", "test:types": "tsc tests/comp_typescript.ts --lib es2015 --strictNull<PERSON>hecks --experimentalDecorators --emitDecoratorMetadata && tsc tests/data/test.js.ts --lib es2015 --noEmit --strictNullChecks && tsc tests/data/*.ts --lib es2015 --noEmit --strictNullChecks", "make": "npm run lint:sources && npm run build && npm run lint:types && node ./scripts/gentests.js && npm test"}, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^17.0.0", "browserify-wrap": "^1.0.2", "bundle-collapser": "^1.3.0", "chalk": "^4.0.0", "escodegen": "^1.13.0", "eslint": "^8.15.0", "espree": "^7.0.0", "estraverse": "^5.1.0", "gh-pages": "^3.0.0", "git-raw-commits": "^2.0.3", "git-semver-tags": "^4.0.0", "glob": "^7.2.1", "google-protobuf": "^3.11.3", "gulp": "^4.0.2", "gulp-header": "^2.0.9", "gulp-if": "^3.0.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.2", "jaguarjs-jsdoc": "github:dcodeIO/jaguarjs-jsdoc", "jsdoc": "^3.6.3", "minimist": "^1.2.0", "nyc": "^15.0.0", "reflect-metadata": "^0.1.13", "semver": "^7.1.2", "tape": "^5.0.0", "tmp": "^0.2.0", "tslint": "^6.0.0", "typescript": "^3.7.5", "uglify-js": "^3.7.7", "vinyl-buffer": "^1.0.1", "vinyl-fs": "^3.0.3", "vinyl-source-stream": "^2.0.0"}, "cliDependencies": ["semver", "chalk", "glob", "jsdoc", "minimist", "tmp", "uglify-js", "espree", "escodegen", "estraverse"], "files": ["index.js", "index.d.ts", "light.d.ts", "light.js", "minimal.d.ts", "minimal.js", "package-lock.json", "tsconfig.json", "scripts/postinstall.js", "bin/**", "cli/**", "dist/**", "ext/**", "google/**", "src/**"]}
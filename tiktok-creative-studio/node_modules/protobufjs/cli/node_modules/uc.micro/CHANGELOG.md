1.0.6 / 2019-01-31
------------------

- Unicode update to 10.0.0.
- Fixed `Z` content (added missed line and paragraph seperators), #10.


1.0.5 / 2018-01-26
------------------

- Remove outdated license info from readme (missed in previous update).


1.0.4 / 2018-01-26
------------------

- Unicode update to 10.0.0.
- Clarified license, should be MIT, #6.


1.0.3 / 2016-09-14
------------------

- Unicode update to 9.0.0.
- Rewrite update script (use npm instead of Makefile).
- Added integrity tests.


1.0.2 / 2015-06-24
------------------

- License info clarify, #3.


1.0.1 / 2015-05-30
------------------

- Update to Unicode 8.+.
- Also automatically fix possible ReDOS in `Any`, if source used to generate
  patterns like `(Any)+`.


1.0.0 / 2015-03-10
------------------

- Export all in index.js.


0.1.0 / 2015-02-22
------------------

- First release.

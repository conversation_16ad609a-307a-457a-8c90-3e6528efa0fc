syntax = "proto2";

package google.protobuf;

message FileDescriptorSet {

    repeated FileDescriptorProto file = 1;
}

message FileDescriptorProto {

    optional string name = 1;
    optional string package = 2;
    repeated string dependency = 3;
    repeated int32 public_dependency = 10;
    repeated int32 weak_dependency = 11;
    repeated DescriptorProto message_type = 4;
    repeated EnumDescriptorProto enum_type = 5;
    repeated ServiceDescriptorProto service = 6;
    repeated FieldDescriptorProto extension = 7;
    optional FileOptions options = 8;
    optional SourceCodeInfo source_code_info = 9;
    optional string syntax = 12;
}

message DescriptorProto {

    optional string name = 1;
    repeated FieldDescriptorProto field = 2;
    repeated FieldDescriptorProto extension = 6;
    repeated DescriptorProto nested_type = 3;
    repeated EnumDescriptorProto enum_type = 4;
    repeated ExtensionRange extension_range = 5;
    repeated OneofDescriptorProto oneof_decl = 8;
    optional MessageOptions options = 7;
    repeated ReservedRange reserved_range = 9;
    repeated string reserved_name = 10;

    message ExtensionRange {

        optional int32 start = 1;
        optional int32 end = 2;
    }

    message ReservedRange {

        optional int32 start = 1;
        optional int32 end = 2;
    }
}

message FieldDescriptorProto {

    optional string name = 1;
    optional int32 number = 3;
    optional Label label = 4;
    optional Type type = 5;
    optional string type_name = 6;
    optional string extendee = 2;
    optional string default_value = 7;
    optional int32 oneof_index = 9;
    optional string json_name = 10;
    optional FieldOptions options = 8;

    enum Type {

        TYPE_DOUBLE = 1;
        TYPE_FLOAT = 2;
        TYPE_INT64 = 3;
        TYPE_UINT64 = 4;
        TYPE_INT32 = 5;
        TYPE_FIXED64 = 6;
        TYPE_FIXED32 = 7;
        TYPE_BOOL = 8;
        TYPE_STRING = 9;
        TYPE_GROUP = 10;
        TYPE_MESSAGE = 11;
        TYPE_BYTES = 12;
        TYPE_UINT32 = 13;
        TYPE_ENUM = 14;
        TYPE_SFIXED32 = 15;
        TYPE_SFIXED64 = 16;
        TYPE_SINT32 = 17;
        TYPE_SINT64 = 18;
    }

    enum Label {

        LABEL_OPTIONAL = 1;
        LABEL_REQUIRED = 2;
        LABEL_REPEATED = 3;
    }
}

message OneofDescriptorProto {

    optional string name = 1;
    optional OneofOptions options = 2;
}

message EnumDescriptorProto {

    optional string name = 1;
    repeated EnumValueDescriptorProto value = 2;
    optional EnumOptions options = 3;
}

message EnumValueDescriptorProto {

    optional string name = 1;
    optional int32 number = 2;
    optional EnumValueOptions options = 3;
}

message ServiceDescriptorProto {

    optional string name = 1;
    repeated MethodDescriptorProto method = 2;
    optional ServiceOptions options = 3;
}

message MethodDescriptorProto {

    optional string name = 1;
    optional string input_type = 2;
    optional string output_type = 3;
    optional MethodOptions options = 4;
    optional bool client_streaming = 5;
    optional bool server_streaming = 6;
}

message FileOptions {

    optional string java_package = 1;
    optional string java_outer_classname = 8;
    optional bool java_multiple_files = 10;
    optional bool java_generate_equals_and_hash = 20 [deprecated=true];
    optional bool java_string_check_utf8 = 27;
    optional OptimizeMode optimize_for = 9 [default=SPEED];
    optional string go_package = 11;
    optional bool cc_generic_services = 16;
    optional bool java_generic_services = 17;
    optional bool py_generic_services = 18;
    optional bool deprecated = 23;
    optional bool cc_enable_arenas = 31;
    optional string objc_class_prefix = 36;
    optional string csharp_namespace = 37;
    repeated UninterpretedOption uninterpreted_option = 999;

    enum OptimizeMode {

        SPEED = 1;
        CODE_SIZE = 2;
        LITE_RUNTIME = 3;
    }

    extensions 1000 to max;

    reserved 38;
}

message MessageOptions {

    optional bool message_set_wire_format = 1;
    optional bool no_standard_descriptor_accessor = 2;
    optional bool deprecated = 3;
    optional bool map_entry = 7;
    repeated UninterpretedOption uninterpreted_option = 999;

    extensions 1000 to max;

    reserved 8;
}

message FieldOptions {

    optional CType ctype = 1 [default=STRING];
    optional bool packed = 2;
    optional JSType jstype = 6 [default=JS_NORMAL];
    optional bool lazy = 5;
    optional bool deprecated = 3;
    optional bool weak = 10;
    repeated UninterpretedOption uninterpreted_option = 999;

    enum CType {

        STRING = 0;
        CORD = 1;
        STRING_PIECE = 2;
    }

    enum JSType {

        JS_NORMAL = 0;
        JS_STRING = 1;
        JS_NUMBER = 2;
    }

    extensions 1000 to max;

    reserved 4;
}

message OneofOptions {

    repeated UninterpretedOption uninterpreted_option = 999;

    extensions 1000 to max;
}

message EnumOptions {

    optional bool allow_alias = 2;
    optional bool deprecated = 3;
    repeated UninterpretedOption uninterpreted_option = 999;

    extensions 1000 to max;
}

message EnumValueOptions {

    optional bool deprecated = 1;
    repeated UninterpretedOption uninterpreted_option = 999;

    extensions 1000 to max;
}

message ServiceOptions {

    optional bool deprecated = 33;
    repeated UninterpretedOption uninterpreted_option = 999;

    extensions 1000 to max;
}

message MethodOptions {

    optional bool deprecated = 33;
    repeated UninterpretedOption uninterpreted_option = 999;

    extensions 1000 to max;
}

message UninterpretedOption {

    repeated NamePart name = 2;
    optional string identifier_value = 3;
    optional uint64 positive_int_value = 4;
    optional int64 negative_int_value = 5;
    optional double double_value = 6;
    optional bytes string_value = 7;
    optional string aggregate_value = 8;

    message NamePart {

        required string name_part = 1;
        required bool is_extension = 2;
    }
}

message SourceCodeInfo {

    repeated Location location = 1;

    message Location {

        repeated int32 path = 1 [packed=true];
        repeated int32 span = 2 [packed=true];
        optional string leading_comments = 3;
        optional string trailing_comments = 4;
        repeated string leading_detached_comments = 6;
    }
}

message GeneratedCodeInfo {

    repeated Annotation annotation = 1;

    message Annotation {

        repeated int32 path = 1 [packed=true];
        optional string source_file = 2;
        optional int32 begin = 3;
        optional int32 end = 4;
    }
}

import './style.css'
import { RealTikTokConnector, TikTokLiveUtils } from './real-tiktok-connector.js'

// TikTok Live Casino - Main Application
class TikTokLiveCasino {
  constructor() {
    this.currentSection = 'roulette-section';
    this.playerChips = 0; // Începe cu 0, primește credite prin acțiuni
    this.totalGames = 0;
    this.totalWinnings = 0;
    this.biggestWin = 0;
    this.wins = 0;
    this.viewerCount = 0;
    this.leaderboard = new Map();
    this.chatMessages = [];
    this.autoPilot = null;

    // Real TikTok Live Integration
    this.realTikTokConnector = new RealTikTokConnector(this);
    this.isReallyConnected = false;

    // SISTEM ECONOMIC CASINO
    this.players = new Map(); // Stochează datele fiecărui jucător
    this.creditSystem = {
      tapCredits: 100,        // Credite pentru 3000 tap-uri
      shareCredits: 100,      // Credite pentru 30 distribuiri
      maxTapsPerUser: 3000,   // Limită tap-uri per utilizator
      maxSharesPerUser: 30,   // Limită distribuiri per utilizator

      // Credite pentru cadouri (retrăgabile)
      giftCredits: {
        'rose': 500,
        'heart': 200,
        'diamond': 1000,
        'perfume': 300,
        'sports_car': 2000,
        'yacht': 5000,
        'default': 150
      }
    };

    // STRATEGIA DE PROFIT
    this.profitStrategy = {
      // Șanse de câștig bazate pe tipul utilizatorului
      freeUserWinChance: 0.15,      // 15% pentru utilizatori fără cadouri
      smallGiftWinChance: 0.25,     // 25% pentru cadouri mici
      bigGiftWinChance: 0.35,       // 35% pentru cadouri mari
      bonusWinChance: 0.60,         // 60% bonus ocazional

      // Praguri pentru categorii
      smallGiftThreshold: 500,      // Sub 500 credite = cadou mic
      bigGiftThreshold: 1000,       // Peste 1000 credite = cadou mare

      // Frecvența bonus-urilor (să-i captiveze)
      bonusFrequency: 0.1,          // 10% șanse pentru bonus

      // Multiplicatori de câștig
      maxWinMultiplier: 5,          // Câștig maxim 5x
      averageWinMultiplier: 2       // Câștig mediu 2x
    };

    // Game States
    this.roulette = {
      isSpinning: false,
      currentBets: new Map(),
      lastNumber: null,
      wheel: null
    };

    this.blackjack = {
      isPlaying: false,
      dealerCards: [],
      playerCards: [],
      deck: [],
      currentBet: 0
    };

    this.slots = {
      isSpinning: false,
      symbols: ['🍒', '🍋', '🍊', '🍇', '🍎', '💎', '⭐', '🔔'],
      payouts: {
        '🍒🍒🍒': 100,
        '🍋🍋🍋': 50,
        '🍊🍊🍊': 25,
        '🍇🍇🍇': 75,
        '🍎🍎🍎': 40,
        '💎💎💎': 500,
        '⭐⭐⭐': 1000,
        '🔔🔔🔔': 200
      }
    };

    this.dice = {
      isRolling: false,
      currentBets: new Map(),
      lastRoll: [1, 1]
    };

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.initializeRouletteWheel();
    this.generateRouletteNumbers();
    this.updateDisplay();
    this.simulateViewerActivity();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Roulette
    document.getElementById('spin-roulette').addEventListener('click', () => this.spinRoulette());
    document.getElementById('clear-bets').addEventListener('click', () => this.clearRouletteBets());
    document.getElementById('max-bet').addEventListener('click', () => this.maxBet());

    // Blackjack
    document.getElementById('deal-cards').addEventListener('click', () => this.dealBlackjack());
    document.getElementById('hit-card').addEventListener('click', () => this.hitBlackjack());
    document.getElementById('stand-cards').addEventListener('click', () => this.standBlackjack());
    document.getElementById('double-down').addEventListener('click', () => this.doubleDownBlackjack());

    // Slots
    document.getElementById('spin-slots').addEventListener('click', () => this.spinSlots());

    // Dice
    document.getElementById('roll-dice').addEventListener('click', () => this.rollDice());

    // Chat
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Auto-pilot
    document.getElementById('autopilot-toggle').addEventListener('click', () => this.toggleAutoPilot());
  }

  switchSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.casino-section').forEach(section => {
      section.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  // Roulette Game Logic
  initializeRouletteWheel() {
    const canvas = document.getElementById('roulette-wheel');
    const ctx = canvas.getContext('2d');
    this.roulette.wheel = { canvas, ctx };
    this.drawRouletteWheel();
  }

  drawRouletteWheel() {
    const { canvas, ctx } = this.roulette.wheel;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 180;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Roulette numbers and colors
    const numbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];

    const anglePerNumber = (2 * Math.PI) / numbers.length;

    numbers.forEach((number, index) => {
      const startAngle = index * anglePerNumber;
      const endAngle = (index + 1) * anglePerNumber;

      // Determine color
      let color = '#228b22'; // Green for 0
      if (number !== 0) {
        color = redNumbers.includes(number) ? '#dc143c' : '#000000';
      }

      // Draw segment
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fill();

      // Draw border
      ctx.strokeStyle = '#ffd700';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw number
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(startAngle + anglePerNumber / 2);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Inter';
      ctx.textAlign = 'center';
      ctx.fillText(number.toString(), radius * 0.8, 5);
      ctx.restore();
    });

    // Draw center
    ctx.fillStyle = '#ffd700';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI);
    ctx.fill();
  }

  generateRouletteNumbers() {
    const numberGrid = document.querySelector('.number-grid');
    const numbers = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36];
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];

    numberGrid.innerHTML = '';

    numbers.forEach(number => {
      const btn = document.createElement('button');
      btn.className = 'number-btn';
      btn.textContent = number;
      btn.dataset.number = number;

      if (number === 0) {
        btn.classList.add('zero');
      } else if (redNumbers.includes(number)) {
        btn.classList.add('red');
      } else {
        btn.classList.add('black');
      }

      btn.addEventListener('click', () => this.placeBet('number', number));
      numberGrid.appendChild(btn);
    });

    // Setup outside bets
    document.querySelectorAll('.bet-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const betType = e.target.dataset.bet;
        this.placeBet('outside', betType);
      });
    });
  }

  placeBet(type, value) {
    const betAmount = parseInt(document.getElementById('bet-amount').value) || 10;

    if (betAmount > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    const betKey = `${type}-${value}`;
    const currentBet = this.roulette.currentBets.get(betKey) || 0;
    this.roulette.currentBets.set(betKey, currentBet + betAmount);
    this.playerChips -= betAmount;

    this.updateDisplay();
    this.addChatMessage('Casino', `Bet placed: ${betAmount} chips on ${value}`);
  }

  spinRoulette(forcedUsername = null) {
    if (this.roulette.isSpinning || this.roulette.currentBets.size === 0) return;

    this.roulette.isSpinning = true;
    document.getElementById('spin-roulette').disabled = true;

    // Animate wheel
    const ball = document.getElementById('roulette-ball');
    ball.style.animation = 'none';
    setTimeout(() => {
      ball.style.animation = 'spin 3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }, 10);

    // Generate winning number cu STRATEGIA DE PROFIT
    setTimeout(() => {
      const numbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];

      // Calculează numărul câștigător bazat pe strategia de profit
      let winningNumber;

      // Verifică dacă avem pariuri și calculează șansele
      const mainBettor = forcedUsername || Array.from(this.roulette.currentBets.keys())[0]?.split('-')[0];

      if (mainBettor) {
        const winChance = this.calculateWinChance(mainBettor);
        const shouldWin = Math.random() < winChance;

        if (shouldWin) {
          // Alege un număr care să câștige pentru jucător
          winningNumber = this.selectWinningNumber(true);
          this.addChatMessage('🍀 NOROC', `Norocul îi surâde lui ${mainBettor}!`);
        } else {
          // Alege un număr care să piardă
          winningNumber = this.selectWinningNumber(false);
        }
      } else {
        // Număr aleatoriu dacă nu avem jucător specific
        winningNumber = numbers[Math.floor(Math.random() * numbers.length)];
      }

      this.roulette.lastNumber = winningNumber;

      this.processRouletteWin(winningNumber);
      this.roulette.isSpinning = false;
      document.getElementById('spin-roulette').disabled = false;
    }, 3000);
  }

  // Selectează numărul câștigător bazat pe strategia de profit
  selectWinningNumber(shouldWin) {
    const numbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];

    if (shouldWin) {
      // Încearcă să găsească un număr pe care jucătorul a pariat
      for (const [betKey, betAmount] of this.roulette.currentBets) {
        const [type, value] = betKey.split('-');
        if (type === 'number') {
          const betNumber = parseInt(value);
          if (numbers.includes(betNumber)) {
            return betNumber; // Returnează numărul pe care a pariat
          }
        }
      }

      // Dacă nu a pariat pe numere specifice, încearcă să câștige la pariurile externe
      const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];

      for (const [betKey, betAmount] of this.roulette.currentBets) {
        const [type, value] = betKey.split('-');
        if (type === 'outside') {
          if (value === 'red') {
            return redNumbers[Math.floor(Math.random() * redNumbers.length)];
          } else if (value === 'black') {
            const blackNumbers = numbers.filter(n => n !== 0 && !redNumbers.includes(n));
            return blackNumbers[Math.floor(Math.random() * blackNumbers.length)];
          } else if (value === 'even') {
            const evenNumbers = numbers.filter(n => n !== 0 && n % 2 === 0);
            return evenNumbers[Math.floor(Math.random() * evenNumbers.length)];
          } else if (value === 'odd') {
            const oddNumbers = numbers.filter(n => n !== 0 && n % 2 === 1);
            return oddNumbers[Math.floor(Math.random() * oddNumbers.length)];
          }
        }
      }
    }

    // Returnează un număr aleatoriu (pentru pierdere sau fallback)
    return numbers[Math.floor(Math.random() * numbers.length)];
  }

  processRouletteWin(winningNumber) {
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
    let totalWin = 0;

    this.roulette.currentBets.forEach((betAmount, betKey) => {
      const [type, value] = betKey.split('-');
      let isWin = false;
      let payout = 0;

      if (type === 'number' && parseInt(value) === winningNumber) {
        isWin = true;
        payout = betAmount * 35; // 35:1 payout
      } else if (type === 'outside') {
        switch (value) {
          case 'red':
            isWin = redNumbers.includes(winningNumber);
            payout = betAmount * 2;
            break;
          case 'black':
            isWin = !redNumbers.includes(winningNumber) && winningNumber !== 0;
            payout = betAmount * 2;
            break;
          case 'even':
            isWin = winningNumber % 2 === 0 && winningNumber !== 0;
            payout = betAmount * 2;
            break;
          case 'odd':
            isWin = winningNumber % 2 === 1;
            payout = betAmount * 2;
            break;
          case 'low':
            isWin = winningNumber >= 1 && winningNumber <= 18;
            payout = betAmount * 2;
            break;
          case 'high':
            isWin = winningNumber >= 19 && winningNumber <= 36;
            payout = betAmount * 2;
            break;
        }
      }

      if (isWin) {
        totalWin += payout;
      }
    });

    this.playerChips += totalWin;
    this.totalGames++;

    if (totalWin > 0) {
      this.wins++;
      this.totalWinnings += totalWin;
      if (totalWin > this.biggestWin) {
        this.biggestWin = totalWin;
      }
      this.addChatMessage('Casino', `🎉 Winner! Number ${winningNumber}! You won ${totalWin} chips!`);
    } else {
      this.addChatMessage('Casino', `Number ${winningNumber}. Better luck next time!`);
    }

    this.roulette.currentBets.clear();
    this.updateDisplay();
  }

  clearRouletteBets() {
    // Return chips
    let totalReturn = 0;
    this.roulette.currentBets.forEach(amount => {
      totalReturn += amount;
    });
    this.playerChips += totalReturn;
    this.roulette.currentBets.clear();
    this.updateDisplay();
    this.addChatMessage('Casino', 'All bets cleared!');
  }

  maxBet() {
    document.getElementById('bet-amount').value = Math.min(100, this.playerChips);
  }

  // Blackjack Game Logic
  createDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const deck = [];

    suits.forEach(suit => {
      ranks.forEach(rank => {
        deck.push({
          suit,
          rank,
          value: rank === 'A' ? 11 : (rank === 'J' || rank === 'Q' || rank === 'K') ? 10 : parseInt(rank)
        });
      });
    });

    // Shuffle deck
    for (let i = deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [deck[i], deck[j]] = [deck[j], deck[i]];
    }

    return deck;
  }

  dealBlackjack() {
    const bet = parseInt(document.getElementById('blackjack-bet').value) || 10;

    if (bet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    this.blackjack.currentBet = bet;
    this.playerChips -= bet;
    this.blackjack.isPlaying = true;
    this.blackjack.deck = this.createDeck();
    this.blackjack.dealerCards = [];
    this.blackjack.playerCards = [];

    // Deal initial cards
    this.blackjack.playerCards.push(this.blackjack.deck.pop());
    this.blackjack.dealerCards.push(this.blackjack.deck.pop());
    this.blackjack.playerCards.push(this.blackjack.deck.pop());
    this.blackjack.dealerCards.push(this.blackjack.deck.pop());

    this.updateBlackjackDisplay();
    this.updateBlackjackControls();

    // Check for blackjack
    if (this.calculateBlackjackScore(this.blackjack.playerCards) === 21) {
      this.endBlackjack('blackjack');
    }
  }

  hitBlackjack() {
    if (!this.blackjack.isPlaying) return;

    this.blackjack.playerCards.push(this.blackjack.deck.pop());
    this.updateBlackjackDisplay();

    const playerScore = this.calculateBlackjackScore(this.blackjack.playerCards);
    if (playerScore > 21) {
      this.endBlackjack('bust');
    } else if (playerScore === 21) {
      this.standBlackjack();
    }
  }

  standBlackjack() {
    if (!this.blackjack.isPlaying) return;

    // Dealer plays
    while (this.calculateBlackjackScore(this.blackjack.dealerCards) < 17) {
      this.blackjack.dealerCards.push(this.blackjack.deck.pop());
    }

    this.updateBlackjackDisplay();

    const playerScore = this.calculateBlackjackScore(this.blackjack.playerCards);
    const dealerScore = this.calculateBlackjackScore(this.blackjack.dealerCards);

    if (dealerScore > 21) {
      this.endBlackjack('dealer-bust');
    } else if (playerScore > dealerScore) {
      this.endBlackjack('win');
    } else if (playerScore < dealerScore) {
      this.endBlackjack('lose');
    } else {
      this.endBlackjack('push');
    }
  }

  doubleDownBlackjack() {
    if (!this.blackjack.isPlaying || this.blackjack.playerCards.length !== 2) return;

    if (this.blackjack.currentBet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips to double down!');
      return;
    }

    this.playerChips -= this.blackjack.currentBet;
    this.blackjack.currentBet *= 2;

    this.hitBlackjack();
    if (this.blackjack.isPlaying) {
      this.standBlackjack();
    }
  }

  calculateBlackjackScore(cards) {
    let score = 0;
    let aces = 0;

    cards.forEach(card => {
      if (card.rank === 'A') {
        aces++;
        score += 11;
      } else {
        score += card.value;
      }
    });

    while (score > 21 && aces > 0) {
      score -= 10;
      aces--;
    }

    return score;
  }

  updateBlackjackDisplay() {
    const dealerArea = document.getElementById('dealer-cards');
    const playerArea = document.getElementById('player-cards');

    // Show dealer cards (hide first card if game is active)
    dealerArea.innerHTML = '';
    this.blackjack.dealerCards.forEach((card, index) => {
      const cardDiv = document.createElement('div');
      cardDiv.className = 'card';

      if (this.blackjack.isPlaying && index === 0) {
        cardDiv.classList.add('back');
        cardDiv.textContent = '?';
      } else {
        if (card.suit === '♥' || card.suit === '♦') {
          cardDiv.classList.add('red');
        }
        cardDiv.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
      }

      dealerArea.appendChild(cardDiv);
    });

    // Show player cards
    playerArea.innerHTML = '';
    this.blackjack.playerCards.forEach(card => {
      const cardDiv = document.createElement('div');
      cardDiv.className = 'card';

      if (card.suit === '♥' || card.suit === '♦') {
        cardDiv.classList.add('red');
      }
      cardDiv.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
      playerArea.appendChild(cardDiv);
    });

    // Update scores
    const dealerScore = this.blackjack.isPlaying ? '?' : this.calculateBlackjackScore(this.blackjack.dealerCards);
    const playerScore = this.calculateBlackjackScore(this.blackjack.playerCards);

    document.getElementById('dealer-score').textContent = `Score: ${dealerScore}`;
    document.getElementById('player-score').textContent = `Score: ${playerScore}`;
  }

  updateBlackjackControls() {
    const isPlaying = this.blackjack.isPlaying;
    const canDoubleDown = isPlaying && this.blackjack.playerCards.length === 2;

    document.getElementById('deal-cards').disabled = isPlaying;
    document.getElementById('hit-card').disabled = !isPlaying;
    document.getElementById('stand-cards').disabled = !isPlaying;
    document.getElementById('double-down').disabled = !canDoubleDown;
  }

  endBlackjack(result) {
    this.blackjack.isPlaying = false;
    this.totalGames++;

    let winAmount = 0;
    let message = '';

    switch (result) {
      case 'blackjack':
        winAmount = Math.floor(this.blackjack.currentBet * 2.5);
        message = '🎉 Blackjack! You win!';
        this.wins++;
        break;
      case 'win':
      case 'dealer-bust':
        winAmount = this.blackjack.currentBet * 2;
        message = '🎉 You win!';
        this.wins++;
        break;
      case 'push':
        winAmount = this.blackjack.currentBet;
        message = 'Push! Bet returned.';
        break;
      case 'lose':
      case 'bust':
        winAmount = 0;
        message = 'You lose!';
        break;
    }

    this.playerChips += winAmount;
    this.totalWinnings += winAmount - this.blackjack.currentBet;

    if (winAmount > this.biggestWin) {
      this.biggestWin = winAmount;
    }

    document.getElementById('blackjack-result').textContent = message;
    this.addChatMessage('Casino', message);
    this.updateBlackjackDisplay();
    this.updateBlackjackControls();
    this.updateDisplay();
  }

  // Slots Game Logic
  spinSlots() {
    if (this.slots.isSpinning) return;

    const bet = parseInt(document.getElementById('slots-bet').value) || 10;

    if (bet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    this.slots.isSpinning = true;
    this.playerChips -= bet;
    document.getElementById('spin-slots').disabled = true;

    // Animate reels
    const reels = document.querySelectorAll('.reel');
    reels.forEach(reel => {
      reel.classList.add('spinning');
    });

    // Generate results
    setTimeout(() => {
      const results = [];
      reels.forEach((reel, index) => {
        const randomSymbol = this.slots.symbols[Math.floor(Math.random() * this.slots.symbols.length)];
        results.push(randomSymbol);

        reel.innerHTML = `<div class="symbol">${randomSymbol}</div>`;
        reel.classList.remove('spinning');
      });

      this.processSlotsWin(results, bet);
      this.slots.isSpinning = false;
      document.getElementById('spin-slots').disabled = false;
    }, 2000);
  }

  processSlotsWin(results, bet) {
    const combination = results.join('');
    const payout = this.slots.payouts[combination];

    this.totalGames++;

    if (payout) {
      const winAmount = bet * payout;
      this.playerChips += winAmount;
      this.wins++;
      this.totalWinnings += winAmount - bet;

      if (winAmount > this.biggestWin) {
        this.biggestWin = winAmount;
      }

      this.addChatMessage('Casino', `🎰 JACKPOT! ${combination} - You won ${winAmount} chips!`);

      // Special effects for big wins
      if (payout >= 500) {
        document.querySelector('.slot-machine').classList.add('jackpot-animation');
        setTimeout(() => {
          document.querySelector('.slot-machine').classList.remove('jackpot-animation');
        }, 1500);
      }
    } else {
      // Check for partial matches
      const uniqueSymbols = [...new Set(results)];
      if (uniqueSymbols.length === 2) {
        const smallWin = Math.floor(bet * 0.5);
        this.playerChips += smallWin;
        this.addChatMessage('Casino', `Two matching symbols! Small win: ${smallWin} chips`);
      } else {
        this.addChatMessage('Casino', `${combination} - Try again!`);
      }
    }

    this.updateDisplay();
  }

  // Dice Game Logic
  rollDice() {
    if (this.dice.isRolling) return;

    const bet = parseInt(document.getElementById('dice-bet').value) || 10;

    if (bet > this.playerChips) {
      this.addChatMessage('Casino', 'Insufficient chips!');
      return;
    }

    this.dice.isRolling = true;
    this.playerChips -= bet;
    document.getElementById('roll-dice').disabled = true;

    // Animate dice
    const dice1 = document.getElementById('dice1');
    const dice2 = document.getElementById('dice2');

    dice1.classList.add('rolling');
    dice2.classList.add('rolling');

    // Generate results
    setTimeout(() => {
      const roll1 = Math.floor(Math.random() * 6) + 1;
      const roll2 = Math.floor(Math.random() * 6) + 1;
      const total = roll1 + roll2;

      this.dice.lastRoll = [roll1, roll2];

      // Update dice display
      const diceSymbols = ['⚀', '⚁', '⚂', '⚃', '⚄', '⚅'];
      dice1.textContent = diceSymbols[roll1 - 1];
      dice2.textContent = diceSymbols[roll2 - 1];
      document.getElementById('dice-total').textContent = total;

      dice1.classList.remove('rolling');
      dice2.classList.remove('rolling');

      this.processDiceWin(total, roll1, roll2, bet);
      this.dice.isRolling = false;
      document.getElementById('roll-dice').disabled = false;
    }, 1000);
  }

  processDiceWin(total, roll1, roll2, bet) {
    this.totalGames++;
    let winAmount = 0;
    let message = `Rolled ${roll1} and ${roll2} (Total: ${total})`;

    // Check active bets (simulate player bets)
    const betTypes = ['under7', 'seven', 'over7', 'doubles'];
    const activeBet = betTypes[Math.floor(Math.random() * betTypes.length)];

    switch (activeBet) {
      case 'under7':
        if (total < 7) {
          winAmount = bet * 2;
          message += ' - Under 7 wins!';
          this.wins++;
        } else {
          message += ' - Under 7 loses!';
        }
        break;
      case 'seven':
        if (total === 7) {
          winAmount = bet * 5;
          message += ' - Lucky Seven!';
          this.wins++;
        } else {
          message += ' - Seven loses!';
        }
        break;
      case 'over7':
        if (total > 7) {
          winAmount = bet * 2;
          message += ' - Over 7 wins!';
          this.wins++;
        } else {
          message += ' - Over 7 loses!';
        }
        break;
      case 'doubles':
        if (roll1 === roll2) {
          winAmount = bet * 6;
          message += ' - Doubles win!';
          this.wins++;
        } else {
          message += ' - Doubles lose!';
        }
        break;
    }

    this.playerChips += winAmount;
    this.totalWinnings += winAmount - bet;

    if (winAmount > this.biggestWin) {
      this.biggestWin = winAmount;
    }

    this.addChatMessage('Casino', message);
    this.updateDisplay();
  }

  // Chat System
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';

      // Process chat commands
      this.processChatCommand('You', message);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    // Keep only last 50 messages
    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }

    this.chatMessages.push({ username, message, timestamp });
  }

  processChatCommand(username, message) {
    if (!message) return;

    const msg = message.toLowerCase().trim();

    // COMENZI PENTRU CREDITE GRATUITE
    if (msg.includes('!tap') || msg.includes('!like')) {
      this.handleTapAction(username);
      return;
    } else if (msg.includes('!share') || msg.includes('!distribuie')) {
      this.handleShareAction(username);
      return;
    } else if (msg.includes('!credite') || msg.includes('!balance')) {
      const player = this.getOrCreatePlayer(username);
      this.addChatMessage('💰 BALANCE',
        `${username}: ${player.chips} credite totale | ` +
        `${player.redeemableChips} retrăgabile | ` +
        `${player.freeChips} gratuite`
      );
      return;
    } else if (msg.includes('!retrage') || msg.includes('!withdraw')) {
      this.handleWithdrawRequest(username);
      return;
    }

    // Comenzi pentru Casino Live
    if (msg.includes('!spin') || msg.includes('!roulette') || msg.includes('!ruletă')) {
      this.switchSection('roulette-section');
      if (!this.roulette.isSpinning) {
        this.addChatMessage('🎰 COMMAND', `${username} a cerut să învârt ruleta!`);
        setTimeout(() => this.spinRoulette(username), 1000);
      }
    } else if (msg.includes('!blackjack') || msg.includes('!cards') || msg.includes('!cărți')) {
      this.switchSection('blackjack-section');
      if (!this.blackjack.isPlaying) {
        this.addChatMessage('🃏 COMMAND', `${username} vrea să joace blackjack!`);
        setTimeout(() => this.dealBlackjack(username), 1000);
      }
    } else if (msg.includes('!slots') || msg.includes('!slot') || msg.includes('!păcănele')) {
      this.switchSection('slots-section');
      if (!this.slots.isSpinning) {
        this.addChatMessage('🎰 COMMAND', `${username} joacă la păcănele!`);
        setTimeout(() => this.spinSlots(username), 1000);
      }
    } else if (msg.includes('!dice') || msg.includes('!roll') || msg.includes('!zaruri')) {
      this.switchSection('dice-section');
      if (!this.dice.isRolling) {
        this.addChatMessage('🎲 COMMAND', `${username} aruncă zarurile!`);
        setTimeout(() => this.rollDice(username), 1000);
      }
    } else if (msg.includes('!stats') || msg.includes('!statistici')) {
      this.switchSection('stats-section');
      this.addChatMessage('📊 COMMAND', `${username} vrea să vadă statisticile!`);
    } else if (msg.includes('!chips') || msg.includes('!jetoane')) {
      const player = this.getOrCreatePlayer(username);
      this.addChatMessage('💰 CHIPS', `${username}, ai ${player.chips} credite!`);
    } else if (msg.includes('!bet') || msg.includes('!pariu')) {
      // Detectare pariuri pentru ruletă
      const betMatch = msg.match(/!bet\s+(\d+)\s+(\w+)/);
      if (betMatch && !this.roulette.isSpinning) {
        const amount = parseInt(betMatch[1]);
        const target = betMatch[2];
        this.placeBetForPlayer(username, target, amount);
      }
    } else if (msg.includes('!help') || msg.includes('!comenzi')) {
      this.showHelpCommands(username);
    }

    // Comenzi pentru blackjack
    if (this.blackjack.isPlaying && this.blackjack.currentPlayer === username) {
      if (msg.includes('!hit') || msg.includes('!carte')) {
        this.addChatMessage('🃏 HIT', `${username} cere o carte!`);
        this.hitBlackjack();
      } else if (msg.includes('!stand') || msg.includes('!stai')) {
        this.addChatMessage('🃏 STAND', `${username} se oprește!`);
        this.standBlackjack();
      }
    }

    // Comenzi speciale pentru streameri
    if (username.toLowerCase() === this.autoPilot?.username?.toLowerCase()) {
      if (msg.includes('!stop') || msg.includes('!oprește')) {
        this.addChatMessage('⏹️ STREAMER', 'Streamer-ul a oprit casino-ul!');
        if (this.autoPilot) {
          this.stopAutoPilot();
        }
      }

      if (msg.includes('!bonus') || msg.includes('!cadou')) {
        this.playerChips += 500;
        this.addChatMessage('🎁 BONUS', `Streamer-ul a oferit 500 jetoane bonus!`);
        this.updateDisplay();
      }
    }
  }

  // Display Updates
  updateDisplay() {
    document.getElementById('total-chips').textContent = `💰 ${this.playerChips} chips`;
    document.getElementById('viewer-count').textContent = `${this.viewerCount} viewers`;

    // Update statistics
    document.getElementById('total-games').textContent = this.totalGames;
    document.getElementById('total-winnings').textContent = this.totalWinnings;
    document.getElementById('biggest-win').textContent = this.biggestWin;

    const winRate = this.totalGames > 0 ? Math.round((this.wins / this.totalGames) * 100) : 0;
    document.getElementById('win-rate').textContent = `${winRate}%`;

    this.updateLeaderboard();
  }

  updateLeaderboard() {
    // Add current player to leaderboard
    this.leaderboard.set('You', this.playerChips);

    const leaderboardList = document.getElementById('casino-leaderboard');
    const sortedEntries = Array.from(this.leaderboard.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    leaderboardList.innerHTML = sortedEntries.map((entry, index) => `
      <div class="leaderboard-entry">
        <div class="leaderboard-rank">${index + 1}</div>
        <div class="leaderboard-name">${entry[0]}</div>
        <div class="leaderboard-chips">${entry[1]} chips</div>
      </div>
    `).join('');
  }

  // Viewer Activity Simulation
  simulateViewerActivity() {
    // Update viewer count
    setInterval(() => {
      const change = Math.floor(Math.random() * 20) - 10;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      this.updateDisplay();
    }, 10000);

    // Random chat messages
    setInterval(() => {
      if (Math.random() < 0.4) {
        this.simulateViewerMessage();
      }
    }, 5000);

    // Add random players to leaderboard
    setInterval(() => {
      if (Math.random() < 0.3) {
        const names = ['HighRoller', 'LuckyPlayer', 'CasinoKing', 'SlotMaster', 'CardShark', 'DiceRoller'];
        const randomName = names[Math.floor(Math.random() * names.length)] + Math.floor(Math.random() * 100);
        const randomChips = Math.floor(Math.random() * 2000) + 500;
        this.leaderboard.set(randomName, randomChips);
      }
    }, 15000);
  }

  simulateViewerMessage() {
    const viewers = ['HighRoller2024', 'LuckyGambler', 'CasinoFan', 'SlotLover', 'CardMaster', 'DiceKing'];
    const messages = [
      'Spin that roulette!',
      'Hit me in blackjack!',
      'Lucky slots tonight!',
      'Roll those dice!',
      'Big win incoming!',
      'Casino night!',
      'Feeling lucky!',
      'Let\'s gamble!',
      'All in!',
      'House always wins... or does it?'
    ];

    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addChatMessage(randomViewer, randomMessage);
  }

  // Auto-Pilot System
  initializeAutoPilot() {
    this.addChatMessage('Casino', '🤖 Auto-Pilot available! Click 🤖 to configure automated casino games.');
  }

  toggleAutoPilot() {
    this.createAutoPilotPanel();
  }

  createAutoPilotPanel() {
    // Remove existing panel if any
    const existingPanel = document.getElementById('autopilot-panel');
    if (existingPanel) {
      existingPanel.remove();
      return;
    }

    const panel = document.createElement('div');
    panel.id = 'autopilot-panel';
    panel.innerHTML = `
      <div class="autopilot-header">
        <h3>🎰 Casino Auto-Pilot</h3>
        <button id="close-autopilot" class="close-btn">✕</button>
      </div>

      <div class="autopilot-content">
        <div class="setup-section">
          <h4>Streamer Configuration</h4>
          <div class="input-group">
            <label>TikTok Username:</label>
            <input type="text" id="streamer-username" placeholder="Your username" />
          </div>
          <div class="input-group">
            <label>Starting Chips:</label>
            <input type="number" id="starting-chips" value="1000" min="500" max="10000" />
          </div>
        </div>

        <div class="settings-section">
          <h4>Game Settings</h4>
          <div class="setting-item">
            <label>Game Rotation (seconds):</label>
            <input type="range" id="game-interval" min="30" max="180" value="60">
            <span id="game-interval-value">60s</span>
          </div>
          <div class="setting-item">
            <label>Auto-bet Amount:</label>
            <input type="range" id="auto-bet" min="5" max="50" value="10">
            <span id="auto-bet-value">10 chips</span>
          </div>
          <div class="setting-item">
            <label>Risk Level:</label>
            <select id="risk-level">
              <option value="low">Conservative</option>
              <option value="medium" selected>Balanced</option>
              <option value="high">High Roller</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Chat Responses:</label>
            <input type="checkbox" id="auto-chat" checked>
            <span>Auto-respond to viewers</span>
          </div>
        </div>

        <div class="control-section">
          <button id="start-autopilot" class="start-btn">🚀 Start Casino Auto-Pilot</button>
          <button id="stop-autopilot" class="stop-btn" disabled>⏹️ Stop Auto-Pilot</button>
        </div>

        <div class="status-section">
          <div class="status-indicator" id="autopilot-status">
            <span class="status-dot offline"></span>
            <span class="status-text">Offline</span>
          </div>
        </div>
      </div>
    `;

    panel.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #1a0f1a, #2a1a2a);
      border: 2px solid #ffd700;
      border-radius: 15px;
      padding: 2rem;
      z-index: 2000;
      width: 450px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
      color: white;
      font-family: 'Inter', sans-serif;
    `;

    document.body.appendChild(panel);
    this.setupAutoPilotControls();
  }

  setupAutoPilotControls() {
    // Close button
    document.getElementById('close-autopilot').addEventListener('click', () => {
      document.getElementById('autopilot-panel').remove();
    });

    // Range inputs
    document.getElementById('game-interval').addEventListener('input', (e) => {
      document.getElementById('game-interval-value').textContent = e.target.value + 's';
    });

    document.getElementById('auto-bet').addEventListener('input', (e) => {
      document.getElementById('auto-bet-value').textContent = e.target.value + ' chips';
    });

    // Start Auto-Pilot
    document.getElementById('start-autopilot').addEventListener('click', () => {
      const username = document.getElementById('streamer-username').value.trim();
      if (!username) {
        alert('Please enter your TikTok username!');
        return;
      }

      this.startAutoPilot(username);
    });

    // Stop Auto-Pilot
    document.getElementById('stop-autopilot').addEventListener('click', () => {
      this.stopAutoPilot();
    });
  }

  startAutoPilot(username) {
    this.autoPilot = {
      isActive: true,
      username: username,
      gameInterval: parseInt(document.getElementById('game-interval').value) * 1000,
      autoBet: parseInt(document.getElementById('auto-bet').value),
      riskLevel: document.getElementById('risk-level').value,
      autoChat: document.getElementById('auto-chat').checked,
      timer: null,
      currentGame: 0
    };

    // Reset chips if needed
    const startingChips = parseInt(document.getElementById('starting-chips').value);
    this.playerChips = startingChips;

    // Update UI
    document.getElementById('autopilot-status').innerHTML = `
      <span class="status-dot online"></span>
      <span class="status-text">Online & Active</span>
    `;
    document.getElementById('start-autopilot').disabled = true;
    document.getElementById('stop-autopilot').disabled = false;

    // Start automatic casino games
    this.addChatMessage('Auto-Pilot', `🎰 Casino Auto-Pilot activated! Welcome to ${username}'s live casino!`);

    // Start first game immediately
    this.autoPlayGame();

    // Set up automatic game rotation
    this.autoPilot.timer = setInterval(() => {
      if (this.autoPilot.isActive) {
        this.autoPlayGame();
      }
    }, this.autoPilot.gameInterval);

    // Auto responses
    if (this.autoPilot.autoChat) {
      this.startAutoResponses();
    }

    // Show live indicator
    document.getElementById('live-indicator').classList.add('active');
  }

  stopAutoPilot() {
    if (this.autoPilot) {
      this.autoPilot.isActive = false;
      if (this.autoPilot.timer) {
        clearInterval(this.autoPilot.timer);
      }
    }

    // Update UI
    document.getElementById('autopilot-status').innerHTML = `
      <span class="status-dot offline"></span>
      <span class="status-text">Offline</span>
    `;
    document.getElementById('start-autopilot').disabled = false;
    document.getElementById('stop-autopilot').disabled = true;

    this.addChatMessage('Auto-Pilot', '🎰 Casino Auto-Pilot stopped. Thanks for playing!');
    document.getElementById('live-indicator').classList.remove('active');
  }

  autoPlayGame() {
    const games = ['roulette', 'blackjack', 'slots', 'dice'];
    const currentGame = games[this.autoPilot.currentGame % games.length];
    this.autoPilot.currentGame++;

    // Switch to the game section
    this.switchSection(`${currentGame}-section`);

    // Announce the game
    this.addChatMessage(this.autoPilot.username, `🎮 Time for ${currentGame.toUpperCase()}! Let's see what luck brings us!`);

    // Play the game after a short delay
    setTimeout(() => {
      switch (currentGame) {
        case 'roulette':
          this.autoPlayRoulette();
          break;
        case 'blackjack':
          this.autoPlayBlackjack();
          break;
        case 'slots':
          this.autoPlaySlots();
          break;
        case 'dice':
          this.autoPlayDice();
          break;
      }
    }, 2000);
  }

  autoPlayRoulette() {
    // Place random bets based on risk level
    const betAmount = this.autoPilot.autoBet;
    document.getElementById('bet-amount').value = betAmount;

    const riskLevel = this.autoPilot.riskLevel;
    if (riskLevel === 'low') {
      // Conservative bets (outside bets)
      const outsideBets = ['red', 'black', 'even', 'odd'];
      const randomBet = outsideBets[Math.floor(Math.random() * outsideBets.length)];
      this.placeBet('outside', randomBet);
    } else if (riskLevel === 'high') {
      // Risky bets (single numbers)
      const randomNumber = Math.floor(Math.random() * 37);
      this.placeBet('number', randomNumber);
    } else {
      // Balanced approach
      if (Math.random() < 0.7) {
        const outsideBets = ['red', 'black', 'even', 'odd'];
        const randomBet = outsideBets[Math.floor(Math.random() * outsideBets.length)];
        this.placeBet('outside', randomBet);
      } else {
        const randomNumber = Math.floor(Math.random() * 37);
        this.placeBet('number', randomNumber);
      }
    }

    setTimeout(() => {
      this.spinRoulette();
    }, 1000);
  }

  autoPlayBlackjack() {
    document.getElementById('blackjack-bet').value = this.autoPilot.autoBet;
    this.dealBlackjack();
  }

  autoPlaySlots() {
    document.getElementById('slots-bet').value = this.autoPilot.autoBet;
    this.spinSlots();
  }

  autoPlayDice() {
    document.getElementById('dice-bet').value = this.autoPilot.autoBet;
    this.rollDice();
  }

  startAutoResponses() {
    const responses = [
      'The house edge is real, but so is the thrill! 🎰',
      'Feeling lucky tonight! 🍀',
      'Every spin could be THE spin! ✨',
      'Casino night is the best night! 🌟',
      'Let\'s see what the cards have in store! 🃏',
      'Rolling the dice of destiny! 🎲',
      'Fortune favors the bold! 💪',
      'The excitement never stops here! 🔥'
    ];

    setInterval(() => {
      if (this.autoPilot && this.autoPilot.isActive && Math.random() < 0.4) {
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        this.addChatMessage(this.autoPilot.username, randomResponse);
      }
    }, 8000);
  }

  // Real TikTok Live Integration Methods
  async connectToRealTikTokLive(username) {
    const validation = TikTokLiveUtils.validateUsername(username);
    if (!validation.valid) {
      this.addChatMessage('❌ ERROR', validation.error);
      return false;
    }

    this.addChatMessage('🔄 CONNECTING', `Încerc conectarea REALĂ la @${validation.username}...`);

    try {
      const connected = await this.realTikTokConnector.connectToUser(validation.username);

      if (connected) {
        this.addChatMessage('✅ CONNECTED', `Conectat REAL la TikTok Live @${validation.username}!`);
        return true;
      } else {
        this.addChatMessage('❌ FAILED', `Nu m-am putut conecta la @${validation.username}`);
        return false;
      }

    } catch (error) {
      console.error('Eroare conectare TikTok Live:', error);
      this.addChatMessage('❌ ERROR', `Eroare conectare: ${error.message}`);
      return false;
    }
  }

  disconnectFromRealTikTokLive() {
    if (this.realTikTokConnector && this.isReallyConnected) {
      this.realTikTokConnector.disconnect();
      this.isReallyConnected = false;
      this.addChatMessage('👋 DISCONNECTED', 'Deconectat de la TikTok Live');
    }
  }

  // Handle real gifts from TikTok Live
  handleGiftReceived(username, giftName, repeatCount) {
    // Obține sau creează jucătorul
    const player = this.getOrCreatePlayer(username);

    // Calculează creditele pentru cadou
    let giftCredits = 0;
    const giftNameLower = giftName.toLowerCase();

    // Verifică tipul cadoului
    if (giftNameLower.includes('rose')) {
      giftCredits = this.creditSystem.giftCredits.rose * repeatCount;
    } else if (giftNameLower.includes('heart')) {
      giftCredits = this.creditSystem.giftCredits.heart * repeatCount;
    } else if (giftNameLower.includes('diamond')) {
      giftCredits = this.creditSystem.giftCredits.diamond * repeatCount;
    } else if (giftNameLower.includes('perfume')) {
      giftCredits = this.creditSystem.giftCredits.perfume * repeatCount;
    } else if (giftNameLower.includes('car') || giftNameLower.includes('sports')) {
      giftCredits = this.creditSystem.giftCredits.sports_car * repeatCount;
    } else if (giftNameLower.includes('yacht')) {
      giftCredits = this.creditSystem.giftCredits.yacht * repeatCount;
    } else {
      giftCredits = this.creditSystem.giftCredits.default * repeatCount;
    }

    // Adaugă creditele și marchează-le ca retrăgabile
    player.chips += giftCredits;
    player.redeemableChips += giftCredits;
    player.hasGifted = true;
    player.totalGifts += repeatCount;

    // Actualizează jucătorul curent
    this.playerChips = player.chips;

    // Declanșează jocuri bazate pe cadou
    if (giftNameLower.includes('rose')) {
      this.spinRoulette(); // Învârte ruleta pentru cadouri
    } else if (giftNameLower.includes('heart')) {
      this.dealBlackjack(); // Joacă blackjack pentru cadouri
    } else if (giftNameLower.includes('diamond')) {
      this.spinSlots(); // Joacă la păcănele pentru cadouri premium
    }

    // Notifică utilizatorul
    this.addChatMessage('🎁 CADOU', `Mulțumim ${username} pentru ${repeatCount}x ${giftName}!`);
    this.addChatMessage('💰 CREDITE', `${username} a primit ${giftCredits} credite RETRĂGABILE!`);

    // Actualizează afișajul
    this.updateDisplay();
    this.updateLeaderboard(username, giftCredits);
  }

  // Update viewer count from real TikTok Live
  updateViewerCount(realCount) {
    if (realCount && realCount > 0) {
      this.viewerCount = realCount;
      document.getElementById('viewer-count').textContent = `${realCount} players`;
    }
  }

  // Place bet from chat command
  placeBetForPlayer(username, target, amount) {
    const player = this.getOrCreatePlayer(username);

    // Verifică dacă jucătorul are suficiente credite
    if (amount <= 0 || amount > player.chips) {
      this.addChatMessage('⚠️ ERROR', `${username}, nu ai suficiente credite pentru acest pariu!`);
      return false;
    }

    // Verifică dacă ținta pariului este validă
    if (!this.isValidBetTarget(target)) {
      this.addChatMessage('⚠️ ERROR', `${username}, ținta pariului "${target}" nu este validă!`);
      return false;
    }

    // Scade creditele din contul jucătorului
    player.chips -= amount;

    // Adaugă pariul
    const betKey = `${username}-${target}`;
    this.roulette.currentBets.set(betKey, amount);

    // Notifică jucătorul
    this.addChatMessage('💰 BET', `${username} pariază ${amount} credite pe ${target}!`);

    // Actualizează afișajul
    this.playerChips = player.chips;
    this.updateDisplay();

    // Auto-spin după 10 secunde dacă nu se învârte deja
    if (!this.roulette.isSpinning) {
      setTimeout(() => {
        if (!this.roulette.isSpinning) {
          this.spinRoulette(username);
        }
      }, 10000);
    }

    return true;
  }

  // Verifică dacă ținta pariului este validă
  isValidBetTarget(target) {
    // Numere de la 0 la 36
    if (!isNaN(target) && parseInt(target) >= 0 && parseInt(target) <= 36) {
      return true;
    }

    // Pariuri externe
    const validOutsideBets = ['red', 'black', 'even', 'odd', '1-18', '19-36', '1-12', '13-24', '25-36'];
    return validOutsideBets.includes(target.toLowerCase());
  }

  // Gestionează cererile de retragere
  handleWithdrawRequest(username) {
    const player = this.getOrCreatePlayer(username);

    // Verifică dacă jucătorul are credite retrăgabile
    if (player.redeemableChips <= 0) {
      this.addChatMessage('⚠️ ERROR', `${username}, nu ai credite retrăgabile! Doar creditele din cadouri pot fi retrase.`);
      return false;
    }

    // Verifică dacă jucătorul a dat cadouri
    if (!player.hasGifted) {
      this.addChatMessage('⚠️ ERROR', `${username}, doar utilizatorii care au trimis cadouri pot retrage credite!`);
      return false;
    }

    // Calculează suma de retras
    const withdrawAmount = player.redeemableChips;

    // Notifică jucătorul
    this.addChatMessage('💸 WITHDRAW', `${username} a retras ${withdrawAmount} credite! Contactează streamer-ul pentru detalii.`);

    // Resetează creditele retrăgabile
    player.redeemableChips = 0;

    return true;
  }

  // Afișează comenzile disponibile
  showHelpCommands(username) {
    this.addChatMessage('ℹ️ HELP', `${username}, iată comenzile disponibile:`);
    this.addChatMessage('🎮 GAMES', `!roulette, !blackjack, !slots, !dice - Joacă jocuri`);
    this.addChatMessage('💰 CREDITS', `!tap, !share - Primești credite gratuite`);
    this.addChatMessage('💸 WITHDRAW', `!withdraw - Retrage creditele din cadouri`);
    this.addChatMessage('📊 INFO', `!balance, !stats, !help - Informații`);
  }

  // SISTEM GESTIONARE JUCĂTORI
  getOrCreatePlayer(username) {
    if (!this.players.has(username)) {
      this.players.set(username, {
        username: username,
        chips: 0,                    // Credite totale
        redeemableChips: 0,          // Credite retrăgabile (doar din cadouri)
        freeChips: 0,                // Credite gratuite (tap/share)
        hasGifted: false,            // A dat cadouri?
        totalGifts: 0,               // Numărul total de cadouri
        tapsUsed: 0,                 // Tap-uri folosite
        sharesUsed: 0,               // Distribuiri folosite
        gamesPlayed: 0,              // Jocuri jucate
        totalWinnings: 0,            // Câștiguri totale
        winStreak: 0,                // Șirul de victorii
        lastBonusTime: 0,            // Ultima dată când a primit bonus
        playerType: 'free'           // free, small_gift, big_gift
      });
    }
    return this.players.get(username);
  }

  // SISTEM CREDITE PENTRU ACȚIUNI
  handleTapAction(username) {
    const player = this.getOrCreatePlayer(username);

    if (player.tapsUsed >= this.creditSystem.maxTapsPerUser) {
      this.addChatMessage('⚠️ LIMITĂ', `${username}, ai folosit deja toate tap-urile gratuite!`);
      return false;
    }

    player.tapsUsed++;

    // La fiecare 100 tap-uri, dă credite
    if (player.tapsUsed % 100 === 0) {
      const creditsEarned = Math.floor(this.creditSystem.tapCredits / 30); // ~3.33 credite per 100 tap-uri
      player.chips += creditsEarned;
      player.freeChips += creditsEarned;

      this.addChatMessage('👆 TAP', `${username} +${creditsEarned} credite! (${player.tapsUsed}/3000 tap-uri)`);

      if (player.tapsUsed >= this.creditSystem.maxTapsPerUser) {
        this.addChatMessage('🎉 COMPLET', `${username} a completat toate tap-urile! +${this.creditSystem.tapCredits} credite bonus!`);
        player.chips += this.creditSystem.tapCredits;
        player.freeChips += this.creditSystem.tapCredits;
      }
    }

    return true;
  }

  handleShareAction(username) {
    const player = this.getOrCreatePlayer(username);

    if (player.sharesUsed >= this.creditSystem.maxSharesPerUser) {
      this.addChatMessage('⚠️ LIMITĂ', `${username}, ai folosit deja toate distribuirile gratuite!`);
      return false;
    }

    player.sharesUsed++;

    // La fiecare distribuire, dă credite
    const creditsEarned = Math.floor(this.creditSystem.shareCredits / this.creditSystem.maxSharesPerUser); // ~3.33 credite per share
    player.chips += creditsEarned;
    player.freeChips += creditsEarned;

    this.addChatMessage('📤 SHARE', `${username} +${creditsEarned} credite! (${player.sharesUsed}/30 distribuiri)`);

    if (player.sharesUsed >= this.creditSystem.maxSharesPerUser) {
      this.addChatMessage('🎉 COMPLET', `${username} a completat toate distribuirile! +${this.creditSystem.shareCredits} credite bonus!`);
      player.chips += this.creditSystem.shareCredits;
      player.freeChips += this.creditSystem.shareCredits;
    }

    return true;
  }

  // STRATEGIA DE PROFIT - CALCULEAZĂ ȘANSELE DE CÂȘTIG
  calculateWinChance(username) {
    const player = this.getOrCreatePlayer(username);

    // Actualizează tipul jucătorului
    if (player.redeemableChips >= this.profitStrategy.bigGiftThreshold) {
      player.playerType = 'big_gift';
    } else if (player.redeemableChips >= this.profitStrategy.smallGiftThreshold) {
      player.playerType = 'small_gift';
    } else {
      player.playerType = 'free';
    }

    // Calculează șansele de câștig
    let winChance = this.profitStrategy.freeUserWinChance;

    if (player.playerType === 'big_gift') {
      winChance = this.profitStrategy.bigGiftWinChance;
    } else if (player.playerType === 'small_gift') {
      winChance = this.profitStrategy.smallGiftWinChance;
    }

    // Bonus ocazional pentru a-i captiva
    const timeSinceLastBonus = Date.now() - player.lastBonusTime;
    const shouldGiveBonus = Math.random() < this.profitStrategy.bonusFrequency && timeSinceLastBonus > 300000; // 5 minute

    if (shouldGiveBonus) {
      winChance = this.profitStrategy.bonusWinChance;
      player.lastBonusTime = Date.now();
      this.addChatMessage('🍀 BONUS', `${username} are noroc special astăzi!`);
    }

    return winChance;
  }

  // Browser Source Mode
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');
    document.getElementById('autopilot-toggle').style.display = 'none';
  }
}

// Initialize the Casino
document.addEventListener('DOMContentLoaded', () => {
  const casino = new TikTokLiveCasino();

  // Make globally available
  window.TikTokLiveCasino = casino;

  // Check for browser source mode
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    casino.enableBrowserSourceMode();
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          casino.switchSection('roulette-section');
          break;
        case '2':
          e.preventDefault();
          casino.switchSection('blackjack-section');
          break;
        case '3':
          e.preventDefault();
          casino.switchSection('slots-section');
          break;
        case '4':
          e.preventDefault();
          casino.switchSection('dice-section');
          break;
        case '5':
          e.preventDefault();
          casino.switchSection('stats-section');
          break;
        case '6':
          e.preventDefault();
          casino.switchSection('chat-section');
          break;
      }
    }
  });
});

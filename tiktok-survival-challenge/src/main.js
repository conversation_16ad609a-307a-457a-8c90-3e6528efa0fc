import './style.css'

// TikTok Live Survival Challenge - Main Application
class TikTokSurvivalChallenge {
  constructor() {
    this.currentSection = 'scenario-section';
    this.currentDay = 1;
    this.viewerCount = 0;
    this.survivalRate = 100;
    this.voteTimer = null;
    this.timeLeft = 30;

    // Resources
    this.resources = {
      water: 50,
      food: 30,
      shelter: 20,
      fire: 10
    };

    // Survivors
    this.survivors = new Map();
    this.totalSurvivors = 0;
    this.challengesCompleted = 0;
    this.groupMorale = 'High';

    // Scenarios
    this.scenarios = [
      {
        title: "Crashed on Survival Island!",
        description: "Your plane has crashed on a mysterious island. You need to work together to survive!",
        image: "🏝️",
        choices: [
          { text: "Explore the beach for supplies", effect: { water: +10, food: +5 } },
          { text: "Look for fresh water source", effect: { water: +20, shelter: -5 } },
          { text: "Build emergency shelter", effect: { shelter: +15, fire: +5 } },
          { text: "Search for food in jungle", effect: { food: +15, water: -5 } }
        ]
      },
      {
        title: "Storm Approaching!",
        description: "Dark clouds gather overhead. A storm is coming and you need to prepare!",
        image: "⛈️",
        choices: [
          { text: "Reinforce the shelter", effect: { shelter: +20, fire: -10 } },
          { text: "Gather rainwater", effect: { water: +25, food: -5 } },
          { text: "Find higher ground", effect: { shelter: +10, water: +10 } },
          { text: "Secure food supplies", effect: { food: +20, shelter: -5 } }
        ]
      },
      {
        title: "Wild Animal Encounter!",
        description: "You've encountered a wild animal! How do you handle this dangerous situation?",
        image: "🐺",
        choices: [
          { text: "Make noise to scare it away", effect: { fire: +10, food: -10 } },
          { text: "Slowly back away", effect: { shelter: +5, water: -5 } },
          { text: "Use fire to intimidate", effect: { fire: -15, food: +10 } },
          { text: "Climb a tree for safety", effect: { shelter: +10, fire: -5 } }
        ]
      }
    ];

    this.currentScenario = 0;
    this.chatMessages = [];
    this.autoPilot = null;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateDisplay();
    this.simulateViewerActivity();
    this.loadScenario();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Choices
    document.querySelectorAll('.choice-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.makeChoice(e.target.dataset.choice);
      });
    });

    // Resource actions
    document.querySelectorAll('.resource-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.performResourceAction(e.target.dataset.action);
      });
    });

    // Challenge actions
    document.getElementById('start-challenge').addEventListener('click', () => this.startChallenge());
    document.getElementById('skip-challenge').addEventListener('click', () => this.skipChallenge());

    // Chat
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Auto-pilot
    document.getElementById('autopilot-toggle').addEventListener('click', () => this.toggleAutoPilot());
  }

  switchSection(sectionId) {
    document.querySelectorAll('.survival-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');

    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  loadScenario() {
    const scenario = this.scenarios[this.currentScenario];

    document.getElementById('scenario-title').textContent = scenario.title;
    document.getElementById('scenario-description').textContent = scenario.description;
    document.querySelector('.scenario-image').textContent = scenario.image;

    const choiceButtons = document.querySelectorAll('.choice-btn');
    choiceButtons.forEach((btn, index) => {
      if (scenario.choices[index]) {
        btn.textContent = `${btn.dataset.choice}. ${scenario.choices[index].text}`;
        btn.style.display = 'block';
        btn.classList.remove('selected');
        btn.disabled = false;
      } else {
        btn.style.display = 'none';
      }
    });

    this.startVoteTimer();
    this.addChatMessage('Survival Guide', `🏝️ New scenario: ${scenario.title}`);
  }

  startVoteTimer() {
    this.timeLeft = 30;
    this.voteTimer = setInterval(() => {
      this.timeLeft--;
      document.getElementById('vote-time').textContent = `${this.timeLeft}s`;

      if (this.timeLeft <= 0) {
        this.endVoting();
      }
    }, 1000);
  }

  makeChoice(choice) {
    const scenario = this.scenarios[this.currentScenario];
    const choiceIndex = choice.charCodeAt(0) - 65; // A=0, B=1, etc.
    const selectedChoice = scenario.choices[choiceIndex];

    if (!selectedChoice) return;

    // Mark choice as selected
    document.querySelector(`[data-choice="${choice}"]`).classList.add('selected');

    // Apply effects
    Object.keys(selectedChoice.effect).forEach(resource => {
      this.resources[resource] = Math.max(0, Math.min(100, this.resources[resource] + selectedChoice.effect[resource]));
    });

    this.addChatMessage('Survival Guide', `Choice ${choice} selected: ${selectedChoice.text}`);
    this.updateResourceDisplay();

    // Auto next scenario after 3 seconds
    setTimeout(() => {
      this.nextScenario();
    }, 3000);

    this.endVoting();
  }

  endVoting() {
    if (this.voteTimer) {
      clearInterval(this.voteTimer);
      this.voteTimer = null;
    }

    document.querySelectorAll('.choice-btn').forEach(btn => {
      btn.disabled = true;
    });
  }

  nextScenario() {
    this.currentScenario = (this.currentScenario + 1) % this.scenarios.length;
    if (this.currentScenario === 0) {
      this.currentDay++;
      this.challengesCompleted++;
      this.addChatMessage('Survival Guide', `🌅 Day ${this.currentDay} begins! You've survived another day!`);
    }
    this.loadScenario();
    this.updateDisplay();
  }

  performResourceAction(action) {
    switch (action) {
      case 'gather':
        this.resources.water += 5;
        this.resources.food += 3;
        this.addChatMessage('Survival Guide', '🌿 Gathered resources from the environment!');
        break;
      case 'craft':
        if (this.resources.water >= 10) {
          this.resources.water -= 10;
          this.resources.shelter += 15;
          this.addChatMessage('Survival Guide', '🔨 Crafted shelter improvements!');
        }
        break;
      case 'explore':
        this.resources.food += 10;
        this.resources.water -= 5;
        this.addChatMessage('Survival Guide', '🗺️ Explored new area and found supplies!');
        break;
    }

    // Clamp resources
    Object.keys(this.resources).forEach(key => {
      this.resources[key] = Math.max(0, Math.min(100, this.resources[key]));
    });

    this.updateResourceDisplay();
  }

  startChallenge() {
    this.addChatMessage('Survival Guide', '🎯 Daily challenge started! Work together to complete it!');
    document.getElementById('challenge-status').textContent = 'Active';

    // Simulate challenge progress
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += Math.random() * 10;
      document.getElementById('challenge-progress').style.width = `${Math.min(100, progress)}%`;

      if (progress >= 100) {
        clearInterval(progressInterval);
        this.completeChallenge();
      }
    }, 1000);
  }

  completeChallenge() {
    this.challengesCompleted++;
    document.getElementById('challenge-status').textContent = 'Completed';
    this.addChatMessage('Survival Guide', '🎉 Challenge completed! Resources gained!');

    // Reward resources
    this.resources.water += 15;
    this.resources.food += 10;
    this.resources.shelter += 5;
    this.updateResourceDisplay();
    this.updateDisplay();
  }

  skipChallenge() {
    document.getElementById('challenge-status').textContent = 'Skipped';
    this.addChatMessage('Survival Guide', '⏭️ Challenge skipped. Moving to next objective.');
  }

  // Chat System
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';
      this.processChatCommand('You', message);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }

    this.chatMessages.push({ username, message, timestamp });
  }

  processChatCommand(username, message) {
    const msg = message.toLowerCase();

    if (msg.includes('!choice') || msg.includes('!vote')) {
      this.switchSection('scenario-section');
    } else if (msg.includes('!resources')) {
      this.switchSection('resources-section');
    } else if (msg.includes('!challenge')) {
      this.switchSection('challenges-section');
      this.startChallenge();
    } else if (msg.includes('!gather')) {
      this.performResourceAction('gather');
    }
  }

  // Display Updates
  updateDisplay() {
    document.getElementById('viewer-count').textContent = `${this.viewerCount} survivors`;
    document.getElementById('current-day').textContent = `Day ${this.currentDay}`;
    document.getElementById('survival-rate').textContent = `${this.survivalRate}% alive`;

    document.getElementById('total-survivors').textContent = this.totalSurvivors;
    document.getElementById('days-survived').textContent = this.currentDay;
    document.getElementById('challenges-completed').textContent = this.challengesCompleted;
    document.getElementById('group-morale').textContent = this.groupMorale;

    this.updateSurvivorsList();
  }

  updateResourceDisplay() {
    Object.keys(this.resources).forEach(resource => {
      const amount = this.resources[resource];
      document.getElementById(`${resource}-amount`).textContent = amount;
      document.getElementById(`${resource}-fill`).style.width = `${amount}%`;
    });
  }

  updateSurvivorsList() {
    const survivorsList = document.getElementById('survivors-list');
    const survivors = Array.from(this.survivors.entries()).slice(0, 12);

    survivorsList.innerHTML = survivors.map(([name, status]) => `
      <div class="survivor-card ${status.alive ? 'alive' : 'dead'}">
        <div class="survivor-avatar">${status.alive ? '😊' : '💀'}</div>
        <div class="survivor-name">${name}</div>
        <div class="survivor-status">${status.alive ? 'Alive' : 'Lost'}</div>
      </div>
    `).join('');
  }

  // Viewer Activity Simulation
  simulateViewerActivity() {
    setInterval(() => {
      const change = Math.floor(Math.random() * 10) - 5;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      this.totalSurvivors = this.viewerCount;

      // Add random survivors
      if (Math.random() < 0.3) {
        const names = ['Survivor1', 'Explorer2', 'Adventurer3', 'Brave4', 'Hero5'];
        const randomName = names[Math.floor(Math.random() * names.length)] + Math.floor(Math.random() * 100);
        this.survivors.set(randomName, { alive: true, day: this.currentDay });
      }

      this.updateDisplay();
    }, 8000);

    setInterval(() => {
      if (Math.random() < 0.4) {
        this.simulateViewerMessage();
      }
    }, 6000);
  }

  simulateViewerMessage() {
    const viewers = ['Survivor2024', 'IslandExplorer', 'AdventureSeeker', 'WildernessGuru', 'SurvivalPro'];
    const messages = [
      'We need more water!',
      'Let\'s build a better shelter!',
      'I vote for exploring!',
      'We should gather food!',
      'Stay together team!',
      'This island is dangerous!',
      'We can survive this!',
      'What\'s that sound?',
      'Fire is going out!',
      'Found some berries!'
    ];

    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addChatMessage(randomViewer, randomMessage);
  }

  // Auto-Pilot System
  initializeAutoPilot() {
    this.addChatMessage('Survival Guide', '🤖 Auto-Pilot available! Click 🤖 to start automated survival challenge!');
  }

  toggleAutoPilot() {
    if (!this.autoPilot || !this.autoPilot.isActive) {
      this.autoPilot = { isActive: true, username: 'SurvivalStreamer' };
      this.addChatMessage('Auto-Pilot', '🏝️ Survival Auto-Pilot activated! The adventure begins!');
      document.getElementById('live-indicator').classList.add('active');

      // Auto scenario progression
      setInterval(() => {
        if (this.autoPilot && this.autoPilot.isActive) {
          // Auto make choices
          const choices = ['A', 'B', 'C', 'D'];
          const randomChoice = choices[Math.floor(Math.random() * choices.length)];
          this.makeChoice(randomChoice);

          // Auto resource actions
          if (Math.random() < 0.3) {
            const actions = ['gather', 'craft', 'explore'];
            const randomAction = actions[Math.floor(Math.random() * actions.length)];
            this.performResourceAction(randomAction);
          }
        }
      }, 35000);
    } else {
      this.autoPilot.isActive = false;
      this.addChatMessage('Auto-Pilot', '🏝️ Survival Auto-Pilot stopped. Thanks for surviving!');
      document.getElementById('live-indicator').classList.remove('active');
    }
  }

  // Browser Source Mode
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');
    document.getElementById('autopilot-toggle').style.display = 'none';
  }
}

// Initialize the Survival Challenge
document.addEventListener('DOMContentLoaded', () => {
  const survivalChallenge = new TikTokSurvivalChallenge();

  window.TikTokSurvivalChallenge = survivalChallenge;

  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    survivalChallenge.enableBrowserSourceMode();
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          survivalChallenge.switchSection('scenario-section');
          break;
        case '2':
          e.preventDefault();
          survivalChallenge.switchSection('resources-section');
          break;
        case '3':
          e.preventDefault();
          survivalChallenge.switchSection('survivors-section');
          break;
      }
    }
  });
});

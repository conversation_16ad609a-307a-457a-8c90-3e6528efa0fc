import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureFinite(value: any, options?: EnsureBaseOptions): number;
declare function ensureFinite(value: any, options?: EnsureBaseOptions & EnsureIsOptional): number | null;
declare function ensureFinite(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<number>): number;

export default ensureFinite;

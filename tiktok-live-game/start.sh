#!/bin/bash

# TikTok Live Adventure Game - Start Script
echo "🎮 TikTok Live Adventure Game"
echo "================================"
echo ""

# Verifică dacă Node.js este instalat
if ! command -v node &> /dev/null; then
    echo "❌ Node.js nu este instalat!"
    echo "Te rog instalează Node.js de la: https://nodejs.org/"
    exit 1
fi

# Verifică dacă npm este instalat
if ! command -v npm &> /dev/null; then
    echo "❌ npm nu este instalat!"
    echo "Te rog instalează npm împreună cu Node.js"
    exit 1
fi

echo "✅ Node.js și npm sunt instalate"
echo ""

# Verifică dacă dependențele sunt instalate
if [ ! -d "node_modules" ]; then
    echo "📦 Instalez dependențele..."
    npm install
    echo ""
fi

echo "🚀 Pornesc aplicația..."
echo ""
echo "📱 Aplicația va fi disponibilă la:"
echo "   http://localhost:5173/"
echo ""
echo "🎥 Pentru OBS Browser Source folosește:"
echo "   http://localhost:5173/?mode=browser-source"
echo ""
echo "🤖 Pentru Auto-Pilot:"
echo "   1. Deschide aplicația în browser"
echo "   2. Apasă butonul 🤖 din centru"
echo "   3. Introdu username-ul tău TikTok"
echo "   4. Apasă 'Pornește Auto-Pilot'"
echo ""
echo "⏹️  Pentru a opri aplicația: Ctrl+C"
echo "================================"
echo ""

# Pornește aplicația
npm run dev

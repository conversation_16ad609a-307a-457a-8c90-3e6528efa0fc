/* TikTok Live Casino Styles */
:root {
  /* Casino Color Palette */
  --casino-gold: #ffd700;
  --casino-red: #dc143c;
  --casino-green: #228b22;
  --casino-black: #000000;
  --casino-white: #ffffff;
  --neon-blue: #00ffff;
  --neon-pink: #ff1493;
  --neon-purple: #9400d3;

  /* Background Colors */
  --background-dark: #0a0a0a;
  --background-casino: #1a0f1a;
  --background-table: #0d4f0d;
  --background-card: #2a2a2a;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-gold: #ffd700;
  --text-muted: #888888;

  /* Border and Effects */
  --border-color: #333333;
  --border-gold: #ffd700;
  --glow-gold: 0 0 20px #ffd700;
  --glow-red: 0 0 20px #dc143c;
  --glow-green: 0 0 20px #228b22;

  /* Fonts */
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: radial-gradient(circle at center, var(--background-casino) 0%, var(--background-dark) 100%);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Casino Container */
#casino-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: var(--background-casino);
  border: 2px solid var(--casino-gold);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--glow-gold);
}

/* Header */
.casino-header {
  background: linear-gradient(90deg, var(--casino-red), var(--casino-gold), var(--casino-red));
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(220, 20, 60, 0.5);
  border-bottom: 2px solid var(--casino-gold);
}

.casino-header h1 {
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: goldGlow 2s infinite alternate;
}

@keyframes goldGlow {
  0% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px #ffd700; }
  100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 20px #ffd700, 0 0 30px #ffd700; }
}

.casino-status {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.casino-status span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--casino-gold);
}

/* Auto-Pilot Toggle */
.autopilot-toggle {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--casino-red), var(--casino-gold));
  border: 2px solid var(--casino-gold);
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1500;
  transition: all 0.3s ease;
  box-shadow: var(--glow-gold);
  animation: pulse 2s infinite;
}

.autopilot-toggle:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 0 30px var(--casino-gold);
}

@keyframes pulse {
  0%, 100% { transform: translateX(-50%) scale(1); }
  50% { transform: translateX(-50%) scale(1.05); }
}

/* Main Casino Area */
.casino-main {
  flex: 1;
  padding: 2rem;
  position: relative;
  overflow-y: auto;
  background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
}

/* Casino Sections */
.casino-section {
  display: none;
  width: 100%;
  height: 100%;
  animation: fadeIn 0.5s ease-in-out;
}

.casino-section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Roulette Section */
.roulette-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: 100%;
}

.roulette-wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  background: var(--background-table);
  border-radius: 15px;
  padding: 2rem;
  border: 3px solid var(--casino-gold);
  box-shadow: var(--glow-gold);
  position: relative;
}

#roulette-wheel {
  border: 4px solid var(--casino-gold);
  border-radius: 50%;
  box-shadow: var(--glow-gold);
  background: var(--background-dark);
}

.roulette-ball {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--casino-white);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px var(--casino-white);
  transition: all 0.1s ease;
}

.spin-button {
  background: linear-gradient(45deg, var(--casino-red), var(--casino-gold));
  border: 2px solid var(--casino-gold);
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1rem 2rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--glow-gold);
}

.spin-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

.spin-button:active {
  transform: translateY(0);
}

.spin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Roulette Betting */
.roulette-betting {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.roulette-betting h3 {
  color: var(--casino-gold);
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.betting-board {
  margin-bottom: 2rem;
}

.number-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 2px;
  margin-bottom: 1rem;
}

.number-btn {
  aspect-ratio: 1;
  border: 1px solid var(--casino-gold);
  background: var(--casino-green);
  color: white;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.number-btn.red {
  background: var(--casino-red);
}

.number-btn.black {
  background: var(--casino-black);
}

.number-btn.zero {
  background: var(--casino-green);
  grid-column: span 12;
}

.number-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px currentColor;
}

.number-btn.selected {
  box-shadow: 0 0 20px var(--casino-gold);
  border-color: var(--casino-gold);
}

.outside-bets {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.bet-btn {
  background: var(--background-dark);
  border: 2px solid var(--casino-gold);
  color: var(--casino-gold);
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.bet-btn:hover {
  background: var(--casino-gold);
  color: var(--background-dark);
  transform: translateY(-2px);
}

.bet-btn.selected {
  background: var(--casino-gold);
  color: var(--background-dark);
}

.betting-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

.control-btn {
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.control-btn:hover {
  border-color: var(--casino-gold);
  color: var(--casino-gold);
}

#bet-amount, #blackjack-bet, #dice-bet {
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem;
  border-radius: 10px;
  font-size: 1rem;
  width: 120px;
  text-align: center;
}

#bet-amount:focus, #blackjack-bet:focus, #dice-bet:focus {
  outline: none;
  border-color: var(--casino-gold);
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Blackjack Section */
.blackjack-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.blackjack-table {
  background: var(--background-table);
  border-radius: 15px;
  padding: 2rem;
  border: 3px solid var(--casino-gold);
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 2rem;
  flex: 1;
}

.dealer-area, .player-area {
  text-align: center;
}

.dealer-area h3, .player-area h3 {
  color: var(--casino-gold);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.card-area {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  min-height: 120px;
  align-items: center;
}

.card {
  width: 80px;
  height: 112px;
  background: white;
  border: 2px solid var(--casino-black);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0.5rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--casino-black);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.card.red {
  color: var(--casino-red);
}

.card.back {
  background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
  color: white;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
}

.card-placeholder {
  width: 80px;
  height: 112px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.score {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--casino-gold);
}

.blackjack-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.game-btn {
  background: linear-gradient(45deg, var(--casino-green), var(--casino-gold));
  border: 2px solid var(--casino-gold);
  color: white;
  padding: 1rem 2rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 700;
  font-size: 1rem;
}

.game-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.game-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.bet-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.result-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--casino-gold);
  min-width: 150px;
}

/* Slot Machine Section */
.slots-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.slot-machine {
  background: linear-gradient(145deg, var(--background-card), var(--background-dark));
  border: 4px solid var(--casino-gold);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: var(--glow-gold);
  text-align: center;
}

.slots-display {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.reel {
  width: 120px;
  height: 180px;
  background: var(--background-dark);
  border: 3px solid var(--casino-gold);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.reel.spinning {
  animation: reelSpin 2s ease-out;
}

@keyframes reelSpin {
  0% { transform: translateY(0); }
  50% { transform: translateY(-200px); }
  100% { transform: translateY(0); }
}

.symbol {
  font-size: 3rem;
  line-height: 1;
  padding: 1rem;
  text-shadow: 0 0 10px currentColor;
}

.slots-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  margin-bottom: 2rem;
}

.bet-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bet-selector label {
  color: var(--casino-gold);
  font-weight: 600;
}

#slots-bet {
  background: var(--background-dark);
  border: 2px solid var(--casino-gold);
  color: var(--casino-gold);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 1rem;
}

.paytable {
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid var(--casino-gold);
  border-radius: 10px;
  padding: 1.5rem;
}

.paytable h4 {
  color: var(--casino-gold);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.payout-line {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  font-size: 1.1rem;
  color: var(--text-primary);
}

.payout-line:last-child {
  border-bottom: none;
}

/* Dice Game Section */
.dice-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.dice-game {
  background: var(--background-table);
  border: 3px solid var(--casino-gold);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  box-shadow: var(--glow-gold);
}

.dice-game h3 {
  color: var(--casino-gold);
  margin-bottom: 2rem;
  font-size: 2rem;
}

.dice-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
}

.dice {
  width: 80px;
  height: 80px;
  background: white;
  border: 3px solid var(--casino-black);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--casino-black);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  margin: 0 1rem;
}

.dice.rolling {
  animation: diceRoll 1s ease-in-out;
}

@keyframes diceRoll {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(90deg); }
  50% { transform: rotate(180deg); }
  75% { transform: rotate(270deg); }
}

.total-display {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--casino-gold);
}

.dice-betting {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.bet-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.dice-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

/* Statistics Section */
.stats-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  height: 100%;
}

.casino-stats {
  background: var(--background-card);
  border: 2px solid var(--border-color);
  border-radius: 15px;
  padding: 2rem;
}

.casino-stats h3 {
  color: var(--casino-gold);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.stat-card {
  background: var(--background-dark);
  border: 2px solid var(--casino-gold);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--glow-gold);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--casino-gold);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.leaderboard {
  background: var(--background-card);
  border: 2px solid var(--border-color);
  border-radius: 15px;
  padding: 2rem;
}

.leaderboard h3 {
  color: var(--casino-gold);
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.leaderboard-entry {
  display: flex;
  align-items: center;
  background: var(--background-dark);
  padding: 1rem;
  border-radius: 10px;
  border-left: 4px solid var(--casino-gold);
  transition: all 0.3s ease;
}

.leaderboard-entry:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.leaderboard-rank {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--casino-gold);
  min-width: 50px;
}

.leaderboard-name {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-left: 1rem;
}

.leaderboard-chips {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--casino-gold);
}

/* Chat Section */
.chat-container {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-container h2 {
  color: var(--casino-gold);
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
}

.chat-display {
  flex: 1;
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  border-radius: 10px;
  padding: 1rem;
  overflow-y: auto;
  margin-bottom: 1rem;
  max-height: 400px;
}

.chat-message {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border-radius: 10px;
  background: rgba(255, 215, 0, 0.1);
  border-left: 3px solid var(--casino-gold);
}

.chat-username {
  font-weight: 700;
  color: var(--casino-gold);
  margin-right: 0.5rem;
}

.chat-text {
  color: var(--text-primary);
}

.chat-timestamp {
  font-size: 0.8rem;
  color: var(--text-muted);
  float: right;
}

.chat-controls {
  display: flex;
  gap: 1rem;
}

#chat-input {
  flex: 1;
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

#chat-input:focus {
  outline: none;
  border-color: var(--casino-gold);
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Navigation */
.casino-nav {
  display: flex;
  background: var(--background-dark);
  border-top: 2px solid var(--casino-gold);
}

.nav-btn {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  border-right: 1px solid var(--border-color);
}

.nav-btn:last-child {
  border-right: none;
}

.nav-btn:hover {
  background: var(--background-card);
  color: var(--casino-gold);
}

.nav-btn.active {
  background: var(--casino-gold);
  color: var(--background-dark);
  font-weight: 700;
}

/* Live Indicator */
.live-indicator {
  position: fixed;
  top: 20px;
  left: 20px;
  background: linear-gradient(45deg, var(--casino-red), var(--casino-gold));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
  z-index: 1000;
  display: none;
  animation: pulse 2s infinite;
  border: 2px solid var(--casino-gold);
}

.live-indicator.active {
  display: block;
}

.live-indicator::before {
  content: '🎰 ';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .roulette-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .casino-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .casino-header h1 {
    font-size: 1.4rem;
  }

  .casino-main {
    padding: 1rem;
  }

  .roulette-wheel-container {
    padding: 1rem;
  }

  #roulette-wheel {
    width: 300px;
    height: 300px;
  }

  .outside-bets {
    grid-template-columns: repeat(2, 1fr);
  }

  .betting-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .blackjack-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .slots-display {
    gap: 0.5rem;
  }

  .reel {
    width: 80px;
    height: 120px;
  }

  .symbol {
    font-size: 2rem;
    padding: 0.5rem;
  }

  .bet-options {
    grid-template-columns: 1fr;
  }

  .dice-controls {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .casino-nav {
    flex-wrap: wrap;
  }

  .nav-btn {
    font-size: 0.9rem;
    padding: 0.75rem 0.5rem;
  }

  .chat-controls {
    flex-direction: column;
  }
}

/* Browser Source Mode */
body.browser-source {
  background: transparent !important;
}

body.browser-source #casino-container {
  background: transparent !important;
  border: none !important;
}

body.browser-source .casino-header {
  background: rgba(220, 20, 60, 0.9) !important;
  backdrop-filter: blur(10px);
}

body.browser-source .autopilot-toggle {
  display: none !important;
}

/* Special Effects */
@keyframes jackpot {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes coinFlip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(180deg); }
  100% { transform: rotateY(360deg); }
}

@keyframes winGlow {
  0%, 100% { box-shadow: 0 0 20px var(--casino-gold); }
  50% { box-shadow: 0 0 40px var(--casino-gold), 0 0 60px var(--casino-gold); }
}

.jackpot-animation {
  animation: jackpot 0.5s ease-in-out 3;
}

.win-glow {
  animation: winGlow 2s ease-in-out 3;
}

.coin-flip {
  animation: coinFlip 1s ease-in-out;
}

/* Chip Stack Animation */
@keyframes chipStack {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.chip-animation {
  animation: chipStack 0.5s ease-in-out;
}

/* ===== TIKTOK LIVE STYLES ===== */

/* Live Indicator */
.live-indicator {
  font-weight: bold;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-size: 0.9rem;
}

.live-indicator.live {
  background: linear-gradient(45deg, #ff0000, #ff4444) !important;
  color: white !important;
  border: 2px solid #ff0000 !important;
  animation: livePulse 2s infinite;
  box-shadow: 0 0 15px rgba(255, 0, 0, 0.5);
}

.live-indicator.offline {
  background: rgba(100, 100, 100, 0.3) !important;
  color: #ccc !important;
  border: 2px solid #666 !important;
}

@keyframes livePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* TikTok Connection Panel */
.tiktok-connection-panel {
  background: linear-gradient(135deg, #16213e 0%, #0f3460 100%);
  padding: 1.5rem 2rem;
  border-bottom: 2px solid rgba(255, 215, 0, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.tiktok-connection-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.connection-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.username-input {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.username-input:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
  background: rgba(255, 255, 255, 0.15);
}

.username-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.connect-btn, .disconnect-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
}

.connect-btn {
  background: linear-gradient(45deg, #ff0000, #ff4444);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
}

.connect-btn:hover {
  background: linear-gradient(45deg, #ff4444, #ff6666);
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(255, 0, 0, 0.5);
}

.connect-btn:active {
  transform: translateY(0);
}

.disconnect-btn {
  background: linear-gradient(45deg, #666, #888);
  color: white;
  box-shadow: 0 4px 15px rgba(100, 100, 100, 0.3);
}

.disconnect-btn:hover {
  background: linear-gradient(45deg, #888, #aaa);
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(100, 100, 100, 0.4);
}

.connection-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  position: relative;
  z-index: 2;
}

/* Responsive pentru TikTok Panel */
@media (max-width: 768px) {
  .tiktok-connection-panel {
    padding: 1rem;
  }

  .connection-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .username-input {
    width: 100%;
  }

  .connect-btn, .disconnect-btn {
    width: 100%;
    padding: 1rem;
  }
}

/* TikTok Live Music Battle Styles */
:root {
  --music-primary: #ff1744;
  --music-secondary: #00e676;
  --music-accent: #ffc107;
  --music-purple: #9c27b0;
  --music-blue: #2196f3;
  --background-dark: #0a0a0a;
  --background-music: #1a0a1a;
  --background-card: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #333333;

  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: radial-gradient(circle at center, var(--background-music) 0%, var(--background-dark) 100%);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

#music-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: var(--background-music);
  border: 2px solid var(--music-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 20px var(--music-primary);
}

.music-header {
  background: linear-gradient(90deg, var(--music-primary), var(--music-purple), var(--music-blue));
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(255, 23, 68, 0.5);
}

.music-header h1 {
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: musicGlow 2s infinite alternate;
}

@keyframes musicGlow {
  0% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px #ff1744; }
  100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 20px #ff1744, 0 0 30px #ff1744; }
}

.music-status {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.music-status span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--music-accent);
}

.autopilot-toggle {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--music-primary), var(--music-purple));
  border: 2px solid var(--music-accent);
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1500;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px var(--music-primary);
  animation: pulse 2s infinite;
}

.autopilot-toggle:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 0 30px var(--music-primary);
}

.music-main {
  flex: 1;
  padding: 2rem;
  position: relative;
  overflow-y: auto;
  background: radial-gradient(ellipse at center, rgba(255, 23, 68, 0.1) 0%, transparent 70%);
}

.music-section {
  display: none;
  width: 100%;
  height: 100%;
  animation: fadeIn 0.5s ease-in-out;
}

.music-section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Guess Section */
.guess-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: 100%;
}

.music-player {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--music-primary);
  box-shadow: 0 0 20px rgba(255, 23, 68, 0.3);
}

.now-playing {
  text-align: center;
}

.song-info {
  margin-bottom: 2rem;
}

#song-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--music-accent);
  margin-bottom: 0.5rem;
}

#song-artist {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.player-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.play-btn, .stop-btn {
  background: linear-gradient(45deg, var(--music-primary), var(--music-purple));
  border: 2px solid var(--music-accent);
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1rem 2rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.play-btn:hover, .stop-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 23, 68, 0.6);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--background-dark);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--music-accent);
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--music-primary), var(--music-accent));
  width: 0%;
  transition: width 0.3s ease;
}

.guess-options {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.guess-options h3 {
  color: var(--music-accent);
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.choices-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.choice-btn {
  background: var(--background-dark);
  border: 2px solid var(--music-primary);
  color: var(--text-primary);
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  text-align: left;
}

.choice-btn:hover {
  background: var(--music-primary);
  color: white;
  transform: translateY(-2px);
}

.choice-btn.correct {
  background: var(--music-secondary);
  border-color: var(--music-secondary);
  animation: correctPulse 1s ease-in-out;
}

.choice-btn.incorrect {
  background: #ff5722;
  border-color: #ff5722;
  animation: incorrectShake 0.5s ease-in-out;
}

@keyframes correctPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.guess-timer {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--music-accent);
}

#guess-time {
  font-size: 2rem;
  color: var(--music-primary);
}

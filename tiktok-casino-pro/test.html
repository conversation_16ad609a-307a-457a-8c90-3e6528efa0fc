<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test TikTok Casino PRO</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 2rem;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #16213e;
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #ffd700;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .test-button {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #ffd700;
        }
        .success { border-left-color: #00ff00; }
        .error { border-left-color: #ff0000; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎰 Test TikTok Casino PRO</h1>
        <p>Testează funcționalitatea aplicației cu username-ul: <strong>xo.xo.xo.xo.xo.xo.xo.xol</strong></p>
        
        <div class="test-section">
            <h3>🔍 Test Validare Username</h3>
            <button class="test-button" onclick="testUsernameValidation()">Testează Username</button>
            <div id="username-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🎮 Test Comenzi Joc</h3>
            <button class="test-button" onclick="testGameCommands()">Testează Comenzi</button>
            <div id="commands-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Test Conectare</h3>
            <button class="test-button" onclick="testConnection()">Testează Conectare</button>
            <div id="connection-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📱 Deschide Aplicația</h3>
            <button class="test-button" onclick="openApp()">Deschide Casino</button>
            <p>Aplicația va fi deschisă într-un tab nou. Încearcă să te conectezi cu username-ul tău!</p>
        </div>
    </div>

    <script>
        // Test validare username
        function testUsernameValidation() {
            const result = document.getElementById('username-result');
            result.style.display = 'block';
            
            const testUsername = 'xo.xo.xo.xo.xo.xo.xo.xol';
            
            // Simulează validarea
            const validation = validateUsername(testUsername);
            
            if (validation.valid) {
                result.className = 'result success';
                result.innerHTML = `
                    ✅ Username valid!<br>
                    Input: "${testUsername}"<br>
                    Output: "${validation.username}"<br>
                    Status: ACCEPTAT
                `;
            } else {
                result.className = 'result error';
                result.innerHTML = `
                    ❌ Username invalid!<br>
                    Input: "${testUsername}"<br>
                    Error: ${validation.error}
                `;
            }
        }
        
        // Funcție de validare (copiată din aplicație)
        function validateUsername(username) {
            if (!username || typeof username !== 'string') {
                return { valid: false, error: 'Username invalid' };
            }
            
            username = username.replace('@', '').trim();
            
            if (username.length === 0) {
                return { valid: false, error: 'Username nu poate fi gol' };
            }
            
            if (username.length > 24) {
                return { valid: false, error: 'Username prea lung (max 24 caractere)' };
            }
            
            if (!/^[a-zA-Z0-9._]+$/.test(username)) {
                return { valid: false, error: 'Username poate conține doar litere, cifre, punct și underscore' };
            }
            
            return { valid: true, username };
        }
        
        // Test comenzi joc
        function testGameCommands() {
            const result = document.getElementById('commands-result');
            result.style.display = 'block';
            result.className = 'result success';
            
            const commands = [
                '!spin - Joacă la ruletă',
                '!blackjack - Joacă blackjack',
                '!slots - Joacă la păcănele',
                '!dice - Joacă cu zarurile',
                '!balance - Verifică creditele',
                '!help - Lista comenzilor'
            ];
            
            result.innerHTML = `
                ✅ Comenzi disponibile:<br><br>
                ${commands.map(cmd => `• ${cmd}`).join('<br>')}
                <br><br>
                💡 Aceste comenzi pot fi folosite în chat pentru a juca!
            `;
        }
        
        // Test conectare
        function testConnection() {
            const result = document.getElementById('connection-result');
            result.style.display = 'block';
            result.className = 'result';
            
            result.innerHTML = `
                🔄 Simulez conectarea...<br>
                Username: xo.xo.xo.xo.xo.xo.xo.xol<br>
                Status: În curs...
            `;
            
            setTimeout(() => {
                result.className = 'result success';
                result.innerHTML = `
                    ✅ Conectare simulată cu succes!<br>
                    Username: xo.xo.xo.xo.xo.xo.xo.xol<br>
                    Status: CONECTAT (DEMO MODE)<br>
                    Credite demo: 1000<br>
                    Jocuri disponibile: 4<br><br>
                    💡 În aplicația reală, aceasta se va conecta la TikTok Live!
                `;
            }, 2000);
        }
        
        // Deschide aplicația
        function openApp() {
            window.open('http://localhost:5173/', '_blank');
        }
    </script>
</body>
</html>

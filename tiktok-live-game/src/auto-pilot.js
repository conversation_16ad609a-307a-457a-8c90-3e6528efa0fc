// Auto-Pilot System pentru TikTok Live Game
// Automatizează complet interacțiunea cu publicul

export class AutoPilot {
  constructor(gameInstance) {
    this.game = gameInstance;
    this.isActive = false;
    this.streamerUsername = '';
    this.autoSettings = {
      wheelSpinInterval: 30000, // 30 secunde
      voteInterval: 60000, // 1 minut
      storyUpdateInterval: 120000, // 2 minute
      chatResponseRate: 0.8, // 80% din mesaje primesc răspuns
      giftResponseRate: 1.0, // 100% din cadouri primesc răspuns
      viewerEngagementLevel: 'high' // low, medium, high
    };
    
    this.autoTimers = new Map();
    this.conversationContext = [];
    this.currentAdventure = {
      theme: 'fantasy',
      location: 'mysterious forest',
      character: 'brave explorer',
      objective: 'find the ancient treasure'
    };
    
    this.responseTemplates = {
      wheelSpin: [
        "Să vedem ce ne rezervă roata aventurii! 🎡",
        "Gata pentru următoarea aventură? Hai să învârtim! 🌟",
        "Roata destinului decide pentru noi! ✨",
        "Ce aventură ne așteaptă? Să descoperim împreună! 🎮"
      ],
      voteStart: [
        "Timpul pentru o decizie importantă! Votați acum! 🗳️",
        "Comunitatea decide! Care e următorul pas? 🤔",
        "Votul vostru contează! Să alegem împreună! 💫",
        "Democrația în acțiune! Ce facem mai departe? 🎯"
      ],
      giftResponse: [
        "Mulțumesc pentru {gift}! Ești incredibil! ❤️",
        "Wow, {gift}! Comunitatea asta e fantastică! 🎉",
        "Apreciez mult {gift}! Să continuăm aventura! ✨",
        "Genial {gift}! Energia voastră mă motivează! 🚀"
      ],
      chatResponse: [
        "Exact! Gândești ca un adevărat aventurier! 🏆",
        "Ideea ta e genială! Să o implementăm! 💡",
        "Îmi place entuziasmul tău! Să mergem mai departe! 🔥",
        "Perfect! Asta e spiritul de echipă! 🤝"
      ],
      storyProgression: [
        "Povestea noastră devine din ce în ce mai captivantă...",
        "Aventura continuă cu o întorsătură neașteptată...",
        "Eroii noștri se confruntă cu o nouă provocare...",
        "Misterul se adâncește pe măsură ce explorăm..."
      ]
    };
    
    this.adventureScenarios = {
      fantasy: [
        "Explorăm o peșteră misterioasă plină de cristale magice",
        "Traversăm o pădure fermecată cu creaturi mitice",
        "Escaladăm un munte înalt pentru a găsi templul antic",
        "Navigăm pe o mare furtunoasă către insula secretă",
        "Intrăm într-un castel bântuit să găsim comoara pierdută"
      ],
      scifi: [
        "Explorăm o stație spațială abandonată",
        "Aterizăm pe o planetă necunoscută",
        "Investigăm un semnal misterios din cosmos",
        "Călătorim prin timp pentru a salva viitorul",
        "Descoperim o civilizație extraterestră"
      ],
      adventure: [
        "Explorăm o junglă periculoasă",
        "Căutăm o comoară pierdută în deșert",
        "Escaladăm cel mai înalt vârf din lume",
        "Navigăm prin rapide periculoase",
        "Explorăm ruinele unei civilizații antice"
      ]
    };
  }

  // Pornește sistemul auto-pilot
  start(streamerUsername = '') {
    this.streamerUsername = streamerUsername || 'Streamer';
    this.isActive = true;
    
    console.log(`🤖 Auto-Pilot activat pentru ${this.streamerUsername}`);
    this.game.addChatMessage('Auto-Pilot', `🤖 Sistemul automat este activ! Bun venit la aventura live a lui ${this.streamerUsername}!`);
    
    this.startAutoInteractions();
    this.startChatMonitoring();
    this.startViewerEngagement();
    this.initializeAdventure();
  }

  // Oprește sistemul auto-pilot
  stop() {
    this.isActive = false;
    this.autoTimers.forEach(timer => clearInterval(timer));
    this.autoTimers.clear();
    
    console.log('🤖 Auto-Pilot dezactivat');
    this.game.addChatMessage('Auto-Pilot', '🤖 Sistemul automat a fost oprit. Mulțumim pentru aventură!');
  }

  // Inițializează interacțiunile automate
  startAutoInteractions() {
    // Auto wheel spin
    const wheelTimer = setInterval(() => {
      if (this.isActive && this.game.currentSection === 'wheel-section' && !this.game.isSpinning) {
        this.autoSpinWheel();
      }
    }, this.autoSettings.wheelSpinInterval);
    this.autoTimers.set('wheel', wheelTimer);

    // Auto voting
    const voteTimer = setInterval(() => {
      if (this.isActive && this.game.currentSection === 'voting-section' && !this.game.voteTimer) {
        this.autoStartVote();
      }
    }, this.autoSettings.voteInterval);
    this.autoTimers.set('vote', voteTimer);

    // Auto story updates
    const storyTimer = setInterval(() => {
      if (this.isActive) {
        this.autoUpdateStory();
      }
    }, this.autoSettings.storyUpdateInterval);
    this.autoTimers.set('story', storyTimer);

    // Auto section switching pentru varietate
    const switchTimer = setInterval(() => {
      if (this.isActive) {
        this.autoSwitchSection();
      }
    }, 45000); // 45 secunde
    this.autoTimers.set('switch', switchTimer);
  }

  // Monitorizează chat-ul și răspunde automat
  startChatMonitoring() {
    const originalAddChatMessage = this.game.addChatMessage.bind(this.game);
    this.game.addChatMessage = (username, message) => {
      originalAddChatMessage(username, message);
      
      if (this.isActive && username !== 'Auto-Pilot' && username !== 'System' && username !== this.streamerUsername) {
        this.processIncomingMessage(username, message);
      }
    };
  }

  // Procesează mesajele primite și răspunde inteligent
  processIncomingMessage(username, message) {
    // Șansă de răspuns bazată pe setări
    if (Math.random() > this.autoSettings.chatResponseRate) return;

    setTimeout(() => {
      let response = this.generateContextualResponse(username, message);
      this.game.addChatMessage(this.streamerUsername, response);
      
      // Actualizează contextul conversației
      this.conversationContext.push({ username, message, timestamp: Date.now() });
      if (this.conversationContext.length > 10) {
        this.conversationContext.shift();
      }
    }, 1000 + Math.random() * 3000); // Răspuns între 1-4 secunde
  }

  // Generează răspunsuri contextuale inteligente
  generateContextualResponse(username, message) {
    const msg = message.toLowerCase();
    
    // Răspunsuri specifice pentru comenzi
    if (msg.includes('!spin') || msg.includes('roata') || msg.includes('wheel')) {
      return this.getRandomResponse('wheelSpin');
    }
    
    if (msg.includes('!vote') || msg.includes('vot') || msg.includes('alegere')) {
      return this.getRandomResponse('voteStart');
    }
    
    // Răspunsuri pentru întrebări
    if (msg.includes('?') || msg.includes('cum') || msg.includes('ce') || msg.includes('când')) {
      return `@${username} Excelentă întrebare! ${this.getRandomResponse('chatResponse')}`;
    }
    
    // Răspunsuri pentru complimente
    if (msg.includes('cool') || msg.includes('tare') || msg.includes('genial') || msg.includes('awesome')) {
      return `@${username} Mulțumesc! ${this.getRandomResponse('chatResponse')}`;
    }
    
    // Răspunsuri pentru aventură
    if (msg.includes('aventura') || msg.includes('poveste') || msg.includes('story')) {
      return `@${username} ${this.getRandomResponse('storyProgression')}`;
    }
    
    // Răspuns generic personalizat
    const responses = [
      `@${username} Îmi place energia ta! Să continuăm aventura! 🎮`,
      `@${username} Ești parte din echipa noastră de aventurieri! 🌟`,
      `@${username} Contribuția ta face diferența! Să mergem mai departe! 🚀`,
      `@${username} Spiritul tău de aventură e contagios! ✨`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
  }

  // Învârte roata automat cu comentarii
  autoSpinWheel() {
    const announcement = this.getRandomResponse('wheelSpin');
    this.game.addChatMessage(this.streamerUsername, announcement);
    
    setTimeout(() => {
      this.game.spinWheel();
    }, 2000);
  }

  // Pornește votul automat
  autoStartVote() {
    const scenarios = this.adventureScenarios[this.currentAdventure.theme];
    const options = this.getRandomElements(scenarios, 3);
    
    const announcement = this.getRandomResponse('voteStart');
    this.game.addChatMessage(this.streamerUsername, announcement);
    
    setTimeout(() => {
      // Simulează pornirea votului cu opțiuni
      this.game.switchSection('voting-section');
      
      // Setează întrebarea și opțiunile
      document.getElementById('voting-question').textContent = 'Ce aventură alegem următoarea?';
      this.game.votes = {};
      this.game.voteTimeLeft = 30;

      const container = document.getElementById('voting-options');
      container.innerHTML = '';
      
      options.forEach((option, index) => {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'vote-option';
        optionDiv.innerHTML = `
          <div class="vote-text">${option}</div>
          <div class="vote-count">0 votes</div>
          <div class="vote-progress" style="width: 0%"></div>
        `;
        optionDiv.addEventListener('click', () => this.game.castVote(index, option));
        container.appendChild(optionDiv);
        this.game.votes[index] = { option, count: 0 };
      });

      // Pornește timer-ul
      this.game.voteTimer = setInterval(() => {
        this.game.voteTimeLeft--;
        document.getElementById('vote-timer').textContent = `${this.game.voteTimeLeft}s`;
        
        // Simulează voturi din partea publicului
        if (Math.random() < 0.3) {
          const randomOption = Math.floor(Math.random() * options.length);
          this.game.castVote(randomOption, options[randomOption]);
        }
        
        if (this.game.voteTimeLeft <= 0) {
          this.game.endVote();
        }
      }, 1000);
    }, 3000);
  }

  // Actualizează povestea automat
  autoUpdateStory() {
    const storyElements = [
      `${this.currentAdventure.character} continuă călătoria prin ${this.currentAdventure.location}...`,
      `O nouă provocare apare în calea eroilor noștri...`,
      `Misterul se adâncește pe măsură ce descoperim indicii noi...`,
      `Aventura ia o întorsătură neașteptată...`,
      `Echipa noastră de aventurieri se confruntă cu o decizie dificilă...`
    ];
    
    const newChapter = storyElements[Math.floor(Math.random() * storyElements.length)];
    this.game.addToStory(newChapter);
    
    const announcement = this.getRandomResponse('storyProgression');
    this.game.addChatMessage(this.streamerUsername, announcement);
  }

  // Schimbă secțiunea automat pentru varietate
  autoSwitchSection() {
    const sections = ['wheel-section', 'voting-section', 'story-section'];
    const currentIndex = sections.indexOf(this.game.currentSection);
    const nextIndex = (currentIndex + 1) % sections.length;
    
    this.game.switchSection(sections[nextIndex]);
    
    const sectionNames = {
      'wheel-section': 'Roata Aventurii',
      'voting-section': 'Votul Comunității',
      'story-section': 'Povestea Noastră'
    };
    
    this.game.addChatMessage(this.streamerUsername, 
      `Să trecem la ${sectionNames[sections[nextIndex]]}! 🎯`);
  }

  // Inițializează aventura
  initializeAdventure() {
    const themes = Object.keys(this.adventureScenarios);
    this.currentAdventure.theme = themes[Math.floor(Math.random() * themes.length)];
    
    const welcomeMessage = `🎮 Bun venit la aventura live! Sunt ${this.streamerUsername} și astăzi explorăm împreună o lume ${this.currentAdventure.theme}! Să începem! ✨`;
    this.game.addChatMessage(this.streamerUsername, welcomeMessage);
    
    setTimeout(() => {
      this.game.addToStory(`Aventura începe! ${this.currentAdventure.character} se pregătește să exploreze ${this.currentAdventure.location} în căutarea ${this.currentAdventure.objective}.`);
    }, 3000);
  }

  // Pornește engagement-ul cu publicul
  startViewerEngagement() {
    setInterval(() => {
      if (this.isActive && Math.random() < 0.2) { // 20% șansă la fiecare 10 secunde
        this.triggerEngagementAction();
      }
    }, 10000);
  }

  // Acțiuni de engagement
  triggerEngagementAction() {
    const actions = [
      () => this.game.addChatMessage(this.streamerUsername, "Ce părere aveți despre această aventură? Scrieți în chat! 💬"),
      () => this.game.addChatMessage(this.streamerUsername, "Cine e gata pentru următoarea provocare? React cu emoji! 🎯"),
      () => this.game.addChatMessage(this.streamerUsername, "Comunitatea asta e incredibilă! Mulțumesc pentru energie! ❤️"),
      () => this.game.addChatMessage(this.streamerUsername, "Să vedem cine urmărește din ce țară! Scrieți de unde sunteți! 🌍")
    ];
    
    const randomAction = actions[Math.floor(Math.random() * actions.length)];
    randomAction();
  }

  // Utility functions
  getRandomResponse(category) {
    const responses = this.responseTemplates[category];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  getRandomElements(array, count) {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  // Setări auto-pilot
  updateSettings(newSettings) {
    this.autoSettings = { ...this.autoSettings, ...newSettings };
  }

  // Status auto-pilot
  getStatus() {
    return {
      isActive: this.isActive,
      streamerUsername: this.streamerUsername,
      currentAdventure: this.currentAdventure,
      settings: this.autoSettings,
      activeTimers: this.autoTimers.size
    };
  }
}

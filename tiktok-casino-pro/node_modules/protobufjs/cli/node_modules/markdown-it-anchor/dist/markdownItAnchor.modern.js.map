{"version": 3, "file": "markdownItAnchor.modern.js", "sources": ["../permalink.js", "../index.js"], "sourcesContent": ["let emittedWarning = false\n\nconst position = {\n  false: 'push',\n  true: 'unshift',\n  after: 'push',\n  before: 'unshift'\n}\n\nconst permalinkSymbolMeta = {\n  isPermalinkSymbol: true\n}\n\nexport function legacy (slug, opts, state, idx) {\n  if (!emittedWarning) {\n    const warningText = 'Using deprecated markdown-it-anchor permalink option, see https://github.com/valeriangalliat/markdown-it-anchor#todo-anchor-or-file'\n\n    if (typeof process === 'object' && process && process.emitWarning) {\n      process.emitWarning(warningText)\n    } else {\n      console.warn(warningText)\n    }\n\n    emittedWarning = true\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.permalinkClass ? [['class', opts.permalinkClass]] : []),\n        ['href', opts.permalinkHref(slug, state)],\n        ...Object.entries(opts.permalinkAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_block', '', 0), { content: opts.permalinkSymbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.permalinkSpace) {\n    state.tokens[idx + 1].children[position[opts.permalinkBefore]](Object.assign(new state.Token('text', '', 0), { content: ' ' }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.permalinkBefore]](...linkTokens)\n}\n\nexport function renderHref (slug) {\n  return `#${slug}`\n}\n\nexport function renderAttrs (slug) {\n  return {}\n}\n\nconst commonDefaults = {\n  class: 'header-anchor',\n  symbol: '#',\n  renderHref,\n  renderAttrs\n}\n\nexport function makePermalink (renderPermalinkImpl) {\n  function renderPermalink (opts) {\n    opts = Object.assign({}, renderPermalink.defaults, opts)\n\n    return (slug, anchorOpts, state, idx) => {\n      return renderPermalinkImpl(slug, opts, anchorOpts, state, idx)\n    }\n  }\n\n  renderPermalink.defaults = Object.assign({}, commonDefaults)\n  renderPermalink.renderPermalinkImpl = renderPermalinkImpl\n\n  return renderPermalink\n}\n\nexport const linkInsideHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...(opts.ariaHidden ? [['aria-hidden', 'true']] : []),\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_inline', '', 0), { content: opts.symbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.space) {\n    const space = typeof opts.space === 'string' ? opts.space : ' '\n    const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n    state.tokens[idx + 1].children[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.placement]](...linkTokens)\n})\n\nObject.assign(linkInsideHeader.defaults, {\n  space: true,\n  placement: 'after',\n  ariaHidden: false\n})\n\nexport const ariaHidden = makePermalink(linkInsideHeader.renderPermalinkImpl)\n\nariaHidden.defaults = Object.assign({}, linkInsideHeader.defaults, {\n  ariaHidden: true\n})\n\nexport const headerLink = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    ...(opts.safariReaderFix ? [new state.Token('span_open', 'span', 1)] : []),\n    ...state.tokens[idx + 1].children,\n    ...(opts.safariReaderFix ? [new state.Token('span_close', 'span', -1)] : []),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  state.tokens[idx + 1] = Object.assign(new state.Token('inline', '', 0), {\n    children: linkTokens\n  })\n})\n\nObject.assign(headerLink.defaults, {\n  safariReaderFix: false\n})\n\nexport const linkAfterHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  if (!['visually-hidden', 'aria-label', 'aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called with unknown style option \\`${opts.style}\\``)\n  }\n\n  if (!['aria-describedby', 'aria-labelledby'].includes(opts.style) && !opts.assistiveText) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called without the \\`assistiveText\\` option in \\`${opts.style}\\` style`)\n  }\n\n  if (opts.style === 'visually-hidden' && !opts.visuallyHiddenClass) {\n    throw new Error('`permalink.linkAfterHeader` called without the `visuallyHiddenClass` option in `visually-hidden` style')\n  }\n\n  const title = state.tokens[idx + 1]\n    .children\n    .filter(token => token.type === 'text' || token.type === 'code_inline')\n    .reduce((acc, t) => acc + t.content, '')\n\n  const subLinkTokens = []\n  const linkAttrs = []\n\n  if (opts.class) {\n    linkAttrs.push(['class', opts.class])\n  }\n\n  linkAttrs.push(['href', opts.renderHref(slug, state)])\n  linkAttrs.push(...Object.entries(opts.renderAttrs(slug, state)))\n\n  if (opts.style === 'visually-hidden') {\n    subLinkTokens.push(\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['class', opts.visuallyHiddenClass]],\n      }),\n      Object.assign(new state.Token('text', '', 0), {\n        content: opts.assistiveText(title)\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n\n    if (opts.space) {\n      const space = typeof opts.space === 'string' ? opts.space : ' '\n      const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n      subLinkTokens[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n    }\n\n    subLinkTokens[position[opts.placement]](\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['aria-hidden', 'true']],\n      }),\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n  } else {\n    subLinkTokens.push(\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      })\n    )\n  }\n\n  if (opts.style === 'aria-label') {\n    linkAttrs.push(['aria-label', opts.assistiveText(title)])\n  } else if (['aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    linkAttrs.push([opts.style, slug])\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: linkAttrs\n    }),\n    ...subLinkTokens,\n    new state.Token('link_close', 'a', -1),\n  ]\n\n  state.tokens.splice(idx + 3, 0, ...linkTokens)\n\n  if (opts.wrapper) {\n    state.tokens.splice(idx, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[0] + '\\n'\n    }))\n\n    state.tokens.splice(idx + 3 + linkTokens.length + 1, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[1] + '\\n'\n    }))\n  }\n})\n\nObject.assign(linkAfterHeader.defaults, {\n  style: 'visually-hidden',\n  space: true,\n  placement: 'after',\n  wrapper: null\n})\n", "import * as permalink from './permalink'\n\nconst slugify = (s) => encodeURIComponent(String(s).trim().toLowerCase().replace(/\\s+/g, '-'))\n\nfunction getTokensText (tokens) {\n  return tokens\n    .filter(t => ['text', 'code_inline'].includes(t.type))\n    .map(t => t.content)\n    .join('')\n}\n\nfunction uniqueSlug (slug, slugs, failOnNonUnique, startIndex) {\n  let uniq = slug\n  let i = startIndex\n\n  if (failOnNonUnique && Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n    throw new Error(`User defined \\`id\\` attribute \\`${slug}\\` is not unique. Please fix it in your Markdown to continue.`)\n  } else {\n    while (Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n      uniq = `${slug}-${i}`\n      i += 1\n    }\n  }\n\n  slugs[uniq] = true\n\n  return uniq\n}\n\nconst isLevelSelectedNumber = selection => level => level >= selection\nconst isLevelSelectedArray = selection => level => selection.includes(level)\n\nfunction anchor (md, opts) {\n  opts = Object.assign({}, anchor.defaults, opts)\n\n  md.core.ruler.push('anchor', state => {\n    const slugs = {}\n    const tokens = state.tokens\n\n    const isLevelSelected = Array.isArray(opts.level)\n      ? isLevelSelectedArray(opts.level)\n      : isLevelSelectedNumber(opts.level)\n\n    for (let idx = 0; idx < tokens.length; idx++) {\n      const token = tokens[idx]\n\n      if (token.type !== 'heading_open') {\n        continue\n      }\n\n      if (!isLevelSelected(Number(token.tag.substr(1)))) {\n        continue\n      }\n\n      // Aggregate the next token children text.\n      const title = opts.getTokensText(tokens[idx + 1].children)\n\n      let slug = token.attrGet('id')\n\n      if (slug == null) {\n        slug = uniqueSlug(opts.slugify(title), slugs, false, opts.uniqueSlugStartIndex)\n      } else {\n        slug = uniqueSlug(slug, slugs, true, opts.uniqueSlugStartIndex)\n      }\n\n      token.attrSet('id', slug)\n\n      if (opts.tabIndex !== false) {\n        token.attrSet('tabindex', `${opts.tabIndex}`)\n      }\n\n      if (typeof opts.permalink === 'function') {\n        opts.permalink(slug, opts, state, idx)\n      } else if (opts.permalink) {\n        opts.renderPermalink(slug, opts, state, idx)\n      } else if (opts.renderPermalink && opts.renderPermalink !== permalink.legacy) {\n        opts.renderPermalink(slug, opts, state, idx)\n      }\n\n      // A permalink renderer could modify the `tokens` array so\n      // make sure to get the up-to-date index on each iteration.\n      idx = tokens.indexOf(token)\n\n      if (opts.callback) {\n        opts.callback(token, { slug, title })\n      }\n    }\n  })\n}\n\nanchor.permalink = permalink\n\nanchor.defaults = {\n  level: 1,\n  slugify,\n  uniqueSlugStartIndex: 1,\n  tabIndex: '-1',\n  getTokensText,\n\n  // Legacy options.\n  permalink: false,\n  renderPermalink: permalink.legacy,\n  permalinkClass: permalink.ariaHidden.defaults.class,\n  permalinkSpace: permalink.ariaHidden.defaults.space,\n  permalinkSymbol: '¶',\n  permalinkBefore: permalink.ariaHidden.defaults.placement === 'before',\n  permalinkHref: permalink.ariaHidden.defaults.renderHref,\n  permalinkAttrs: permalink.ariaHidden.defaults.renderAttrs\n}\n\n// Dirty hack to make `import anchor from 'markdown-it-anchor'` work with\n// TypeScript which doesn't support the `module` field of `package.json` and\n// will always get the CommonJS version which otherwise wouldn't have a\n// `default` key, resulting in markdown-it-anchor being undefined when being\n// imported that way.\nanchor.default = anchor\n\nexport default anchor\n"], "names": ["emitted<PERSON><PERSON>ning", "position", "false", "true", "after", "before", "permalinkSymbolMeta", "isPermalinkSymbol", "legacy", "slug", "opts", "state", "idx", "warningText", "process", "emitWarning", "console", "warn", "linkTokens", "Object", "assign", "Token", "attrs", "permalinkClass", "permalinkHref", "entries", "permalinkAttrs", "content", "permalinkSymbol", "meta", "permalinkSpace", "tokens", "children", "permalinkBefore", "renderHref", "renderAttrs", "commonDefaults", "class", "symbol", "makePermalink", "renderPermalinkImpl", "renderPermalink", "defaults", "anchorOpts", "linkInsideHeader", "ariaHidden", "space", "placement", "headerLink", "safariReaderFix", "linkAfterHeader", "includes", "style", "Error", "assistiveText", "visuallyHiddenClass", "title", "filter", "token", "type", "reduce", "acc", "t", "subLinkTokens", "linkAttrs", "push", "splice", "wrapper", "length", "uniqueSlug", "slugs", "failOnNonUnique", "startIndex", "uniq", "i", "prototype", "hasOwnProperty", "call", "anchor", "md", "core", "ruler", "isLevelSelected", "Array", "isArray", "level", "selection", "isLevelSelectedNumber", "Number", "tag", "substr", "getTokensText", "attrGet", "slugify", "uniqueSlugStartIndex", "attrSet", "tabIndex", "permalink", "indexOf", "callback", "s", "encodeURIComponent", "String", "trim", "toLowerCase", "replace", "map", "join", "default"], "mappings": "AAAA,IAAIA,GAAiB,EAErB,MAAMC,EAAW,CACfC,MAAO,OACPC,KAAM,UACNC,MAAO,OACPC,OAAQ,WAGJC,EAAsB,CAC1BC,mBAAmB,YAGLC,EAAQC,EAAMC,EAAMC,EAAOC,GACzC,IAAKZ,EAAgB,CACnB,MAAMa,EAAc,sIAEG,iBAAZC,SAAwBA,SAAWA,QAAQC,YACpDD,QAAQC,YAAYF,GAEpBG,QAAQC,KAAKJ,GAGfb,GAAiB,EAGnB,MAAMkB,EAAa,CACjBC,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAKa,eAAiB,CAAC,CAAC,QAASb,EAAKa,iBAAmB,GAC7D,CAAC,OAAQb,EAAKc,cAAcf,EAAME,OAC/BQ,OAAOM,QAAQf,EAAKgB,eAAejB,EAAME,OAGhDQ,OAAOC,OAAO,IAAIT,EAAMU,MAAM,aAAc,GAAI,GAAI,CAAEM,QAASjB,EAAKkB,gBAAiBC,KAAMvB,IAC3F,IAAIK,EAAMU,MAAM,aAAc,KAAM,IAGlCX,EAAKoB,gBACPnB,EAAMoB,OAAOnB,EAAM,GAAGoB,SAAS/B,EAASS,EAAKuB,kBAAkBd,OAAOC,OAAO,IAAIT,EAAMU,MAAM,OAAQ,GAAI,GAAI,CAAEM,QAAS,OAG1HhB,EAAMoB,OAAOnB,EAAM,GAAGoB,SAAS/B,EAASS,EAAKuB,qBAAqBf,YAGpDgB,EAAYzB,GAC1B,MAAQ,IAAGA,WAGG0B,EAAa1B,GAC3B,MAAO,GAGT,MAAM2B,EAAiB,CACrBC,MAAO,gBACPC,OAAQ,IACRJ,WAAAA,EACAC,YAAAA,YAGcI,EAAeC,GAC7B,SAASC,EAAiB/B,GAGxB,OAFAA,EAAOS,OAAOC,OAAO,GAAIqB,EAAgBC,SAAUhC,GAE5C,CAACD,EAAMkC,EAAYhC,EAAOC,IACxB4B,EAAoB/B,EAAMC,EAAMiC,EAAYhC,EAAOC,GAO9D,OAHA6B,EAAgBC,SAAWvB,OAAOC,OAAO,GAAIgB,GAC7CK,EAAgBD,oBAAsBA,EAE/BC,QAGIG,EAAmBL,EAAc,CAAC9B,EAAMC,EAAMiC,EAAYhC,EAAOC,KAC5E,MAAMM,EAAa,CACjBC,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAK2B,MAAQ,CAAC,CAAC,QAAS3B,EAAK2B,QAAU,GAC3C,CAAC,OAAQ3B,EAAKwB,WAAWzB,EAAME,OAC3BD,EAAKmC,WAAa,CAAC,CAAC,cAAe,SAAW,MAC/C1B,OAAOM,QAAQf,EAAKyB,YAAY1B,EAAME,OAG7CQ,OAAOC,OAAO,IAAIT,EAAMU,MAAM,cAAe,GAAI,GAAI,CAAEM,QAASjB,EAAK4B,OAAQT,KAAMvB,IACnF,IAAIK,EAAMU,MAAM,aAAc,KAAM,IAGtC,GAAIX,EAAKoC,MAAO,CACd,MAAMA,EAA8B,iBAAfpC,EAAKoC,MAAqBpC,EAAKoC,MAAQ,IAE5DnC,EAAMoB,OAAOnB,EAAM,GAAGoB,SAAS/B,EAASS,EAAKqC,YAAY5B,OAAOC,OAAO,IAAIT,EAAMU,MAD9C,iBAAfX,EAAKoC,MAAqB,cAAgB,OAC+B,GAAI,GAAI,CAAEnB,QAASmB,KAGlHnC,EAAMoB,OAAOnB,EAAM,GAAGoB,SAAS/B,EAASS,EAAKqC,eAAe7B,KAG9DC,OAAOC,OAAOwB,EAAiBF,SAAU,CACvCI,OAAO,EACPC,UAAW,QACXF,YAAY,UAGDA,EAAaN,EAAcK,EAAiBJ,qBAEzDK,EAAWH,SAAWvB,OAAOC,OAAO,GAAIwB,EAAiBF,SAAU,CACjEG,YAAY,UAGDG,EAAaT,EAAc,CAAC9B,EAAMC,EAAMiC,EAAYhC,EAAOC,KACtE,MAAMM,EAAa,CACjBC,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAK2B,MAAQ,CAAC,CAAC,QAAS3B,EAAK2B,QAAU,GAC3C,CAAC,OAAQ3B,EAAKwB,WAAWzB,EAAME,OAC5BQ,OAAOM,QAAQf,EAAKyB,YAAY1B,EAAME,UAGzCD,EAAKuC,gBAAkB,CAAC,IAAItC,EAAMU,MAAM,YAAa,OAAQ,IAAM,MACpEV,EAAMoB,OAAOnB,EAAM,GAAGoB,YACrBtB,EAAKuC,gBAAkB,CAAC,IAAItC,EAAMU,MAAM,aAAc,QAAS,IAAM,GACzE,IAAIV,EAAMU,MAAM,aAAc,KAAM,IAGtCV,EAAMoB,OAAOnB,EAAM,GAAKO,OAAOC,OAAO,IAAIT,EAAMU,MAAM,SAAU,GAAI,GAAI,CACtEW,SAAUd,MAIdC,OAAOC,OAAO4B,EAAWN,SAAU,CACjCO,iBAAiB,UAGNC,EAAkBX,EAAc,CAAC9B,EAAMC,EAAMiC,EAAYhC,EAAOC,KAC3E,IAAK,CAAC,kBAAmB,aAAc,mBAAoB,mBAAmBuC,SAASzC,EAAK0C,OAC1F,UAAUC,MAAO,oEAAmE3C,EAAK0C,WAG3F,IAAK,CAAC,mBAAoB,mBAAmBD,SAASzC,EAAK0C,SAAW1C,EAAK4C,cACzE,UAAUD,MAAO,kFAAiF3C,EAAK0C,iBAGzG,GAAmB,oBAAf1C,EAAK0C,QAAgC1C,EAAK6C,oBAC5C,UAAUF,MAAM,0GAGlB,MAAMG,EAAQ7C,EAAMoB,OAAOnB,EAAM,GAC9BoB,SACAyB,OAAOC,GAAwB,SAAfA,EAAMC,MAAkC,gBAAfD,EAAMC,MAC/CC,OAAO,CAACC,EAAKC,IAAMD,EAAMC,EAAEnC,QAAS,IAEjCoC,EAAgB,GAChBC,EAAY,GASlB,GAPItD,EAAK2B,OACP2B,EAAUC,KAAK,CAAC,QAASvD,EAAK2B,QAGhC2B,EAAUC,KAAK,CAAC,OAAQvD,EAAKwB,WAAWzB,EAAME,KAC9CqD,EAAUC,QAAQ9C,OAAOM,QAAQf,EAAKyB,YAAY1B,EAAME,KAErC,oBAAfD,EAAK0C,MAA6B,CAWpC,GAVAW,EAAcE,KACZ9C,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,QAASZ,EAAK6C,wBAEzBpC,OAAOC,OAAO,IAAIT,EAAMU,MAAM,OAAQ,GAAI,GAAI,CAC5CM,QAASjB,EAAK4C,cAAcE,KAE9B,IAAI7C,EAAMU,MAAM,aAAc,QAAS,IAGrCX,EAAKoC,MAAO,CACd,MAAMA,EAA8B,iBAAfpC,EAAKoC,MAAqBpC,EAAKoC,MAAQ,IAE5DiB,EAAc9D,EAASS,EAAKqC,YAAY5B,OAAOC,OAAO,IAAIT,EAAMU,MAD7B,iBAAfX,EAAKoC,MAAqB,cAAgB,OACc,GAAI,GAAI,CAAEnB,QAASmB,KAGjGiB,EAAc9D,EAASS,EAAKqC,YAC1B5B,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,cAAe,WAE1BH,OAAOC,OAAO,IAAIT,EAAMU,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASjB,EAAK4B,OACdT,KAAMvB,IAER,IAAIK,EAAMU,MAAM,aAAc,QAAS,SAGzC0C,EAAcE,KACZ9C,OAAOC,OAAO,IAAIT,EAAMU,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASjB,EAAK4B,OACdT,KAAMvB,KAKO,eAAfI,EAAK0C,MACPY,EAAUC,KAAK,CAAC,aAAcvD,EAAK4C,cAAcE,KACxC,CAAC,mBAAoB,mBAAmBL,SAASzC,EAAK0C,QAC/DY,EAAUC,KAAK,CAACvD,EAAK0C,MAAO3C,IAG9B,MAAMS,EAAa,CACjBC,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO0C,OAEND,EACH,IAAIpD,EAAMU,MAAM,aAAc,KAAM,IAGtCV,EAAMoB,OAAOmC,OAAOtD,EAAM,EAAG,KAAMM,GAE/BR,EAAKyD,UACPxD,EAAMoB,OAAOmC,OAAOtD,EAAK,EAAGO,OAAOC,OAAO,IAAIT,EAAMU,MAAM,aAAc,GAAI,GAAI,CAC9EM,QAASjB,EAAKyD,QAAQ,GAAK,QAG7BxD,EAAMoB,OAAOmC,OAAOtD,EAAM,EAAIM,EAAWkD,OAAS,EAAG,EAAGjD,OAAOC,OAAO,IAAIT,EAAMU,MAAM,aAAc,GAAI,GAAI,CAC1GM,QAASjB,EAAKyD,QAAQ,GAAK,WCjNjC,SAASE,EAAY5D,EAAM6D,EAAOC,EAAiBC,GACjD,IAAIC,EAAOhE,EACPiE,EAAIF,EAER,GAAID,GAAmBpD,OAAOwD,UAAUC,eAAeC,KAAKP,EAAOG,GACjE,UAAUpB,MAAO,mCAAkC5C,kEAEnD,KAAOU,OAAOwD,UAAUC,eAAeC,KAAKP,EAAOG,IACjDA,EAAQ,GAAEhE,KAAQiE,IAClBA,GAAK,EAMT,OAFAJ,EAAMG,IAAQ,EAEPA,EAMT,SAASK,EAAQC,EAAIrE,GACnBA,EAAOS,OAAOC,OAAO,GAAI0D,EAAOpC,SAAUhC,GAE1CqE,EAAGC,KAAKC,MAAMhB,KAAK,SAAUtD,IAC3B,MAAM2D,EAAQ,GACRvC,EAASpB,EAAMoB,OAEfmD,EAAkBC,MAAMC,QAAQ1E,EAAK2E,QATlBC,EAUA5E,EAAK2E,MAVQA,GAASC,EAAUnC,SAASkC,IADxCC,CAAAA,GAAaD,GAASA,GAASC,EAYrDC,CAAsB7E,EAAK2E,OAXNC,IAAAA,EAazB,IAAK,IAAI1E,EAAM,EAAGA,EAAMmB,EAAOqC,OAAQxD,IAAO,CAC5C,MAAM8C,EAAQ3B,EAAOnB,GAErB,GAAmB,iBAAf8C,EAAMC,KACR,SAGF,IAAKuB,EAAgBM,OAAO9B,EAAM+B,IAAIC,OAAO,KAC3C,SAIF,MAAMlC,EAAQ9C,EAAKiF,cAAc5D,EAAOnB,EAAM,GAAGoB,UAEjD,IAAIvB,EAAOiD,EAAMkC,QAAQ,MAGvBnF,EADU,MAARA,EACK4D,EAAW3D,EAAKmF,QAAQrC,GAAQc,GAAO,EAAO5D,EAAKoF,sBAEnDzB,EAAW5D,EAAM6D,GAAO,EAAM5D,EAAKoF,sBAG5CpC,EAAMqC,QAAQ,KAAMtF,IAEE,IAAlBC,EAAKsF,UACPtC,EAAMqC,QAAQ,WAAa,GAAErF,EAAKsF,UAGN,mBAAnBtF,EAAKuF,UACdvF,EAAKuF,UAAUxF,EAAMC,EAAMC,EAAOC,IACzBF,EAAKuF,WAELvF,EAAK+B,iBAAmB/B,EAAK+B,kBAAoBwD,IAD1DvF,EAAK+B,gBAAgBhC,EAAMC,EAAMC,EAAOC,GAO1CA,EAAMmB,EAAOmE,QAAQxC,GAEjBhD,EAAKyF,UACPzF,EAAKyF,SAASzC,EAAO,CAAEjD,KAAAA,EAAM+C,MAAAA,OD6IrCrC,OAAOC,OAAO8B,EAAgBR,SAAU,CACtCU,MAAO,kBACPN,OAAO,EACPC,UAAW,QACXoB,QAAS,OC3IXW,EAAOmB,8IAEPnB,EAAOpC,SAAW,CAChB2C,MAAO,EACPQ,QA5FeO,GAAMC,mBAAmBC,OAAOF,GAAGG,OAAOC,cAAcC,QAAQ,OAAQ,MA6FvFX,qBAAsB,EACtBE,SAAU,KACVL,cA7FF,SAAwB5D,GACtB,OAAOA,EACJ0B,OAAOK,GAAK,CAAC,OAAQ,eAAeX,SAASW,EAAEH,OAC/C+C,IAAI5C,GAAKA,EAAEnC,SACXgF,KAAK,KA4FRV,WAAW,EACXxD,gBAAiBwD,EACjB1E,eAAgB0E,EAAqBvD,SAASL,MAC9CP,eAAgBmE,EAAqBvD,SAASI,MAC9ClB,gBAAiB,IACjBK,gBAA6D,WAA5CgE,EAAqBvD,SAASK,UAC/CvB,cAAeyE,EAAqBvD,SAASR,WAC7CR,eAAgBuE,EAAqBvD,SAASP,aAQhD2C,EAAO8B,QAAU9B"}
{"author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "mm<PERSON><PERSON>@snaplogic.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "name": "taffydb", "main": "./taffy", "description": "TaffyDB is an opensouce library that brings database features into your JavaScript applications.", "version": "2.6.2", "homepage": "http://taffydb.com/", "repository": {"type": "git", "url": "git://github.com/typicaljoe/taffydb.git"}, "dependencies": {}, "devDependencies": {}, "maintainers": [{"name": "chambery", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}
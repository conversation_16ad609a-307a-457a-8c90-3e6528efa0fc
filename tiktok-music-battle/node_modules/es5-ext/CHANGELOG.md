# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.10.64](https://github.com/medikoo/es5-ext/compare/v0.10.63...v0.10.64) (2024-02-27)

### Bug Fixes

- Revert update to postinstall script meant to fix Powershell issue, as it's a regression for some Linux terminals ([c2e2bb9](https://github.com/medikoo/es5-ext/commit/c2e2bb90c295c4c582445a6f03b2a3ad0b22550a))

### [0.10.63](https://github.com/medikoo/es5-ext/compare/v0.10.62...v0.10.63) (2024-02-23)

### Bug Fixes

- Do not rely on problematic regex ([3551cdd](https://github.com/medikoo/es5-ext/commit/3551cdd7b2db08b1632841f819d008757d28e8e2)), addresses [#201](https://github.com/medikoo/es5-ext/issues/201)
- Support ES2015+ function definitions in `function#toStringTokens()` ([a52e957](https://github.com/medikoo/es5-ext/commit/a52e95736690ad1d465ebcd9791d54570e294602)), addresses [#021](https://github.com/medikoo/es5-ext/issues/021)
- Ensure postinstall script does not crash on Windows, fixes [#181](https://github.com/medikoo/es5-ext/issues/181) ([bf8ed79](https://github.com/medikoo/es5-ext/commit/bf8ed799d57df53096da9d908ff577f305e1366f))

### Maintenance Improvements

- Simplify the manifest message ([7855319](https://github.com/medikoo/es5-ext/commit/7855319f41b9736639cf4555bd2c419f17addf55))

### [0.10.62](https://github.com/medikoo/es5-ext/compare/v0.10.61...v0.10.62) (2022-08-02)

### Maintenance Improvements

- **Manifest improvements:**
  - ([#190](https://github.com/medikoo/es5-ext/issues/190)) ([b8dc53f](https://github.com/medikoo/es5-ext/commit/b8dc53fa439b98541644c64c1275f25d9f2e2235))
  - ([c51d552](https://github.com/medikoo/es5-ext/commit/c51d552c03967858b8f14a4afa305338ba648cce))

### [0.10.61](https://github.com/medikoo/es5-ext/compare/v0.10.60...v0.10.61) (2022-04-20)

### Bug Fixes

- Ensure postinstall script does not error ([a0be4fd](https://github.com/medikoo/es5-ext/commit/a0be4fdacdbc3aefd6f2952b7b9215827d362bbb))

### Maintenance Improvements

- Bump dependencies ([d7e0a61](https://github.com/medikoo/es5-ext/commit/d7e0a612b7d895c1c7238c779feae1e39d4634c4))

### [0.10.60](https://github.com/medikoo/es5-ext/compare/v0.10.59...v0.10.60) (2022-04-07)

### Maintenance Improvements

- Improve `postinstall` script configuration ([ab6b121](https://github.com/medikoo/es5-ext/commit/ab6b121f0ca4f033bba9b6f400b24d07869bd716))

### [0.10.59](https://github.com/medikoo/es5-ext/compare/v0.10.58...v0.10.59) (2022-03-17)

### Maintenance Improvements

- Improve manifest wording ([#122](https://github.com/medikoo/es5-ext/issues/122)) ([eb7ae59](https://github.com/medikoo/es5-ext/commit/eb7ae59966774a8c26f1717415c627d90bb3d954))
- Update data in manifest ([3d2935a](https://github.com/medikoo/es5-ext/commit/3d2935ac6f1a0969c7569840d5b3bdeed6940e56))

### [0.10.58](https://github.com/medikoo/es5-ext/compare/v0.10.57...v0.10.58) (2022-03-11)

### Maintenance Improvements

- Improve "call for peace" manifest ([3beace4](https://github.com/medikoo/es5-ext/commit/3beace4b3d00f02da61b72dd328f90cf069d46de))

### [0.10.57](https://github.com/medikoo/es5-ext/compare/v0.10.56...v0.10.57) (2022-03-08)

### Bug Fixes

- Workaround `postinstall` script prevent npx error ([#110](https://github.com/medikoo/es5-ext/issues/110)) ([e212d5a](https://github.com/medikoo/es5-ext/commit/e212d5a0adf186f1fadf85fdc07aab25085ee097)) ([martindrq](https://github.com/martindrq))

### [0.10.56](https://github.com/medikoo/es5-ext/compare/v0.10.55...v0.10.56) (2022-03-07)

### Maintenance Improvements

- Add missing shebang to postinstall script ([e423fd7](https://github.com/medikoo/es5-ext/commit/e423fd7264c4f145921e461037d571b35b6a9833))

### [0.10.55](https://github.com/medikoo/es5-ext/compare/v0.10.54...v0.10.55) (2022-03-07)

### Maintenance Improvements

- Configure `.npmignore` file (exclude tests from publication) ([d3ed4b6](https://github.com/medikoo/es5-ext/commit/d3ed4b6a873900a2abf3957bbebdcf18c4e564e0))

### [0.10.54](https://github.com/medikoo/es5-ext/compare/v0.10.53...v0.10.54) (2022-03-07)

### Maintenance Improvements

- Convert dependency ranges ([765eb8e](https://github.com/medikoo/es5-ext/commit/765eb8e897cabc76f0351443d84b4843a1187b27))
- Give Peace a Chance ([28de285](https://github.com/medikoo/es5-ext/commit/28de285ed433b45113f01e4ce7c74e9a356b2af2))

### [0.10.53](https://github.com/medikoo/es5-ext/compare/v0.10.52...v0.10.53) (2019-11-21)

_Maintenance improvements_

### [0.10.52](https://github.com/medikoo/es5-ext/compare/v0.10.51...v0.10.52) (2019-10-29)

### Bug Fixes

- Fix global resolution for Safari ([00731d2](https://github.com/medikoo/es5-ext/commit/00731d2))
- Keep support for old Node.js versions ([2fa2a11](https://github.com/medikoo/es5-ext/commit/2fa2a11))

### [0.10.51](https://github.com/medikoo/es5-ext/compare/v0.10.50...v0.10.51) (2019-08-30)

### Bug Fixes

- Ensure Function.isFunction recognizes async functions ([6f06e66](https://github.com/medikoo/es5-ext/commit/6f06e66))

### Tests

- Fix after prettification changes ([dd6fc3f](https://github.com/medikoo/es5-ext/commit/dd6fc3f))

## [0.10.50](https://github.com/medikoo/es5-ext/compare/v0.10.49...v0.10.50) (2019-04-30)

### Bug Fixes

- maximum time value reference ([708202d](https://github.com/medikoo/es5-ext/commit/708202d))

### Features

- ensure global resolves in strict mode ([c6a19d7](https://github.com/medikoo/es5-ext/commit/c6a19d7)), closes [#86](https://github.com/medikoo/es5-ext/issues/86)

## [0.10.49](https://github.com/medikoo/es5-ext/compare/v0.10.48...v0.10.49) (2019-03-11)

### Features

- allow plain function usage of fn.compose ([2bafef7](https://github.com/medikoo/es5-ext/commit/2bafef7))

## [0.10.48](https://github.com/medikoo/es5-ext/compare/v0.10.47...v0.10.48) (2019-02-22)

### Features

- Object.ensurePlainObject util ([f48fbcf](https://github.com/medikoo/es5-ext/commit/f48fbcf))

<a name="0.10.47"></a>

## [0.10.47](https://github.com/medikoo/es5-ext/compare/v0.10.46...v0.10.47) (2019-01-16)

### Features

- Promise.prototype.finally shim ([4dadbc7](https://github.com/medikoo/es5-ext/commit/4dadbc7))

<a name="0.10.46"></a>

## [0.10.46](https://github.com/medikoo/es5-ext/compare/v0.10.45...v0.10.46) (2018-08-13)

### Bug Fixes

- assign-deep to not modify following arguments ([bf43d57](https://github.com/medikoo/es5-ext/commit/bf43d57))

<a name="0.10.45"></a>

## [0.10.45](https://github.com/medikoo/es5-ext/compare/v0.10.44...v0.10.45) (2018-06-01)

### Bug Fixes

- improve error message readbility ([adc91b9](https://github.com/medikoo/es5-ext/commit/adc91b9))

<a name="0.10.44"></a>

## [0.10.44](https://github.com/medikoo/es5-ext/compare/v0.10.43...v0.10.44) (2018-05-30)

### Features

- add Object.entries ([51d2f43](https://github.com/medikoo/es5-ext/commit/51d2f43))

<a name="0.10.43"></a>

## [0.10.43](https://github.com/medikoo/es5-ext/compare/v0.10.42...v0.10.43) (2018-05-28)

### Features

- improve patch string ([6a25b10](https://github.com/medikoo/es5-ext/commit/6a25b10))

<a name="0.10.42"></a>

## [0.10.42](https://github.com/medikoo/es5-ext/compare/v0.10.41...v0.10.42) (2018-03-28)

### Bug Fixes

- Date.isDate to exclude NaN dates ([3b61bc6](https://github.com/medikoo/es5-ext/commit/3b61bc6))

### Features

- improve non-coercible string representation ([20bfb78](https://github.com/medikoo/es5-ext/commit/20bfb78))
- improve non-stringifiable string representation ([2e4512d](https://github.com/medikoo/es5-ext/commit/2e4512d))

<a name="0.10.41"></a>

## [0.10.41](https://github.com/medikoo/es5-ext/compare/v0.10.40...v0.10.41) (2018-03-16)

### Features

- Add function.microtaskDelay method ([66481c0](https://github.com/medikoo/es5-ext/commit/66481c0))
- Add Object.isThenable ([8d5a45c](https://github.com/medikoo/es5-ext/commit/8d5a45c))
- Add promise.asCallback method ([dcc1451](https://github.com/medikoo/es5-ext/commit/dcc1451))
- Object.ensurePlainFunction ([2682be6](https://github.com/medikoo/es5-ext/commit/2682be6))

<a name="0.10.40"></a>

## [0.10.40](https://github.com/medikoo/es5-ext/compare/v0.10.39...v0.10.40) (2018-03-09)

### Features

- **math:** decimal round, floor and ceil ([39290c6](https://github.com/medikoo/es5-ext/commit/39290c6))
- **object:** isInteger and ensureInteger ([a5f7d04](https://github.com/medikoo/es5-ext/commit/a5f7d04))

<a name="0.10.39"></a>

## [0.10.39](https://github.com/medikoo/es5-ext/compare/v0.10.38...v0.10.39) (2018-02-16)

### Features

- Promise.lazy ([7a30a78](https://github.com/medikoo/es5-ext/commit/7a30a78))

<a name="0.10.38"></a>

## [0.10.38](https://github.com/medikoo/es5-ext/compare/v0.10.37...v0.10.38) (2018-01-16)

### Features

- Object.isNaturalNumber an Object.isNaturalNumberValue ([66a40af](https://github.com/medikoo/es5-ext/commit/66a40af))

<a name="0.10.37"></a>

## [0.10.37](https://github.com/medikoo/es5-ext/compare/v0.10.36...v0.10.37) (2017-11-23)

### Features

- String.random util ([7c28739](https://github.com/medikoo/es5-ext/commit/7c28739))

<a name="0.10.36"></a>

## [0.10.36](https://github.com/medikoo/es5-ext/compare/v0.10.35...v0.10.36) (2017-11-23)

### Features

- **date:** isTimeValue and ensureTimeValue utils ([7659dc5](https://github.com/medikoo/es5-ext/commit/7659dc5))

<a name="0.10.35"></a>

## [0.10.35](https://github.com/medikoo/es5-ext/compare/v0.10.34...v0.10.35) (2017-10-13)

### Bug Fixes

- **Object.copy:** do not upgrade primitives to objects ([dd4d88f](https://github.com/medikoo/es5-ext/commit/dd4d88f))

<a name="0.10.34"></a>

## [0.10.34](https://github.com/medikoo/es5-ext/compare/v0.10.33...v0.10.34) (2017-10-13)

### Features

- **copyDeep:** duplicate only recursive instances ([bba529a](https://github.com/medikoo/es5-ext/commit/bba529a))

<a name="0.10.33"></a>

## [0.10.33](https://github.com/medikoo/es5-ext/compare/v0.10.32...v0.10.33) (2017-10-13)

### Bug Fixes

- **Object.assignDeep:** relax input validation ([1baf57d](https://github.com/medikoo/es5-ext/commit/1baf57d))

<a name="0.10.32"></a>

## [0.10.32](https://github.com/medikoo/es5-ext/compare/v0.10.31...v0.10.32) (2017-10-13)

### Features

- Object.assignDeep ([2345e0b](https://github.com/medikoo/es5-ext/commit/2345e0b))

<a name="0.10.31"></a>

## [0.10.31](https://github.com/medikoo/es5-ext/compare/v0.10.30...v0.10.31) (2017-10-09)

### Features

- Object.isPlainFunction utility ([031be0a](https://github.com/medikoo/es5-ext/commit/031be0a))

<a name="0.10.30"></a>

## [0.10.30](https://github.com/medikoo/es5-ext/compare/v0.10.29...v0.10.30) (2017-08-25)

### Bug Fixes

- value stringification for error message ([37bb96b](https://github.com/medikoo/es5-ext/commit/37bb96b))

<a name="0.10.29"></a>

## [0.10.29](https://github.com/medikoo/es5-ext/compare/v0.10.28...v0.10.29) (2017-08-18)

### Bug Fixes

- string.repeat after recent regression ([b02fab4](https://github.com/medikoo/es5-ext/commit/b02fab4))

<a name="0.10.28"></a>

## [0.10.28](https://github.com/medikoo/es5-ext/compare/v0.10.27...v0.10.28) (2017-08-18)

### Features

- array.isEmpty method ([b0cfbdd](https://github.com/medikoo/es5-ext/commit/b0cfbdd))
- improve new lines representation ([860fe8b](https://github.com/medikoo/es5-ext/commit/860fe8b))
- Object.ensureArray util ([595c341](https://github.com/medikoo/es5-ext/commit/595c341))
- toShortStringRepresentation util ([6842d06](https://github.com/medikoo/es5-ext/commit/6842d06))

<a name="0.10.27"></a>

## [0.10.27](https://github.com/medikoo/es5-ext/compare/v0.10.26...v0.10.27) (2017-08-11)

### Bug Fixes

- isNumberValue should not crash on non-coercible values ([0db765e](https://github.com/medikoo/es5-ext/commit/0db765e))

### Features

- add Object.ensureFiniteNumber util ([11c67f5](https://github.com/medikoo/es5-ext/commit/11c67f5))
- add Object.isFiniteNumber util ([fe5b55a](https://github.com/medikoo/es5-ext/commit/fe5b55a))

<a name="0.10.26"></a>

## [0.10.26](https://github.com/medikoo/es5-ext/compare/v0.10.25...v0.10.26) (2017-08-02)

### Bug Fixes

- **general:** ensure optionalChaining in index ([3df879a](https://github.com/medikoo/es5-ext/commit/3df879a))

<a name="0.10.25"></a>

## [0.10.25](https://github.com/medikoo/es5-ext/compare/v0.10.24...v0.10.25) (2017-08-02)

### Features

- **general:** optionalChaining utility ([26332b5](https://github.com/medikoo/es5-ext/commit/26332b5))

<a name="0.10.24"></a>

## [0.10.24](https://github.com/medikoo/es5-ext/compare/v0.10.23...v0.10.24) (2017-07-10)

### Features

- resolve global with CSP safe method ([d386449](https://github.com/medikoo/es5-ext/commit/d386449))

<a name="0.10.23"></a>

## [0.10.23](https://github.com/medikoo/es5-ext/compare/v0.10.22...v0.10.23) (2017-06-05)

### Bug Fixes

- **Error.custom:** allow non-string code ([e8db3a0](https://github.com/medikoo/es5-ext/commit/e8db3a0))
- **Error.custom:** improve `ext` argument detection ([0edbfbc](https://github.com/medikoo/es5-ext/commit/0edbfbc))

<a name="0.10.22"></a>

## [0.10.22](https://github.com/medikoo/es5-ext/compare/v0.10.21...v0.10.22) (2017-05-31)

### Bug Fixes

- ensure proper symbols stringification in early implementations ([ce51900](https://github.com/medikoo/es5-ext/commit/ce51900))

<a name="0.10.21"></a>

## [0.10.21](https://github.com/medikoo/es5-ext/compare/v0.10.20...v0.10.21) (2017-05-22)

### Features

- support arrow functions in Function/#/to-tring-tokens.js ([ad3de1e](https://github.com/medikoo/es5-ext/commit/ad3de1e))

<a name="0.10.20"></a>

## [0.10.20](https://github.com/medikoo/es5-ext/compare/v0.10.19...v0.10.20) (2017-05-17)

### Features

- if listed copy not only if own property ([d7e7cef](https://github.com/medikoo/es5-ext/commit/d7e7cef))
- support `ensure` option in Object.copy ([295326f](https://github.com/medikoo/es5-ext/commit/295326f))

<a name="0.10.19"></a>

## [0.10.19](https://github.com/medikoo/es5-ext/compare/v0.10.18...v0.10.19) (2017-05-17)

### Features

- support propertyNames option in Object.copy ([5442279](https://github.com/medikoo/es5-ext/commit/5442279))

<a name="0.10.18"></a>

## [0.10.18](https://github.com/medikoo/es5-ext/compare/v0.10.17...v0.10.18) (2017-05-15)

### Bug Fixes

- take all changes in safeToString ([3c5cd12](https://github.com/medikoo/es5-ext/commit/3c5cd12))

<a name="0.10.17"></a>

## [0.10.17](https://github.com/medikoo/es5-ext/compare/v0.10.16...v0.10.17) (2017-05-15)

### Features

- introduce Object.ensurePromise ([46a2f45](https://github.com/medikoo/es5-ext/commit/46a2f45))
- introduce Object.isPromise ([27aecc8](https://github.com/medikoo/es5-ext/commit/27aecc8))
- introduce safeToString ([0cc6a7b](https://github.com/medikoo/es5-ext/commit/0cc6a7b))

<a name="0.10.16"></a>

## [0.10.16](https://github.com/medikoo/es5-ext/compare/v0.10.15...v0.10.16) (2017-05-09)

### Features

- add String.prototype.count ([2e53241](https://github.com/medikoo/es5-ext/commit/2e53241))

## Changelog for previous versions

See `CHANGES` file

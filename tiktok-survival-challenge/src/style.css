/* TikTok Live Survival Challenge Styles */
:root {
  --survival-green: #4caf50;
  --survival-orange: #ff9800;
  --survival-red: #f44336;
  --survival-blue: #2196f3;
  --survival-brown: #8d6e63;
  --background-dark: #0a0a0a;
  --background-survival: #1a1a0a;
  --background-card: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #333333;

  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: radial-gradient(circle at center, var(--background-survival) 0%, var(--background-dark) 100%);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

#survival-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: var(--background-survival);
  border: 2px solid var(--survival-green);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 20px var(--survival-green);
}

.survival-header {
  background: linear-gradient(90deg, var(--survival-green), var(--survival-brown), var(--survival-orange));
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.5);
}

.survival-header h1 {
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: survivalGlow 2s infinite alternate;
}

@keyframes survivalGlow {
  0% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px #4caf50; }
  100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 20px #4caf50, 0 0 30px #4caf50; }
}

.survival-status {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.survival-status span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--survival-orange);
}

.autopilot-toggle {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--survival-green), var(--survival-orange));
  border: 2px solid var(--survival-brown);
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1500;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px var(--survival-green);
  animation: pulse 2s infinite;
}

.survival-main {
  flex: 1;
  padding: 2rem;
  position: relative;
  overflow-y: auto;
  background: radial-gradient(ellipse at center, rgba(76, 175, 80, 0.1) 0%, transparent 70%);
}

.survival-section {
  display: none;
  width: 100%;
  height: 100%;
  animation: fadeIn 0.5s ease-in-out;
}

.survival-section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scenario Section */
.scenario-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: 100%;
}

.scenario-display {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--survival-green);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.scenario-image {
  font-size: 8rem;
  margin-bottom: 1rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

#scenario-title {
  font-size: 2rem;
  color: var(--survival-orange);
  margin-bottom: 1rem;
}

#scenario-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.choice-panel {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.choice-panel h3 {
  color: var(--survival-orange);
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.choices-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.choice-btn {
  background: var(--background-dark);
  border: 2px solid var(--survival-green);
  color: var(--text-primary);
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  text-align: left;
}

.choice-btn:hover {
  background: var(--survival-green);
  color: white;
  transform: translateY(-2px);
}

.choice-btn.selected {
  background: var(--survival-orange);
  border-color: var(--survival-orange);
  color: white;
}

.voting-timer {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--survival-orange);
}

#vote-time {
  font-size: 2rem;
  color: var(--survival-red);
}

/* Resources Section */
.resources-container {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.resources-container h2 {
  color: var(--survival-orange);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.resource-card {
  background: var(--background-dark);
  border: 2px solid var(--survival-green);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.resource-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.resource-icon {
  font-size: 3rem;
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.resource-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--survival-orange);
  margin-bottom: 0.5rem;
}

.resource-bar {
  width: 100%;
  height: 8px;
  background: var(--background-survival);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.resource-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--survival-green), var(--survival-orange));
  transition: width 0.5s ease;
}

.resource-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.resource-btn {
  background: linear-gradient(45deg, var(--survival-green), var(--survival-orange));
  border: 2px solid var(--survival-brown);
  color: white;
  font-size: 1rem;
  font-weight: 700;
  padding: 1rem 2rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

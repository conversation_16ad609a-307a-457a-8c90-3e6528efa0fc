# 🎮 TikTok Live Adventure Game

Un joc interactiv profesional pentru streamerii TikTok Live, cu **automatizare completă** a interacțiunii cu publicul!

## ✨ Caracteristici Principale

### 🤖 **Auto-Pilot System** - COMPLET AUTOMATIZAT!
- **<PERSON>rne<PERSON> live-ul, pui username-ul și totul merge automat!**
- Răspunde automat la chat-ul publicului
- Învârte roata aventurii la intervale regulate
- Creează voturi interactive automat
- Dezvoltă povești captivante în timp real
- Reacționează la cadouri și interacțiuni

### 🎡 **Adventure Wheel**
- Roată interactivă cu opțiuni personalizabile
- Animații fluide optimizate pentru streaming
- Comenzi chat automate (`!spin`)

### 🗳️ **Community Voting**
- Voturi în timp real pentru publicul tău
- Progres vizual cu bare animate
- Generare automată de întrebări și opțiuni

### 💬 **Chat Integration**
- Monitorizare automată a chat-ului TikTok Live
- Răspunsuri inteligente și contextuale
- Comenzi interactive pentru public

### 📖 **Story Mode**
- Povești de aventură generate automat
- Progresie bazată pe voturile publicului
- Teme multiple: Fantasy, Sci-Fi, Adventure

### 🎥 **Streaming Optimization**
- Integrare perfectă cu OBS Studio
- Overlay-uri personalizabile
- Mod Browser Source pentru streaming
- Statistici live și metrici

## 🚀 Instalare Rapidă

```bash
# Clonează proiectul
git clone [repository-url]
cd tiktok-live-game

# Instalează dependențele
npm install

# Pornește aplicația
npm run dev
```

Aplicația va fi disponibilă la: `http://localhost:5173/`

## 🎯 Cum să Folosești Auto-Pilot

### 1. **Pornește Aplicația**
```bash
npm run dev
```

### 2. **Configurează Auto-Pilot**
- Apasă butonul **🤖** din centrul ecranului
- Introdu **username-ul tău TikTok**
- Alege **tema aventurii** (Fantasy/Sci-Fi/Adventure)
- Setează **nivelul de interacțiune** (Scăzut/Mediu/Înalt)

### 3. **Pornește Automatizarea**
- Apasă **"🚀 Pornește Auto-Pilot"**
- **GATA! Totul merge automat!**

### 4. **Pentru OBS/TikTok Live Studio**
- Adaugă **Browser Source** cu URL: `http://localhost:5173/?mode=browser-source`
- Dimensiuni: 1920x1080
- **Jocul va interacționa automat cu publicul!**

## 🎮 Ce Face Auto-Pilot Automat

### 💬 **Răspunsuri Inteligente la Chat**
- Detectează mesajele publicului
- Generează răspunsuri contextuale și personalizate
- Reacționează la întrebări, complimente și comenzi
- Menține conversația vie și angajantă

### 🎡 **Managementul Roții**
- Învârte roata la intervale regulate (configurabil)
- Anunță spin-urile cu mesaje variate
- Reacționează la rezultate cu comentarii

### 🗳️ **Voturi Automate**
- Creează voturi cu opțiuni de aventură
- Simulează participarea publicului
- Anunță rezultatele și le integrează în poveste

### 📖 **Dezvoltarea Poveștii**
- Generează capitole noi automat
- Adaptează povestea la voturile publicului
- Menține continuitatea narativă

### 🎁 **Reacții la Cadouri**
- Detectează cadourile TikTok
- Răspunde cu mesaje personalizate
- Creează efecte vizuale speciale

## ⚙️ Setări Auto-Pilot

### **Intervale Configurabile:**
- **Roată**: 15-120 secunde
- **Voturi**: 30-300 secunde
- **Actualizări poveste**: 2-10 minute

### **Rata de Răspuns:**
- **Chat**: 0-100% din mesaje
- **Cadouri**: 100% (recomandat)

### **Nivele de Engagement:**
- **Scăzut**: Relaxat, mai puține interacțiuni
- **Mediu**: Echilibrat, interacțiuni regulate
- **Înalt**: Foarte activ, interacțiuni constante

## 🎨 Teme de Aventură

### 🏰 **Fantasy**
- Peșteri misterioase cu cristale magice
- Păduri fermecate cu creaturi mitice
- Castele bântuite și comori pierdute
- Temple antice și vrăjitori

### 🚀 **Sci-Fi**
- Stații spațiale abandonate
- Planete necunoscute
- Călătorii în timp
- Civilizații extraterestre

### 🗺️ **Adventure**
- Jungle periculoase
- Comori în deșert
- Escaladări extreme
- Ruine antice

## 📊 Statistici Live

Auto-Pilot urmărește automat:
- **Mesaje procesate**
- **Răspunsuri generate**
- **Roți învârtite**
- **Voturi create**
- **Numărul de spectatori**
- **Rata de engagement**

## 🎥 Integrare OBS Studio

### **Browser Source Setup:**
1. Adaugă **Browser Source** în OBS
2. URL: `http://localhost:5173/?mode=browser-source`
3. Dimensiuni: 1920x1080 (sau 1280x720)
4. FPS: 30

### **Setări Recomandate:**
- **Rezoluție**: 1920x1080
- **Bitrate**: 2500-6000 kbps
- **Encoder**: x264 sau NVENC

## 🎯 Comenzi Chat pentru Public

Publicul poate folosi aceste comenzi:
- `!spin` - Învârte roata aventurii
- `!vote` - Participă la votare
- `!story` - Vezi povestea
- `!help` - Arată toate comenzile

## 🔧 Troubleshooting

### **Aplicația nu se încarcă:**
- Verifică că Node.js este instalat
- Rulează `npm install` din nou
- Verifică că portul 5173 este liber

### **Auto-Pilot nu răspunde:**
- Verifică că username-ul este introdus corect
- Asigură-te că Auto-Pilot este pornit (indicator verde)
- Verifică consola browser-ului pentru erori

### **OBS Browser Source negru:**
- Restartează OBS
- Verifică URL-ul Browser Source
- Asigură-te că aplicația rulează

## 🎊 Sfaturi pentru Streameri

### **Înainte de Live:**
1. Testează toate funcțiile
2. Configurează Auto-Pilot cu username-ul corect
3. Verifică integrarea OBS
4. Pregătește tema aventurii

### **În timpul Live-ului:**
1. Lasă Auto-Pilot să gestioneze interacțiunile
2. Reacționează la comentariile generate automat
3. Adaugă propriile comentarii când vrei
4. Monitorizează statisticile live

### **Pentru Engagement Maxim:**
- Explică publicului comenzile disponibile
- Reacționează entuziast la rezultatele roții
- Încurajează participarea la voturi
- Construiește pe povestea generată automat

## 📈 Caracteristici Avansate

### **API Integration:**
```javascript
// Controlează jocul extern
window.TikTokLiveGame.spinWheel();
window.TikTokLiveGame.addChatMessage('User', 'Message');
```

### **Customizare Teme:**
- Editează `src/auto-pilot.js` pentru scenarii noi
- Modifică `src/style.css` pentru stiluri personalizate
- Adaugă efecte noi în `src/streaming-utils.js`

## 🎮 Demo Live

Pentru a vedea jocul în acțiune:
1. Pornește aplicația: `npm run dev`
2. Deschide: `http://localhost:5173/`
3. Apasă butonul **🤖** și configurează Auto-Pilot
4. Urmărește cum interacționează automat!

## 🤝 Suport

Pentru întrebări sau probleme:
- Verifică secțiunea Troubleshooting
- Consultă documentația completă în `STREAMING_GUIDE.md`
- Verifică consola browser-ului pentru erori

---

**🎮 Fă-ți live-urile TikTok de neuitat cu aventuri interactive complet automatizate! ✨**

*Doar pornești, pui username-ul și totul merge automat! 🚀*

/* TikTok Live Quiz Master Styles */
:root {
  /* Color Palette */
  --primary-color: #ff6b35;
  --secondary-color: #4ecdc4;
  --accent-color: #ffd700;
  --success-color: #00ff88;
  --error-color: #ff4757;
  --warning-color: #ffa502;
  --background-dark: #0a0a0a;
  --background-light: #1a1a1a;
  --background-card: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #888888;
  --border-color: #333333;

  /* Quiz Specific Colors */
  --answer-a: #ff6b35;
  --answer-b: #4ecdc4;
  --answer-c: #ffd700;
  --answer-d: #9b59b6;

  /* Fonts */
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-light) 100%);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Quiz Container */
#quiz-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

/* Header */
.quiz-header {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
}

.quiz-header h1 {
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.quiz-status {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.quiz-status span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Auto-Pilot Toggle */
.autopilot-toggle {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.autopilot-toggle:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
}

/* Main Quiz Area */
.quiz-main {
  flex: 1;
  padding: 2rem;
  position: relative;
  overflow-y: auto;
}

/* Quiz Sections */
.quiz-section {
  display: none;
  width: 100%;
  height: 100%;
  animation: fadeIn 0.3s ease-in-out;
}

.quiz-section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Question Section */
.question-container {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 2px solid var(--border-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.category-badge {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timer {
  background: var(--warning-color);
  color: var(--background-dark);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 700;
  min-width: 60px;
  text-align: center;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.question-text {
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 1rem;
}

.question-image {
  text-align: center;
  margin: 1rem 0;
}

.question-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Answers Grid */
.answers-container {
  margin-bottom: 2rem;
}

.answers-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.answer-btn {
  position: relative;
  background: var(--background-card);
  border: 3px solid var(--border-color);
  border-radius: 15px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-height: 80px;
  overflow: hidden;
}

.answer-btn:nth-child(1) { border-color: var(--answer-a); }
.answer-btn:nth-child(2) { border-color: var(--answer-b); }
.answer-btn:nth-child(3) { border-color: var(--answer-c); }
.answer-btn:nth-child(4) { border-color: var(--answer-d); }

.answer-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.answer-btn:nth-child(1):hover { box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4); }
.answer-btn:nth-child(2):hover { box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4); }
.answer-btn:nth-child(3):hover { box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4); }
.answer-btn:nth-child(4):hover { box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4); }

.answer-letter {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.answer-btn:nth-child(1) .answer-letter { background: var(--answer-a); }
.answer-btn:nth-child(2) .answer-letter { background: var(--answer-b); }
.answer-btn:nth-child(3) .answer-letter { background: var(--answer-c); }
.answer-btn:nth-child(4) .answer-letter { background: var(--answer-d); }

.answer-text {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: left;
}

.answer-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background: currentColor;
  width: 0%;
  transition: width 0.5s ease;
  opacity: 0.7;
}

.answer-btn.correct {
  background: rgba(0, 255, 136, 0.2);
  border-color: var(--success-color);
  animation: correctAnswer 1s ease-in-out;
}

.answer-btn.incorrect {
  background: rgba(255, 71, 87, 0.2);
  border-color: var(--error-color);
  animation: incorrectAnswer 0.5s ease-in-out;
}

@keyframes correctAnswer {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes incorrectAnswer {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Controls */
.question-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.control-btn {
  background: var(--background-light);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1rem;
}

.control-btn:hover {
  border-color: var(--secondary-color);
  background: var(--secondary-color);
  color: var(--background-dark);
  transform: translateY(-2px);
}

/* Leaderboard */
.leaderboard-container {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.leaderboard-container h2 {
  color: var(--accent-color);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  max-height: 400px;
  overflow-y: auto;
}

.leaderboard-entry {
  display: flex;
  align-items: center;
  background: var(--background-light);
  padding: 1rem;
  border-radius: 10px;
  border-left: 4px solid var(--accent-color);
  transition: all 0.3s ease;
}

.leaderboard-entry:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.leaderboard-rank {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  min-width: 50px;
}

.leaderboard-name {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-left: 1rem;
}

.leaderboard-score {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--success-color);
}

.leaderboard-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Statistics */
.stats-container {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.stats-container h2 {
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: var(--background-light);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: var(--secondary-color);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Chat Section */
.chat-container {
  background: var(--background-card);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid var(--border-color);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-container h2 {
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
}

.chat-display {
  flex: 1;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 10px;
  padding: 1rem;
  overflow-y: auto;
  margin-bottom: 1rem;
  max-height: 400px;
}

.chat-message {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 3px solid var(--primary-color);
}

.chat-username {
  font-weight: 700;
  color: var(--accent-color);
  margin-right: 0.5rem;
}

.chat-text {
  color: var(--text-primary);
}

.chat-timestamp {
  font-size: 0.8rem;
  color: var(--text-muted);
  float: right;
}

.chat-controls {
  display: flex;
  gap: 1rem;
}

#chat-input {
  flex: 1;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

#chat-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

/* Navigation */
.quiz-nav {
  display: flex;
  background: var(--background-light);
  border-top: 2px solid var(--border-color);
}

.nav-btn {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  border-right: 1px solid var(--border-color);
}

.nav-btn:last-child {
  border-right: none;
}

.nav-btn:hover {
  background: var(--background-dark);
  color: var(--text-primary);
}

.nav-btn.active {
  background: var(--primary-color);
  color: white;
  font-weight: 700;
}

/* Live Indicator */
.live-indicator {
  position: fixed;
  top: 20px;
  left: 20px;
  background: linear-gradient(45deg, var(--error-color), #ff6b35);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
  z-index: 1000;
  display: none;
  animation: pulse 2s infinite;
}

.live-indicator.active {
  display: block;
}

.live-indicator::before {
  content: '🔴 ';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .quiz-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .quiz-header h1 {
    font-size: 1.4rem;
  }

  .quiz-main {
    padding: 1rem;
  }

  .answers-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .answer-btn {
    padding: 1rem;
    min-height: 60px;
  }

  .question-text {
    font-size: 1.2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .quiz-nav {
    flex-wrap: wrap;
  }

  .nav-btn {
    font-size: 0.9rem;
    padding: 0.75rem 0.5rem;
  }

  .question-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .chat-controls {
    flex-direction: column;
  }
}

/* Browser Source Mode */
body.browser-source {
  background: transparent !important;
}

body.browser-source #quiz-container {
  background: transparent !important;
  border: none !important;
}

body.browser-source .quiz-header {
  background: rgba(255, 107, 53, 0.9) !important;
  backdrop-filter: blur(10px);
}

body.browser-source .autopilot-toggle {
  display: none !important;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.slide-in {
  animation: slideInUp 0.5s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

/* Auto-Pilot Panel Styles */
.autopilot-panel .autopilot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--primary-color);
}

.autopilot-panel h3 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.close-btn {
  background: var(--error-color);
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.autopilot-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setup-section, .settings-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.setup-section h4, .settings-section h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.input-group {
  margin-bottom: 1rem;
}

.input-group label {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.input-group input, .input-group select {
  width: 100%;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem;
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input-group input:focus, .input-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  gap: 1rem;
}

.setting-item label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  min-width: 150px;
}

.setting-item input[type="range"] {
  flex: 1;
  background: var(--background-light);
  border-radius: 5px;
  height: 6px;
  outline: none;
  -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
}

.setting-item input[type="checkbox"] {
  transform: scale(1.2);
  margin-right: 0.5rem;
}

.setting-item span {
  color: var(--accent-color);
  font-weight: 600;
  min-width: 50px;
  text-align: right;
}

.control-section {
  display: flex;
  gap: 1rem;
}

.start-btn, .stop-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.start-btn {
  background: linear-gradient(45deg, var(--success-color), #00cc66);
  color: white;
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
}

.stop-btn {
  background: linear-gradient(45deg, var(--error-color), #cc0000);
  color: white;
}

.stop-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
}

.start-btn:disabled, .stop-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.status-section {
  background: rgba(255, 107, 53, 0.1);
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid var(--primary-color);
  text-align: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
}

.status-dot.online {
  background: var(--success-color);
  box-shadow: 0 0 10px var(--success-color);
  animation: pulse 2s infinite;
}

.status-dot.offline {
  background: var(--error-color);
}

.stats-mini {
  display: flex;
  justify-content: space-around;
  font-size: 0.9rem;
}

.stats-mini span {
  color: var(--text-secondary);
}

.stats-mini strong {
  color: var(--accent-color);
}

/* Responsive Auto-Pilot Panel */
@media (max-width: 768px) {
  .autopilot-panel {
    width: 90%;
    max-width: 400px;
    padding: 1.5rem;
  }

  .control-section {
    flex-direction: column;
  }

  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .setting-item label {
    min-width: auto;
  }

  .stats-mini {
    flex-direction: column;
    gap: 0.5rem;
  }
}

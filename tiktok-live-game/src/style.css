/* TikTok Live Adventure Game Styles */
:root {
  /* Color Palette */
  --primary-color: #ff0050;
  --secondary-color: #00f2ea;
  --accent-color: #ffd700;
  --background-dark: #0a0a0a;
  --background-light: #1a1a1a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #333333;
  --success-color: #00ff88;
  --warning-color: #ff8800;

  /* Fonts */
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-light) 100%);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Game Container */
#game-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

/* Header */
.game-header {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(255, 0, 80, 0.3);
}

.game-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-status {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.game-status span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Main Game Area */
.game-main {
  flex: 1;
  padding: 2rem;
  position: relative;
  overflow-y: auto;
}

/* Game Sections */
.game-section {
  display: none;
  width: 100%;
  height: 100%;
  animation: fadeIn 0.3s ease-in-out;
}

.game-section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Wheel Section */
.wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

#adventure-wheel {
  border: 4px solid var(--accent-color);
  border-radius: 50%;
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
  background: var(--background-light);
}

.spin-button {
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border: none;
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1rem 2rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(255, 0, 80, 0.4);
}

.spin-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 0, 80, 0.6);
}

.spin-button:active {
  transform: translateY(0);
}

.spin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Controls */
.wheel-controls, .voting-controls, .chat-controls, .story-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.control-btn {
  background: var(--background-light);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.control-btn:hover {
  border-color: var(--secondary-color);
  background: var(--secondary-color);
  color: var(--background-dark);
  transform: translateY(-1px);
}

#new-option, #chat-input {
  background: var(--background-light);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 250px;
  transition: border-color 0.3s ease;
}

#new-option:focus, #chat-input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 10px rgba(0, 242, 234, 0.3);
}

/* Voting Section */
.voting-container {
  text-align: center;
}

#voting-question {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.voting-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.vote-option {
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vote-option:hover {
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.vote-option.selected {
  border-color: var(--success-color);
  background: rgba(0, 255, 136, 0.1);
}

.vote-text {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.vote-count {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.vote-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

#vote-timer {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--warning-color);
  margin-left: 1rem;
}

/* Chat Section */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-display {
  flex: 1;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  overflow-y: auto;
  margin-bottom: 1rem;
  max-height: 400px;
}

.chat-message {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 3px solid var(--secondary-color);
}

.chat-username {
  font-weight: 600;
  color: var(--accent-color);
  margin-right: 0.5rem;
}

.chat-text {
  color: var(--text-primary);
}

.chat-timestamp {
  font-size: 0.8rem;
  color: var(--text-secondary);
  float: right;
}

/* Story Section */
.story-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.story-display {
  flex: 1;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1rem;
}

.story-display h3 {
  color: var(--accent-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

#story-content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-primary);
  min-height: 200px;
}

/* Navigation */
.game-nav {
  display: flex;
  background: var(--background-light);
  border-top: 2px solid var(--border-color);
}

.nav-btn {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  border-right: 1px solid var(--border-color);
}

.nav-btn:last-child {
  border-right: none;
}

.nav-btn:hover {
  background: var(--background-dark);
  color: var(--text-primary);
}

.nav-btn.active {
  background: var(--secondary-color);
  color: var(--background-dark);
  font-weight: 700;
}

/* Settings Panel */
.settings-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  z-index: 1000;
  display: none;
  min-width: 300px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.settings-panel.active {
  display: block;
}

.settings-panel h3 {
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

.setting-group {
  margin-bottom: 1rem;
}

.setting-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.setting-group select,
.setting-group input[type="range"] {
  width: 100%;
  padding: 0.5rem;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
}

.setting-group input[type="checkbox"] {
  transform: scale(1.2);
  margin-left: 0.5rem;
}

.settings-toggle {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  z-index: 100;
}

.settings-toggle:hover {
  background: var(--secondary-color);
  color: var(--background-dark);
  transform: rotate(90deg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .game-header h1 {
    font-size: 1.4rem;
  }

  .game-main {
    padding: 1rem;
  }

  #adventure-wheel {
    width: 300px;
    height: 300px;
  }

  .wheel-controls, .voting-controls, .chat-controls, .story-controls {
    flex-direction: column;
    align-items: stretch;
  }

  #new-option, #chat-input {
    min-width: auto;
    width: 100%;
  }

  .voting-options {
    grid-template-columns: 1fr;
  }

  .game-nav {
    flex-wrap: wrap;
  }

  .nav-btn {
    font-size: 0.9rem;
    padding: 0.75rem 0.5rem;
  }
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
}

.spinning {
  animation: spin 3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.pulsing {
  animation: pulse 2s infinite;
}

.glowing {
  animation: glow 2s infinite;
}

/* TikTok Live Integration Styles */
.tiktok-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 350px;
  background: var(--background-dark);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  z-index: 500;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  max-height: 80vh;
  overflow-y: auto;
}

.connection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.connection-header h3 {
  color: var(--accent-color);
  margin: 0;
  font-size: 1.2rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #666;
}

.connection-status.disconnected .status-indicator {
  background: #ff4444;
}

.connection-status.connecting .status-indicator {
  background: #ffaa00;
  animation: pulse 1s infinite;
}

.connection-status.connected .status-indicator {
  background: var(--success-color);
}

.connection-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.stream-info {
  margin-bottom: 1.5rem;
}

.info-item {
  margin-bottom: 1rem;
}

.info-item label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.info-item input {
  width: 100%;
  background: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.info-item span {
  color: var(--accent-color);
  font-weight: 600;
}

.chat-commands {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.chat-commands h4 {
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.command-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.command {
  background: var(--background-light);
  padding: 0.5rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: var(--secondary-color);
  border-left: 3px solid var(--secondary-color);
}

/* Gift Effects */
@keyframes giftEffect {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(-20px) scale(1.2);
  }
  80% {
    opacity: 1;
    transform: translateX(-50%) translateY(-40px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-60px) scale(0.8);
  }
}

.gift-effect {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* TikTok Live Indicator */
.live-indicator {
  position: fixed;
  top: 20px;
  left: 20px;
  background: linear-gradient(45deg, #ff0050, #ff4081);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
  z-index: 1000;
  display: none;
  animation: pulse 2s infinite;
}

.live-indicator.active {
  display: block;
}

.live-indicator::before {
  content: '🔴 ';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Mobile responsiveness for TikTok panel */
@media (max-width: 768px) {
  .tiktok-panel {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    width: auto;
    height: 100vh;
    border-radius: 0;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .tiktok-panel.active {
    transform: translateX(0);
  }

  .connection-controls {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .control-btn {
    flex: 1;
    min-width: 120px;
  }
}

/* Stream overlay styles for OBS integration */
.stream-overlay {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--accent-color);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-family: 'Inter', sans-serif;
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.overlay-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--accent-color);
}

.overlay-content {
  font-size: 1rem;
  line-height: 1.4;
}

/* Browser source optimization */
body.browser-source {
  background: transparent !important;
  margin: 0;
  padding: 0;
}

body.browser-source #game-container {
  background: transparent !important;
  border: none !important;
}

body.browser-source .game-header {
  background: rgba(255, 0, 80, 0.9) !important;
  backdrop-filter: blur(10px);
}

/* Hide certain elements in browser source mode */
body.browser-source .tiktok-panel,
body.browser-source .settings-toggle {
  display: none !important;
}

/* Streaming Utilities Styles */
.stream-overlay {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--accent-color);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-family: 'Inter', sans-serif;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.overlay-viewer-count {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 120px;
}

.overlay-recent-activity {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 300px;
  max-height: 200px;
  overflow-y: auto;
}

.overlay-wheel-result {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  min-width: 300px;
  text-align: center;
}

.overlay-vote-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
}

.overlay-stream-stats {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 250px;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.overlay-icon {
  font-size: 1.5rem;
  text-align: center;
}

.overlay-text {
  text-align: center;
}

.overlay-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-color);
}

.overlay-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.overlay-header {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-color);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  padding: 0.25rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-time {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.activity-text {
  color: var(--text-primary);
}

.result-text {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  padding: 1rem;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border-radius: 8px;
  margin-top: 0.5rem;
}

.result-highlight {
  animation: resultPulse 3s ease-in-out;
}

@keyframes resultPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.vote-question {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.overlay-vote-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.overlay-vote-option {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.5rem;
}

.option-text {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.option-progress {
  position: relative;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  height: 20px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.vote-timer {
  text-align: center;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--warning-color);
  margin-top: 0.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.75rem 0.5rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.count-update {
  animation: countBounce 0.5s ease-out;
}

@keyframes countBounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Stream Alerts */
.stream-alert {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 400px;
  max-width: 600px;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.alert-icon {
  font-size: 2rem;
}

.alert-message {
  font-size: 1.2rem;
  font-weight: 600;
}

@keyframes alertSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes alertSlideOut {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

/* Responsive overlays */
@media (max-width: 768px) {
  .overlay-recent-activity,
  .overlay-vote-progress,
  .overlay-stream-stats {
    width: 90%;
    left: 5%;
    right: 5%;
  }

  .overlay-wheel-result {
    width: 90%;
    left: 5%;
    transform: none;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stream-alert {
    min-width: 300px;
    max-width: 90%;
  }

  .alert-content {
    flex-direction: column;
    text-align: center;
  }
}

/* Auto-Pilot Panel Styles */
.autopilot-panel {
  background: linear-gradient(135deg, var(--background-dark) 0%, #1a0a1a 100%);
  border: 2px solid var(--primary-color);
  border-radius: 15px;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  box-shadow: 0 20px 40px rgba(255, 0, 80, 0.3);
}

.autopilot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--primary-color);
}

.autopilot-header h3 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.autopilot-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
}

.status-indicator.online {
  background: var(--success-color);
  box-shadow: 0 0 10px var(--success-color);
  animation: pulse 2s infinite;
}

.status-indicator.offline {
  background: #ff4444;
}

.autopilot-setup {
  margin-bottom: 1.5rem;
}

.setup-group {
  margin-bottom: 1rem;
}

.setup-group label {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.setup-group input,
.setup-group select {
  width: 100%;
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem;
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.setup-group input:focus,
.setup-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(255, 0, 80, 0.3);
}

.autopilot-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.autopilot-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.start-btn {
  background: linear-gradient(45deg, var(--success-color), #00cc66);
  color: white;
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
}

.stop-btn {
  background: linear-gradient(45deg, #ff4444, #cc0000);
  color: white;
}

.stop-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

.autopilot-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.autopilot-settings {
  margin-bottom: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.autopilot-settings h4 {
  color: var(--accent-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.setting-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.setting-row label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  min-width: 150px;
}

.setting-row input[type="range"] {
  flex: 1;
  background: var(--background-light);
  border-radius: 5px;
  height: 6px;
  outline: none;
  -webkit-appearance: none;
}

.setting-row input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(255, 0, 80, 0.3);
}

.setting-row input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(255, 0, 80, 0.3);
}

.setting-row span {
  color: var(--accent-color);
  font-weight: 600;
  min-width: 50px;
  text-align: right;
}

.autopilot-stats {
  background: rgba(255, 0, 80, 0.1);
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid var(--primary-color);
}

.autopilot-stats h4 {
  color: var(--primary-color);
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.stats-row span {
  color: var(--text-secondary);
}

.stats-row strong {
  color: var(--accent-color);
  font-weight: 700;
}

/* Auto-Pilot Toggle Button */
#autopilot-toggle:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 0, 80, 0.6);
}

/* Responsive Auto-Pilot Panel */
@media (max-width: 768px) {
  .autopilot-panel {
    width: 90%;
    max-width: 400px;
    padding: 1.5rem;
  }

  .autopilot-controls {
    flex-direction: column;
  }

  .setting-row {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .setting-row label {
    min-width: auto;
  }

  .stats-row {
    flex-direction: column;
    gap: 0.25rem;
  }
}

import './style.css'

// TikTok Live Creative Studio - Main Application
class TikTokCreativeStudio {
  constructor() {
    this.currentSection = 'drawing-section';
    this.viewerCount = 0;
    this.activeProjects = 1;
    this.creativityLevel = 'High';

    // Drawing system
    this.canvas = null;
    this.ctx = null;
    this.isDrawing = false;
    this.currentTool = 'brush';
    this.currentColor = '#ff1744';
    this.currentSize = 5;
    this.drawings = [];

    // Story system
    this.currentStory = ['Once upon a time, in a magical land far away...'];
    this.stories = [];

    // Gallery system
    this.gallery = [];

    // Contest system
    this.contests = [
      { title: 'Draw Your Dream House', description: 'Create the most amazing dream house you can imagine!', time: 300 },
      { title: 'Design a Superhero', description: 'Create your own unique superhero character!', time: 240 },
      { title: 'Magical Creature', description: 'Draw a creature from a fantasy world!', time: 180 }
    ];
    this.currentContest = 0;
    this.contestTimer = null;
    this.contestTimeLeft = 300;

    this.chatMessages = [];
    this.autoPilot = null;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.initializeCanvas();
    this.updateDisplay();
    this.simulateViewerActivity();
    this.initializeAutoPilot();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchSection(e.target.dataset.section);
      });
    });

    // Drawing tools
    document.querySelectorAll('.tool-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.selectTool(e.target.dataset.tool);
      });
    });

    // Color picker
    document.getElementById('color-picker').addEventListener('change', (e) => {
      this.currentColor = e.target.value;
    });

    document.querySelectorAll('.color-preset').forEach(preset => {
      preset.addEventListener('click', (e) => {
        this.currentColor = e.target.dataset.color;
        document.getElementById('color-picker').value = this.currentColor;
      });
    });

    // Brush size
    document.getElementById('brush-size').addEventListener('input', (e) => {
      this.currentSize = e.target.value;
      document.getElementById('size-display').textContent = `${e.target.value}px`;
    });

    // Drawing controls
    document.getElementById('clear-canvas').addEventListener('click', () => this.clearCanvas());
    document.getElementById('save-drawing').addEventListener('click', () => this.saveDrawing());
    document.getElementById('share-drawing').addEventListener('click', () => this.shareDrawing());
    document.getElementById('auto-draw').addEventListener('click', () => this.autoDraw());

    // Story controls
    document.getElementById('add-story').addEventListener('click', () => this.addToStory());
    document.getElementById('new-story').addEventListener('click', () => this.newStory());
    document.getElementById('auto-story').addEventListener('click', () => this.autoStory());
    document.getElementById('save-story').addEventListener('click', () => this.saveStory());

    // Gallery controls
    document.getElementById('refresh-gallery').addEventListener('click', () => this.refreshGallery());
    document.getElementById('slideshow-mode').addEventListener('click', () => this.startSlideshow());
    document.getElementById('vote-favorite').addEventListener('click', () => this.voteFavorite());

    // Contest controls
    document.getElementById('start-contest').addEventListener('click', () => this.startContest());
    document.getElementById('submit-entry').addEventListener('click', () => this.submitEntry());
    document.getElementById('view-entries').addEventListener('click', () => this.viewEntries());

    // Chat
    document.getElementById('send-chat').addEventListener('click', () => this.sendChatMessage());
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendChatMessage();
    });

    // Auto-pilot
    document.getElementById('autopilot-toggle').addEventListener('click', () => this.toggleAutoPilot());
  }

  switchSection(sectionId) {
    document.querySelectorAll('.creative-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');

    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

    this.currentSection = sectionId;
  }

  // Drawing System
  initializeCanvas() {
    this.canvas = document.getElementById('drawing-canvas');
    this.ctx = this.canvas.getContext('2d');

    // Set canvas background
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Mouse events
    this.canvas.addEventListener('mousedown', (e) => this.startDrawing(e));
    this.canvas.addEventListener('mousemove', (e) => this.draw(e));
    this.canvas.addEventListener('mouseup', () => this.stopDrawing());
    this.canvas.addEventListener('mouseout', () => this.stopDrawing());
  }

  selectTool(tool) {
    this.currentTool = tool;
    document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
  }

  startDrawing(e) {
    this.isDrawing = true;
    const rect = this.canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    this.ctx.beginPath();
    this.ctx.moveTo(x, y);
  }

  draw(e) {
    if (!this.isDrawing) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    this.ctx.lineWidth = this.currentSize;
    this.ctx.lineCap = 'round';

    if (this.currentTool === 'eraser') {
      this.ctx.globalCompositeOperation = 'destination-out';
    } else {
      this.ctx.globalCompositeOperation = 'source-over';
      this.ctx.strokeStyle = this.currentColor;
    }

    this.ctx.lineTo(x, y);
    this.ctx.stroke();
    this.ctx.beginPath();
    this.ctx.moveTo(x, y);
  }

  stopDrawing() {
    this.isDrawing = false;
    this.ctx.beginPath();
  }

  clearCanvas() {
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    this.addChatMessage('Creative Studio', '🗑️ Canvas cleared! Ready for new masterpiece!');
  }

  saveDrawing() {
    const imageData = this.canvas.toDataURL();
    this.drawings.push({
      id: Date.now(),
      data: imageData,
      timestamp: new Date().toLocaleString()
    });
    this.addChatMessage('Creative Studio', '💾 Drawing saved to gallery!');
    this.refreshGallery();
  }

  shareDrawing() {
    this.saveDrawing();
    this.addChatMessage('Creative Studio', '📤 Drawing shared with everyone!');
  }

  autoDraw() {
    this.addChatMessage('Creative Studio', '🤖 Auto-drawing activated!');
    // Simple auto-draw simulation
    const colors = ['#ff1744', '#4caf50', '#2196f3', '#ffc107', '#9c27b0'];
    let colorIndex = 0;

    const autoDrawInterval = setInterval(() => {
      this.currentColor = colors[colorIndex % colors.length];
      colorIndex++;

      // Draw random shapes
      this.ctx.strokeStyle = this.currentColor;
      this.ctx.lineWidth = Math.random() * 10 + 2;
      this.ctx.beginPath();
      this.ctx.arc(
        Math.random() * this.canvas.width,
        Math.random() * this.canvas.height,
        Math.random() * 50 + 10,
        0,
        2 * Math.PI
      );
      this.ctx.stroke();

      if (colorIndex >= 10) {
        clearInterval(autoDrawInterval);
        this.addChatMessage('Creative Studio', '✨ Auto-drawing complete!');
      }
    }, 500);
  }

  // Story System
  addToStory() {
    const input = document.getElementById('story-input');
    const text = input.value.trim();

    if (text) {
      this.currentStory.push(text);
      this.updateStoryDisplay();
      input.value = '';
      this.addChatMessage('Creative Studio', '📖 Story continued!');
    }
  }

  newStory() {
    this.currentStory = ['Once upon a time...'];
    this.updateStoryDisplay();
    this.addChatMessage('Creative Studio', '📝 New story started!');
  }

  autoStory() {
    const continuations = [
      'Suddenly, a magical portal appeared...',
      'The brave hero decided to explore further...',
      'A mysterious creature emerged from the shadows...',
      'The adventure was just beginning...',
      'Little did they know, danger was approaching...'
    ];

    const randomContinuation = continuations[Math.floor(Math.random() * continuations.length)];
    this.currentStory.push(randomContinuation);
    this.updateStoryDisplay();
    this.addChatMessage('Creative Studio', '🤖 Story auto-continued!');
  }

  updateStoryDisplay() {
    const storyContent = document.getElementById('story-content');
    storyContent.innerHTML = this.currentStory.map(paragraph => `<p>${paragraph}</p>`).join('');
  }

  saveStory() {
    this.stories.push({
      id: Date.now(),
      content: [...this.currentStory],
      timestamp: new Date().toLocaleString()
    });
    this.addChatMessage('Creative Studio', '💾 Story saved!');
  }

  // Gallery System
  refreshGallery() {
    const galleryGrid = document.getElementById('gallery-grid');
    galleryGrid.innerHTML = this.drawings.map(drawing => `
      <div class="gallery-item">
        <img src="${drawing.data}" alt="Drawing">
        <div class="gallery-info">
          <span>${drawing.timestamp}</span>
        </div>
      </div>
    `).join('');
  }

  startSlideshow() {
    this.addChatMessage('Creative Studio', '🎬 Slideshow mode activated!');
    // Slideshow logic would go here
  }

  voteFavorite() {
    this.addChatMessage('Creative Studio', '⭐ Vote cast for favorite artwork!');
  }

  // Contest System
  startContest() {
    const contest = this.contests[this.currentContest];
    this.contestTimeLeft = contest.time;

    document.getElementById('contest-title').textContent = contest.title;
    document.getElementById('contest-description').textContent = contest.description;

    this.contestTimer = setInterval(() => {
      this.contestTimeLeft--;
      const minutes = Math.floor(this.contestTimeLeft / 60);
      const seconds = this.contestTimeLeft % 60;
      document.getElementById('contest-time').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      if (this.contestTimeLeft <= 0) {
        this.endContest();
      }
    }, 1000);

    this.addChatMessage('Creative Studio', `🏆 Contest started: ${contest.title}!`);
  }

  endContest() {
    if (this.contestTimer) {
      clearInterval(this.contestTimer);
      this.contestTimer = null;
    }

    this.addChatMessage('Creative Studio', '⏰ Contest ended! Submit your entries!');
    this.currentContest = (this.currentContest + 1) % this.contests.length;
  }

  submitEntry() {
    this.saveDrawing();
    this.addChatMessage('Creative Studio', '📤 Contest entry submitted!');
  }

  viewEntries() {
    this.switchSection('gallery-section');
    this.refreshGallery();
  }

  // Chat System
  sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage('You', message);
      input.value = '';
      this.processChatCommand('You', message);
    }
  }

  addChatMessage(username, message) {
    const chatDisplay = document.getElementById('chat-display');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
      <span class="chat-username">${username}:</span>
      <span class="chat-text">${message}</span>
      <span class="chat-timestamp">${timestamp}</span>
    `;

    chatDisplay.appendChild(messageDiv);
    chatDisplay.scrollTop = chatDisplay.scrollHeight;

    while (chatDisplay.children.length > 50) {
      chatDisplay.removeChild(chatDisplay.firstChild);
    }

    this.chatMessages.push({ username, message, timestamp });
  }

  processChatCommand(username, message) {
    const msg = message.toLowerCase();

    if (msg.includes('!draw') || msg.includes('!paint')) {
      this.switchSection('drawing-section');
    } else if (msg.includes('!story')) {
      this.switchSection('story-section');
    } else if (msg.includes('!gallery')) {
      this.switchSection('gallery-section');
    } else if (msg.includes('!contest')) {
      this.switchSection('contests-section');
      this.startContest();
    } else if (msg.includes('!clear')) {
      this.clearCanvas();
    }
  }

  // Display Updates
  updateDisplay() {
    document.getElementById('viewer-count').textContent = `${this.viewerCount} artists`;
    document.getElementById('active-projects').textContent = `${this.activeProjects} project`;
    document.getElementById('creativity-level').textContent = this.creativityLevel;
  }

  // Viewer Activity Simulation
  simulateViewerActivity() {
    setInterval(() => {
      const change = Math.floor(Math.random() * 10) - 5;
      this.viewerCount = Math.max(1, this.viewerCount + change);
      this.updateDisplay();
    }, 8000);

    setInterval(() => {
      if (Math.random() < 0.3) {
        this.simulateViewerMessage();
      }
    }, 6000);
  }

  simulateViewerMessage() {
    const viewers = ['Artist2024', 'CreativeMind', 'DrawingFan', 'StoryTeller', 'ArtLover'];
    const messages = [
      'Amazing artwork!',
      'Love this creative session!',
      'Can we draw together?',
      'Great story so far!',
      'This is so inspiring!',
      'What colors are you using?',
      'Beautiful creation!',
      'Keep going!',
      'So creative!',
      'Art is life!'
    ];

    const randomViewer = viewers[Math.floor(Math.random() * viewers.length)];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addChatMessage(randomViewer, randomMessage);
  }

  // Auto-Pilot System
  initializeAutoPilot() {
    this.addChatMessage('Creative Studio', '🤖 Auto-Pilot available! Click 🤖 to start automated creative sessions!');
  }

  toggleAutoPilot() {
    if (!this.autoPilot || !this.autoPilot.isActive) {
      this.autoPilot = { isActive: true, username: 'CreativeStreamer' };
      this.addChatMessage('Auto-Pilot', '🎨 Creative Auto-Pilot activated! Let the creativity flow!');
      document.getElementById('live-indicator').classList.add('active');

      // Auto creative activities
      setInterval(() => {
        if (this.autoPilot && this.autoPilot.isActive) {
          const activities = ['drawing', 'story', 'contest'];
          const randomActivity = activities[Math.floor(Math.random() * activities.length)];

          switch (randomActivity) {
            case 'drawing':
              this.switchSection('drawing-section');
              this.autoDraw();
              break;
            case 'story':
              this.switchSection('story-section');
              this.autoStory();
              break;
            case 'contest':
              this.switchSection('contests-section');
              this.startContest();
              break;
          }
        }
      }, 30000);
    } else {
      this.autoPilot.isActive = false;
      this.addChatMessage('Auto-Pilot', '🎨 Creative Auto-Pilot stopped. Thanks for creating!');
      document.getElementById('live-indicator').classList.remove('active');
    }
  }

  // Browser Source Mode
  enableBrowserSourceMode() {
    document.body.classList.add('browser-source');
    document.getElementById('autopilot-toggle').style.display = 'none';
  }
}

// Initialize the Creative Studio
document.addEventListener('DOMContentLoaded', () => {
  const creativeStudio = new TikTokCreativeStudio();

  window.TikTokCreativeStudio = creativeStudio;

  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('mode') === 'browser-source') {
    creativeStudio.enableBrowserSourceMode();
  }
});

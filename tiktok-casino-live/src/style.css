/* TikTok Casino Live - Stiluri complete */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;
  min-height: 100vh;
  overflow-x: hidden;
}

.casino-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header */
.casino-header {
  background: linear-gradient(90deg, #ff6b35 0%, #f7931e 50%, #ffd700 100%);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
}

.casino-title {
  font-size: 28px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.casino-stats {
  display: flex;
  gap: 20px;
  align-items: center;
}

.stat-item {
  background: rgba(255,255,255,0.2);
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: bold;
  backdrop-filter: blur(10px);
}

.live-indicator {
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: bold;
  background: #666;
  color: white;
}

.live-indicator.connected {
  background: #ff0000;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Main Content */
.casino-main {
  display: flex;
  flex: 1;
  gap: 20px;
  padding: 20px;
}

.game-area {
  flex: 2;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Game Tabs */
.game-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.game-tab {
  padding: 12px 20px;
  background: rgba(255,255,255,0.1);
  border: none;
  border-radius: 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.game-tab:hover {
  background: rgba(255,255,255,0.2);
  transform: translateY(-2px);
}

.game-tab.active {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

/* Game Sections */
.game-section {
  display: none;
}

.game-section.active {
  display: block;
}

/* Roulette */
.roulette-container {
  text-align: center;
}

.roulette-wheel {
  width: 300px;
  height: 300px;
  border: 10px solid #ffd700;
  border-radius: 50%;
  margin: 20px auto;
  background: conic-gradient(
    #ff0000 0deg 18deg,
    #000000 18deg 36deg,
    #ff0000 36deg 54deg,
    #000000 54deg 72deg,
    #ff0000 72deg 90deg,
    #000000 90deg 108deg,
    #ff0000 108deg 126deg,
    #000000 126deg 144deg,
    #ff0000 144deg 162deg,
    #000000 162deg 180deg,
    #ff0000 180deg 198deg,
    #000000 198deg 216deg,
    #ff0000 216deg 234deg,
    #000000 234deg 252deg,
    #ff0000 252deg 270deg,
    #000000 270deg 288deg,
    #ff0000 288deg 306deg,
    #000000 306deg 324deg,
    #ff0000 324deg 342deg,
    #000000 342deg 360deg
  );
  position: relative;
}

.roulette-ball {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 10px rgba(255,255,255,0.8);
}

@keyframes spin {
  from { transform: translateX(-50%) rotate(0deg) translateX(120px) rotate(0deg); }
  to { transform: translateX(-50%) rotate(1440deg) translateX(120px) rotate(-1440deg); }
}

.last-number {
  font-size: 24px;
  font-weight: bold;
  color: #ffd700;
  margin: 20px 0;
}

/* Betting Area */
.betting-area {
  margin: 20px 0;
}

.bet-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
  flex-wrap: wrap;
}

.bet-amount {
  padding: 10px;
  border: 2px solid #ffd700;
  border-radius: 8px;
  background: rgba(0,0,0,0.3);
  color: white;
  text-align: center;
  width: 100px;
}

.bet-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin: 20px 0;
}

.bet-btn {
  padding: 15px;
  border: 2px solid #ffd700;
  border-radius: 8px;
  background: rgba(255,215,0,0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.bet-btn:hover {
  background: rgba(255,215,0,0.4);
  transform: scale(1.05);
}

/* Action Buttons */
.action-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 10px;
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.action-btn:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Chat */
.chat-container {
  background: rgba(0,0,0,0.3);
  border-radius: 15px;
  padding: 15px;
  height: 300px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 10px;
  padding: 10px;
  background: rgba(255,255,255,0.05);
  border-radius: 8px;
}

.chat-message {
  margin: 5px 0;
  padding: 8px;
  border-radius: 8px;
  background: rgba(255,255,255,0.1);
}

.chat-username {
  font-weight: bold;
  color: #ffd700;
}

/* Connection Panel */
.connection-panel {
  background: rgba(0,0,0,0.3);
  border-radius: 15px;
  padding: 20px;
}

.connection-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-input {
  padding: 12px;
  border: 2px solid #ffd700;
  border-radius: 8px;
  background: rgba(0,0,0,0.3);
  color: white;
  font-size: 16px;
}

.form-input::placeholder {
  color: rgba(255,255,255,0.6);
}

/* Credits Display */
.credits-display {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .casino-main {
    flex-direction: column;
  }

  .casino-header {
    flex-direction: column;
    gap: 10px;
  }

  .casino-stats {
    flex-wrap: wrap;
    justify-content: center;
  }

  .game-tabs {
    justify-content: center;
  }

  .roulette-wheel {
    width: 250px;
    height: 250px;
  }
}

@echo off
title TikTok Live Adventure Game

echo.
echo 🎮 TikTok Live Adventure Game
echo ================================
echo.

REM Verifică dacă Node.js este instalat
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js nu este instalat!
    echo Te rog instalează Node.js de la: https://nodejs.org/
    pause
    exit /b 1
)

REM Verifică dacă npm este instalat
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm nu este instalat!
    echo Te rog instalează npm împreună cu Node.js
    pause
    exit /b 1
)

echo ✅ Node.js și npm sunt instalate
echo.

REM Verifică dacă dependențele sunt instalate
if not exist "node_modules" (
    echo 📦 Instalez dependențele...
    npm install
    echo.
)

echo 🚀 Pornesc aplicația...
echo.
echo 📱 Aplicația va fi disponibilă la:
echo    http://localhost:5173/
echo.
echo 🎥 Pentru OBS Browser Source folosește:
echo    http://localhost:5173/?mode=browser-source
echo.
echo 🤖 Pentru Auto-Pilot:
echo    1. Deschide aplicația în browser
echo    2. Apasă butonul 🤖 din centru
echo    3. Introdu username-ul tău TikTok
echo    4. Apasă 'Pornește Auto-Pilot'
echo.
echo ⏹️  Pentru a opri aplicația: Ctrl+C
echo ================================
echo.

REM Pornește aplicația
npm run dev

pause

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🎨 TikTok Live Creative Studio</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <div id="creative-container">
        <header class="creative-header">
          <h1>🎨 TikTok Live Creative Studio</h1>
          <div class="creative-status">
            <span id="viewer-count">0 artists</span>
            <span id="active-projects">1 project</span>
            <span id="creativity-level">High</span>
          </div>
        </header>

        <button id="autopilot-toggle" class="autopilot-toggle">🤖</button>

        <main class="creative-main">
          <section id="drawing-section" class="creative-section active">
            <div class="drawing-container">
              <div class="canvas-area">
                <canvas id="drawing-canvas" width="800" height="600"></canvas>
                <div class="canvas-tools">
                  <div class="tool-group">
                    <button class="tool-btn active" data-tool="brush">🖌️ Brush</button>
                    <button class="tool-btn" data-tool="eraser">🧽 Eraser</button>
                    <button class="tool-btn" data-tool="line">📏 Line</button>
                    <button class="tool-btn" data-tool="circle">⭕ Circle</button>
                  </div>
                  <div class="color-group">
                    <input type="color" id="color-picker" value="#ff1744">
                    <div class="color-presets">
                      <div class="color-preset" style="background: #ff1744" data-color="#ff1744"></div>
                      <div class="color-preset" style="background: #4caf50" data-color="#4caf50"></div>
                      <div class="color-preset" style="background: #2196f3" data-color="#2196f3"></div>
                      <div class="color-preset" style="background: #ffc107" data-color="#ffc107"></div>
                    </div>
                  </div>
                  <div class="size-group">
                    <label>Size:</label>
                    <input type="range" id="brush-size" min="1" max="50" value="5">
                    <span id="size-display">5px</span>
                  </div>
                </div>
              </div>

              <div class="drawing-controls">
                <button id="clear-canvas" class="control-btn">🗑️ Clear</button>
                <button id="save-drawing" class="control-btn">💾 Save</button>
                <button id="share-drawing" class="control-btn">📤 Share</button>
                <button id="auto-draw" class="control-btn">🤖 Auto Draw</button>
              </div>
            </div>
          </section>

          <section id="story-section" class="creative-section">
            <div class="story-container">
              <h2>📖 Collaborative Story</h2>
              <div class="story-display">
                <div id="story-content" class="story-content">
                  <p>Once upon a time, in a magical land far away...</p>
                </div>
              </div>

              <div class="story-controls">
                <div class="story-input">
                  <textarea id="story-input" placeholder="Add the next part of the story..."></textarea>
                  <button id="add-story" class="story-btn">➕ Add to Story</button>
                </div>
                <div class="story-actions">
                  <button id="new-story" class="story-btn">📝 New Story</button>
                  <button id="auto-story" class="story-btn">🤖 Auto Continue</button>
                  <button id="save-story" class="story-btn">💾 Save Story</button>
                </div>
              </div>
            </div>
          </section>

          <section id="gallery-section" class="creative-section">
            <div class="gallery-container">
              <h2>🖼️ Live Gallery</h2>
              <div id="gallery-grid" class="gallery-grid">
                <!-- Gallery items will be populated here -->
              </div>

              <div class="gallery-controls">
                <button id="refresh-gallery" class="gallery-btn">🔄 Refresh</button>
                <button id="slideshow-mode" class="gallery-btn">🎬 Slideshow</button>
                <button id="vote-favorite" class="gallery-btn">⭐ Vote Favorite</button>
              </div>
            </div>
          </section>

          <section id="contests-section" class="creative-section">
            <div class="contests-container">
              <h2>🏆 Creative Contests</h2>
              <div class="contest-display">
                <div class="contest-card active" id="current-contest">
                  <div class="contest-icon">🎨</div>
                  <div class="contest-info">
                    <h3 id="contest-title">Draw Your Dream House</h3>
                    <p id="contest-description">Create the most amazing dream house you can imagine!</p>
                    <div class="contest-timer">
                      <span id="contest-time">5:00</span> remaining
                    </div>
                  </div>
                </div>
              </div>

              <div class="contest-actions">
                <button id="start-contest" class="contest-btn">🚀 Start Contest</button>
                <button id="submit-entry" class="contest-btn">📤 Submit Entry</button>
                <button id="view-entries" class="contest-btn">👀 View Entries</button>
              </div>
            </div>
          </section>

          <section id="chat-section" class="creative-section">
            <div class="chat-container">
              <h2>💬 Creative Chat</h2>
              <div id="chat-display" class="chat-display"></div>
              <div class="chat-controls">
                <input type="text" id="chat-input" placeholder="Share your creative ideas...">
                <button id="send-chat" class="control-btn">Send</button>
              </div>
            </div>
          </section>
        </main>

        <nav class="creative-nav">
          <button class="nav-btn active" data-section="drawing-section">🎨 Draw</button>
          <button class="nav-btn" data-section="story-section">📖 Story</button>
          <button class="nav-btn" data-section="gallery-section">🖼️ Gallery</button>
          <button class="nav-btn" data-section="contests-section">🏆 Contests</button>
          <button class="nav-btn" data-section="chat-section">💬 Chat</button>
        </nav>

        <div id="live-indicator" class="live-indicator">LIVE CREATIVE</div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

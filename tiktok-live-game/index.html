<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TikTok Live Adventure Game</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <!-- Game Container -->
      <div id="game-container">
        <!-- Header -->
        <header class="game-header">
          <h1>🎮 TikTok Live Adventure</h1>
          <div class="game-status">
            <span id="viewer-count">0 viewers</span>
            <span id="game-mode">Adventure Mode</span>
          </div>
        </header>

        <!-- Main Game Area -->
        <main class="game-main">
          <!-- Adventure Wheel Section -->
          <section id="wheel-section" class="game-section active">
            <div class="wheel-container">
              <canvas id="adventure-wheel" width="400" height="400"></canvas>
              <button id="spin-wheel" class="spin-button">SPIN THE WHEEL!</button>
            </div>
            <div class="wheel-controls">
              <button id="add-option" class="control-btn">Add Option</button>
              <button id="clear-wheel" class="control-btn">Clear All</button>
              <input type="text" id="new-option" placeholder="Enter new adventure option..." maxlength="30">
            </div>
          </section>

          <!-- Voting Section -->
          <section id="voting-section" class="game-section">
            <div class="voting-container">
              <h2 id="voting-question">What should we do next?</h2>
              <div id="voting-options" class="voting-options"></div>
              <div class="voting-controls">
                <button id="start-vote" class="control-btn">Start Vote</button>
                <button id="end-vote" class="control-btn">End Vote</button>
                <span id="vote-timer">30s</span>
              </div>
            </div>
          </section>

          <!-- Chat Integration Section -->
          <section id="chat-section" class="game-section">
            <div class="chat-container">
              <div id="chat-display" class="chat-display"></div>
              <div class="chat-controls">
                <input type="text" id="chat-input" placeholder="Simulate chat message...">
                <button id="send-chat" class="control-btn">Send</button>
              </div>
            </div>
          </section>

          <!-- Story Progress Section -->
          <section id="story-section" class="game-section">
            <div class="story-container">
              <div id="story-display" class="story-display">
                <h3>Adventure Story</h3>
                <div id="story-content">Welcome to the TikTok Live Adventure! Your viewers will help guide this epic journey...</div>
              </div>
              <div class="story-controls">
                <button id="new-chapter" class="control-btn">New Chapter</button>
                <button id="reset-story" class="control-btn">Reset Story</button>
              </div>
            </div>
          </section>
        </main>

        <!-- Game Navigation -->
        <nav class="game-nav">
          <button class="nav-btn active" data-section="wheel-section">🎡 Wheel</button>
          <button class="nav-btn" data-section="voting-section">🗳️ Vote</button>
          <button class="nav-btn" data-section="chat-section">💬 Chat</button>
          <button class="nav-btn" data-section="story-section">📖 Story</button>
        </nav>

        <!-- Settings Panel -->
        <div id="settings-panel" class="settings-panel">
          <h3>Game Settings</h3>
          <div class="setting-group">
            <label>Wheel Colors:</label>
            <select id="color-theme">
              <option value="rainbow">Rainbow</option>
              <option value="neon">Neon</option>
              <option value="pastel">Pastel</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
          <div class="setting-group">
            <label>Sound Effects:</label>
            <input type="checkbox" id="sound-enabled" checked>
          </div>
          <div class="setting-group">
            <label>Animation Speed:</label>
            <input type="range" id="animation-speed" min="1" max="5" value="3">
          </div>
          <button id="close-settings" class="control-btn">Close</button>
        </div>

        <!-- Settings Toggle -->
        <button id="settings-toggle" class="settings-toggle">⚙️</button>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

// TikTok Live Connector - Integrare reală cu TikTok Live
import { WebcastPushConnection } from 'tiktok-live-connector';

export class TikTokLiveConnector {
  constructor(casino) {
    this.casino = casino;
    this.connection = null;
    this.isConnected = false;
    this.username = '';
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    
    // Statistici live
    this.stats = {
      totalViewers: 0,
      totalLikes: 0,
      totalGifts: 0,
      totalComments: 0,
      connectedTime: 0
    };
  }

  // Conectare la TikTok Live
  async connectToLive(username) {
    try {
      console.log(`🔄 Conectare la @${username}...`);
      this.username = username;
      
      // Creează conexiunea
      this.connection = new WebcastPushConnection(username, {
        processInitialData: true,
        enableExtendedGiftInfo: true,
        enableWebsocketUpgrade: true,
        requestPollingIntervalMs: 1000,
        sessionId: undefined,
        clientParams: {},
        requestHeaders: {},
        websocketHeaders: {},
        requestOptions: {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        }
      });

      // Event listeners pentru conexiune
      this.setupConnectionEvents();
      
      // Conectează
      const state = await this.connection.connect();
      console.log(`✅ Conectat la @${username}!`, state);
      
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.stats.connectedTime = Date.now();
      
      // Notifică casino-ul
      this.casino.onTikTokConnected(username, state);
      
      return true;
      
    } catch (error) {
      console.error('❌ Eroare conectare:', error);
      this.casino.onTikTokError(`Eroare conectare: ${error.message}`);
      
      // Încearcă reconectarea
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`🔄 Reconectare ${this.reconnectAttempts}/${this.maxReconnectAttempts}...`);
        setTimeout(() => this.connectToLive(username), 5000);
      }
      
      return false;
    }
  }

  // Configurează event listeners
  setupConnectionEvents() {
    if (!this.connection) return;

    // Conexiune stabilită
    this.connection.on('connected', (state) => {
      console.log('🎉 TikTok Live conectat!', state);
      this.casino.addChatMessage('🔴 LIVE', `Conectat la @${this.username} - ${state.roomInfo?.viewerCount || 0} viewers`);
    });

    // Conexiune închisă
    this.connection.on('disconnected', () => {
      console.log('❌ TikTok Live deconectat');
      this.isConnected = false;
      this.casino.onTikTokDisconnected();
    });

    // Erori
    this.connection.on('error', (error) => {
      console.error('❌ Eroare TikTok Live:', error);
      this.casino.onTikTokError(error.message);
    });

    // Comentarii în chat
    this.connection.on('chat', (data) => {
      const username = data.uniqueId;
      const message = data.comment;
      
      console.log(`💬 ${username}: ${message}`);
      this.stats.totalComments++;
      
      // Procesează comenzile de joc
      this.casino.handleTikTokComment(username, message);
    });

    // Like-uri
    this.connection.on('like', (data) => {
      const username = data.uniqueId;
      const likeCount = data.likeCount;
      
      console.log(`❤️ ${username} a dat ${likeCount} like-uri`);
      this.stats.totalLikes += likeCount;
      
      // Dă credite pentru like-uri
      this.casino.handleTikTokLikes(username, likeCount);
    });

    // Cadouri
    this.connection.on('gift', (data) => {
      const username = data.uniqueId;
      const giftName = data.giftName;
      const giftId = data.giftId;
      const repeatCount = data.repeatCount;
      const diamondCount = data.diamondCount;
      
      console.log(`🎁 ${username} a trimis ${repeatCount}x ${giftName} (${diamondCount} diamante)`);
      this.stats.totalGifts++;
      
      // Procesează cadoul în casino
      this.casino.handleTikTokGift(username, giftName, giftId, repeatCount, diamondCount);
    });

    // Urmăritori noi
    this.connection.on('follow', (data) => {
      const username = data.uniqueId;
      console.log(`👥 ${username} te urmărește acum!`);
      
      // Bonus pentru urmăritori noi
      this.casino.handleTikTokFollow(username);
    });

    // Share-uri
    this.connection.on('share', (data) => {
      const username = data.uniqueId;
      console.log(`📤 ${username} a distribuit live-ul!`);
      
      // Bonus pentru share
      this.casino.handleTikTokShare(username);
    });

    // Actualizări viewer count
    this.connection.on('roomUser', (data) => {
      this.stats.totalViewers = data.viewerCount || 0;
      this.casino.updateViewerCount(this.stats.totalViewers);
    });

    // Utilizatori care se alătură
    this.connection.on('member', (data) => {
      const username = data.uniqueId;
      console.log(`🎉 ${username} s-a alăturat live-ului!`);
      
      // Mesaj de bun venit
      this.casino.handleTikTokJoin(username);
    });
  }

  // Deconectare
  disconnect() {
    if (this.connection && this.isConnected) {
      this.connection.disconnect();
      this.isConnected = false;
      console.log('🔌 Deconectat de la TikTok Live');
    }
  }

  // Obține statistici
  getStats() {
    return {
      ...this.stats,
      isConnected: this.isConnected,
      username: this.username,
      connectedDuration: this.isConnected ? Date.now() - this.stats.connectedTime : 0
    };
  }

  // Verifică dacă este conectat
  isLiveConnected() {
    return this.isConnected && this.connection;
  }

  // Trimite mesaj în chat (dacă este posibil)
  sendChatMessage(message) {
    // Nota: TikTok Live API nu permite trimiterea de mesaje
    // Aceasta este doar pentru logging
    console.log(`📤 Ar trimite: ${message}`);
  }
}

// Utilități pentru TikTok Live
export class TikTokUtils {
  // Validează username TikTok
  static validateUsername(username) {
    if (!username || typeof username !== 'string') {
      return { valid: false, error: 'Username invalid' };
    }
    
    // Elimină @ dacă există
    username = username.replace('@', '');
    
    // Verifică format
    if (!/^[a-zA-Z0-9._]{1,24}$/.test(username)) {
      return { valid: false, error: 'Username trebuie să conțină doar litere, cifre, punct și underscore' };
    }
    
    return { valid: true, username };
  }

  // Calculează valoarea cadoului în credite
  static calculateGiftCredits(giftName, diamondCount, repeatCount = 1) {
    const giftValues = {
      'rose': 1,
      'heart': 5,
      'perfume': 20,
      'sports_car': 1000,
      'yacht': 5000,
      'rocket': 2000,
      'diamond': 100,
      'crown': 500,
      'default': diamondCount || 10
    };
    
    const baseValue = giftValues[giftName.toLowerCase()] || giftValues.default;
    return baseValue * repeatCount;
  }

  // Formatează timp
  static formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

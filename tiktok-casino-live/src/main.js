// TikTok Casino Live - Main Application cu integrare reală TikTok Live
import './style.css'
import { TikTokConnector, TikTokUtils } from './tiktok-connector.js'

class TikTokCasino {
  constructor() {
    // Starea jocului
    this.credits = 1000; // Start cu 1000 credite pentru testare
    this.totalGames = 0;
    this.totalWinnings = 0;
    this.viewerCount = 0;
    this.chatMessages = [];
    this.currentPlayer = 'Demo Player';

    // Integrare reală TikTok Live
    this.tiktokConnector = new TikTokConnector(this);
    this.isReallyConnected = false;

    // Sistem economic cu strategia de profit
    this.players = new Map();
    this.creditSystem = {
      tapCredits: 100,        // Credite pentru 3000 tap-uri
      shareCredits: 100,      // Credite pentru 30 distribuiri
      maxTapsPerUser: 3000,   // Limită tap-uri per utilizator
      maxSharesPerUser: 30,   // Limită distribuiri per utilizator

      // Credite pentru cadouri (retrăgabile)
      giftCredits: {
        'rose': 500,
        'heart': 200,
        'diamond': 1000,
        'perfume': 300,
        'sports_car': 2000,
        'yacht': 5000,
        'default': 150
      }
    };

    // Strategia de profit
    this.profitStrategy = {
      freeUserWinChance: 0.15,      // 15% pentru utilizatori fără cadouri
      smallGiftWinChance: 0.25,     // 25% pentru cadouri mici
      bigGiftWinChance: 0.35,       // 35% pentru cadouri mari
      bonusWinChance: 0.60,         // 60% bonus ocazional

      smallGiftThreshold: 500,      // Sub 500 credite = cadou mic
      bigGiftThreshold: 1000,       // Peste 1000 credite = cadou mare
      bonusFrequency: 0.1,          // 10% șanse pentru bonus

      maxWinMultiplier: 5,          // Câștig maxim 5x
      averageWinMultiplier: 2       // Câștig mediu 2x
    };

    // Starea jocurilor
    this.games = {
      roulette: {
        isSpinning: false,
        lastNumber: null,
        currentBets: new Map()
      },
      blackjack: {
        isPlaying: false,
        playerCards: [],
        dealerCards: [],
        playerScore: 0,
        dealerScore: 0
      },
      slots: {
        isSpinning: false,
        reels: ['🍒', '🍋', '🍊']
      },
      dice: {
        isRolling: false,
        dice1: 1,
        dice2: 1
      }
    };

    this.init();
  }

  init() {
    // Așteaptă ca DOM-ul să se încarce complet
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.initializeApp();
      });
    } else {
      this.initializeApp();
    }
  }

  initializeApp() {
    console.log('🎰 Inițializez TikTok Casino Live...');
    this.setupEventListeners();
    this.updateDisplay();
    this.addChatMessage('🎰 CASINO', 'Bun venit la TikTok Casino Live! Ai 1000 credite pentru testare!');
    this.addChatMessage('ℹ️ INFO', 'Conectează-te la TikTok Live pentru joc real sau testează local!');
    console.log('✅ Casino inițializat cu succes!');
  }

  setupEventListeners() {
    console.log('🔧 Configurez event listeners...');

    // Game tabs
    const gameTabs = document.querySelectorAll('.game-tab');
    console.log(`📋 Găsit ${gameTabs.length} tab-uri de joc`);
    gameTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const game = e.target.dataset.game;
        console.log(`🎮 Comutare la jocul: ${game}`);
        this.switchGame(game);
      });
    });

    // TikTok connection
    const connectBtn = document.getElementById('connect-tiktok');
    if (connectBtn) {
      connectBtn.addEventListener('click', () => {
        this.connectToTikTokLive();
      });
    }

    const disconnectBtn = document.getElementById('disconnect-tiktok');
    if (disconnectBtn) {
      disconnectBtn.addEventListener('click', () => {
        this.disconnectFromTikTokLive();
      });
    }

    // Credits actions
    const withdrawBtn = document.getElementById('withdraw-credits');
    if (withdrawBtn) {
      withdrawBtn.addEventListener('click', () => {
        this.withdrawCredits();
      });
    }

    // Bet controls
    const clearBetsBtn = document.getElementById('clear-bets');
    if (clearBetsBtn) {
      clearBetsBtn.addEventListener('click', () => {
        this.clearBets();
      });
    }

    const maxBetBtn = document.getElementById('max-bet');
    if (maxBetBtn) {
      maxBetBtn.addEventListener('click', () => {
        this.maxBet();
      });
    }

    // Roulette spin button
    const spinBtn = document.getElementById('spin-roulette');
    if (spinBtn) {
      spinBtn.addEventListener('click', () => {
        this.spinRoulette();
      });
    }

    console.log('✅ Event listeners configurați!');
  }

  // SISTEM GESTIONARE JUCĂTORI
  getOrCreatePlayer(username) {
    if (!this.players.has(username)) {
      this.players.set(username, {
        username: username,
        credits: 0,                    // Credite totale
        redeemableCredits: 0,          // Credite retrăgabile (doar din cadouri)
        freeCredits: 0,                // Credite gratuite (tap/share)
        hasGifted: false,              // A dat cadouri?
        totalGifts: 0,                 // Numărul total de cadouri
        tapsUsed: 0,                   // Tap-uri folosite
        sharesUsed: 0,                 // Distribuiri folosite
        gamesPlayed: 0,                // Jocuri jucate
        totalWinnings: 0,              // Câștiguri totale
        winStreak: 0,                  // Șirul de victorii
        lastBonusTime: 0,              // Ultima dată când a primit bonus
        playerType: 'free'             // free, small_gift, big_gift
      });
    }
    return this.players.get(username);
  }

  // SISTEM CREDITE PENTRU ACȚIUNI
  handleTapAction(username) {
    const player = this.getOrCreatePlayer(username);

    if (player.tapsUsed >= this.creditSystem.maxTapsPerUser) {
      this.addChatMessage('⚠️ LIMITĂ', `${username}, ai folosit deja toate tap-urile gratuite!`);
      return false;
    }

    player.tapsUsed++;

    // La fiecare 100 tap-uri, dă credite
    if (player.tapsUsed % 100 === 0) {
      const creditsEarned = Math.floor(this.creditSystem.tapCredits / 30); // ~3.33 credite per 100 tap-uri
      player.credits += creditsEarned;
      player.freeCredits += creditsEarned;

      this.addChatMessage('👆 TAP', `${username} +${creditsEarned} credite! (${player.tapsUsed}/3000 tap-uri)`);

      if (player.tapsUsed >= this.creditSystem.maxTapsPerUser) {
        this.addChatMessage('🎉 COMPLET', `${username} a completat toate tap-urile! +${this.creditSystem.tapCredits} credite bonus!`);
        player.credits += this.creditSystem.tapCredits;
        player.freeCredits += this.creditSystem.tapCredits;
      }

      // Actualizează afișajul dacă este jucătorul curent
      this.updateDisplay();
    }

    return true;
  }

  handleShareAction(username) {
    const player = this.getOrCreatePlayer(username);

    if (player.sharesUsed >= this.creditSystem.maxSharesPerUser) {
      this.addChatMessage('⚠️ LIMITĂ', `${username}, ai folosit deja toate distribuirile gratuite!`);
      return false;
    }

    player.sharesUsed++;

    // La fiecare distribuire, dă credite
    const creditsEarned = Math.floor(this.creditSystem.shareCredits / this.creditSystem.maxSharesPerUser);
    player.credits += creditsEarned;
    player.freeCredits += creditsEarned;

    this.addChatMessage('📤 SHARE', `${username} +${creditsEarned} credite! (${player.sharesUsed}/30 distribuiri)`);

    if (player.sharesUsed >= this.creditSystem.maxSharesPerUser) {
      this.addChatMessage('🎉 COMPLET', `${username} a completat toate distribuirile! +${this.creditSystem.shareCredits} credite bonus!`);
      player.credits += this.creditSystem.shareCredits;
      player.freeCredits += this.creditSystem.shareCredits;
    }

    // Actualizează afișajul dacă este jucătorul curent
    this.updateDisplay();
    return true;
  }

  // Handle real gifts from TikTok Live
  handleGiftReceived(username, giftName, repeatCount) {
    const player = this.getOrCreatePlayer(username);

    // Calculează creditele pentru cadou
    let giftCredits = 0;
    const giftNameLower = giftName.toLowerCase();

    // Verifică tipul cadoului
    if (giftNameLower.includes('rose')) {
      giftCredits = this.creditSystem.giftCredits.rose * repeatCount;
    } else if (giftNameLower.includes('heart')) {
      giftCredits = this.creditSystem.giftCredits.heart * repeatCount;
    } else if (giftNameLower.includes('diamond')) {
      giftCredits = this.creditSystem.giftCredits.diamond * repeatCount;
    } else if (giftNameLower.includes('perfume')) {
      giftCredits = this.creditSystem.giftCredits.perfume * repeatCount;
    } else if (giftNameLower.includes('car') || giftNameLower.includes('sports')) {
      giftCredits = this.creditSystem.giftCredits.sports_car * repeatCount;
    } else if (giftNameLower.includes('yacht')) {
      giftCredits = this.creditSystem.giftCredits.yacht * repeatCount;
    } else {
      giftCredits = this.creditSystem.giftCredits.default * repeatCount;
    }

    // Adaugă creditele și marchează-le ca retrăgabile
    player.credits += giftCredits;
    player.redeemableCredits += giftCredits;
    player.hasGifted = true;
    player.totalGifts += repeatCount;

    // Actualizează jucătorul curent
    this.credits = player.credits;

    // Declanșează jocuri bazate pe cadou
    if (giftNameLower.includes('rose')) {
      this.spinRoulette(); // Învârte ruleta pentru cadouri
    } else if (giftNameLower.includes('heart')) {
      this.dealBlackjack(); // Joacă blackjack pentru cadouri
    } else if (giftNameLower.includes('diamond')) {
      this.spinSlots(); // Joacă la păcănele pentru cadouri premium
    }

    // Notifică utilizatorul
    this.addChatMessage('🎁 CADOU', `Mulțumim ${username} pentru ${repeatCount}x ${giftName}!`);
    this.addChatMessage('💰 CREDITE', `${username} a primit ${giftCredits} credite RETRĂGABILE!`);

    // Actualizează afișajul
    this.updateDisplay();
  }

  // STRATEGIA DE PROFIT - CALCULEAZĂ ȘANSELE DE CÂȘTIG
  calculateWinChance(username) {
    const player = this.getOrCreatePlayer(username);

    // Actualizează tipul jucătorului
    if (player.redeemableCredits >= this.profitStrategy.bigGiftThreshold) {
      player.playerType = 'big_gift';
    } else if (player.redeemableCredits >= this.profitStrategy.smallGiftThreshold) {
      player.playerType = 'small_gift';
    } else {
      player.playerType = 'free';
    }

    // Calculează șansele de câștig
    let winChance = this.profitStrategy.freeUserWinChance;

    if (player.playerType === 'big_gift') {
      winChance = this.profitStrategy.bigGiftWinChance;
    } else if (player.playerType === 'small_gift') {
      winChance = this.profitStrategy.smallGiftWinChance;
    }

    // Bonus ocazional pentru a-i captiva
    const timeSinceLastBonus = Date.now() - player.lastBonusTime;
    const shouldGiveBonus = Math.random() < this.profitStrategy.bonusFrequency && timeSinceLastBonus > 300000; // 5 minute

    if (shouldGiveBonus) {
      winChance = this.profitStrategy.bonusWinChance;
      player.lastBonusTime = Date.now();
      this.addChatMessage('🍀 BONUS', `${username} are noroc special astăzi!`);
    }

    return winChance;
  }

  // TikTok Live Integration
  async connectToTikTokLive() {
    const username = document.getElementById('tiktok-username').value.trim();

    if (!username) {
      alert('Te rog introdu username-ul tău TikTok!');
      return;
    }

    const validation = TikTokUtils.validateUsername(username);
    if (!validation.valid) {
      alert(validation.error);
      return;
    }

    this.addChatMessage('🔄 CONNECTING', `Încerc conectarea REALĂ la @${validation.username}...`);

    try {
      const connected = await this.tiktokConnector.connectToUser(validation.username);

      if (connected) {
        this.isReallyConnected = true;
        document.getElementById('connect-tiktok').style.display = 'none';
        document.getElementById('disconnect-tiktok').style.display = 'block';
        this.addChatMessage('✅ SUCCESS', `🚀 Casino conectat REAL la @${validation.username}!`);
      }

    } catch (error) {
      console.error('Eroare conectare:', error);
      this.addChatMessage('❌ ERROR', `Eroare conectare: ${error.message}`);
    }
  }

  disconnectFromTikTokLive() {
    if (this.tiktokConnector && this.isReallyConnected) {
      this.tiktokConnector.disconnect();
      this.isReallyConnected = false;
      document.getElementById('connect-tiktok').style.display = 'block';
      document.getElementById('disconnect-tiktok').style.display = 'none';
    }
  }

  // Process real chat commands from TikTok Live
  processChatCommand(username, message) {
    if (!message) return;

    const msg = message.toLowerCase().trim();

    // COMENZI PENTRU CREDITE GRATUITE
    if (msg.includes('!tap') || msg.includes('!like')) {
      this.handleTapAction(username);
      return;
    } else if (msg.includes('!share') || msg.includes('!distribuie')) {
      this.handleShareAction(username);
      return;
    } else if (msg.includes('!credite') || msg.includes('!balance')) {
      const player = this.getOrCreatePlayer(username);
      this.addChatMessage('💰 BALANCE',
        `${username}: ${player.credits} credite totale | ` +
        `${player.redeemableCredits} retrăgabile | ` +
        `${player.freeCredits} gratuite`
      );
      return;
    } else if (msg.includes('!retrage') || msg.includes('!withdraw')) {
      this.handleWithdrawRequest(username);
      return;
    }

    // Comenzi pentru jocuri
    if (msg.includes('!roulette') || msg.includes('!ruletă')) {
      this.switchGame('roulette');
      if (!this.games.roulette.isSpinning) {
        this.addChatMessage('🎯 COMMAND', `${username} a cerut să învârt ruleta!`);
        setTimeout(() => this.spinRoulette(username), 1000);
      }
    } else if (msg.includes('!blackjack') || msg.includes('!cards')) {
      this.switchGame('blackjack');
      if (!this.games.blackjack.isPlaying) {
        this.addChatMessage('🃏 COMMAND', `${username} vrea să joace blackjack!`);
        setTimeout(() => this.dealBlackjack(username), 1000);
      }
    } else if (msg.includes('!slots') || msg.includes('!păcănele')) {
      this.switchGame('slots');
      if (!this.games.slots.isSpinning) {
        this.addChatMessage('🎰 COMMAND', `${username} joacă la păcănele!`);
        setTimeout(() => this.spinSlots(username), 1000);
      }
    } else if (msg.includes('!dice') || msg.includes('!zaruri')) {
      this.switchGame('dice');
      if (!this.games.dice.isRolling) {
        this.addChatMessage('🎲 COMMAND', `${username} aruncă zarurile!`);
        setTimeout(() => this.rollDice(username), 1000);
      }
    } else if (msg.includes('!help') || msg.includes('!comenzi')) {
      this.showHelpCommands(username);
    }
  }

  // Gestionează cererile de retragere
  handleWithdrawRequest(username) {
    const player = this.getOrCreatePlayer(username);

    // Verifică dacă jucătorul are credite retrăgabile
    if (player.redeemableCredits <= 0) {
      this.addChatMessage('⚠️ ERROR', `${username}, nu ai credite retrăgabile! Doar creditele din cadouri pot fi retrase.`);
      return false;
    }

    // Verifică dacă jucătorul a dat cadouri
    if (!player.hasGifted) {
      this.addChatMessage('⚠️ ERROR', `${username}, doar utilizatorii care au trimis cadouri pot retrage credite!`);
      return false;
    }

    // Calculează suma de retras
    const withdrawAmount = player.redeemableCredits;

    // Notifică jucătorul
    this.addChatMessage('💸 WITHDRAW', `${username} a retras ${withdrawAmount} credite! Contactează streamer-ul pentru detalii.`);

    // Resetează creditele retrăgabile
    player.redeemableCredits = 0;

    return true;
  }

  // Afișează comenzile disponibile
  showHelpCommands(username) {
    this.addChatMessage('ℹ️ HELP', `${username}, iată comenzile disponibile:`);
    this.addChatMessage('🎮 GAMES', `!roulette, !blackjack, !slots, !dice - Joacă jocuri`);
    this.addChatMessage('💰 CREDITS', `!tap, !share - Primești credite gratuite`);
    this.addChatMessage('💸 WITHDRAW', `!withdraw - Retrage creditele din cadouri`);
    this.addChatMessage('📊 INFO', `!balance, !help - Informații`);
  }

  // Update viewer count from real TikTok Live
  updateViewerCount(realCount) {
    if (realCount && realCount > 0) {
      this.viewerCount = realCount;
      document.getElementById('viewer-count').textContent = `${realCount} players`;
    }
  }

  // Game switching
  switchGame(gameType) {
    // Hide all game sections
    document.querySelectorAll('.game-section').forEach(section => {
      section.classList.remove('active');
    });

    // Remove active class from all tabs
    document.querySelectorAll('.game-tab').forEach(tab => {
      tab.classList.remove('active');
    });

    // Show selected game section
    document.getElementById(`${gameType}-section`).classList.add('active');

    // Add active class to selected tab
    document.querySelector(`[data-game="${gameType}"]`).classList.add('active');
  }

  // Chat system
  addChatMessage(username, message) {
    console.log(`💬 ${username}: ${message}`);

    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) {
      console.warn('⚠️ Element chat-messages nu a fost găsit!');
      return;
    }

    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message';

    const timestamp = new Date().toLocaleTimeString();
    messageElement.innerHTML = `
      <span class="chat-username">${username}</span>
      <span class="chat-time">[${timestamp}]</span>
      <div class="chat-text">${message}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Keep only last 50 messages
    while (chatMessages.children.length > 50) {
      chatMessages.removeChild(chatMessages.firstChild);
    }
  }

  // Update display
  updateDisplay() {
    console.log(`💰 Actualizez afișajul cu ${this.credits} credite`);

    const creditsDisplay = document.getElementById('credits-display');
    if (creditsDisplay) {
      creditsDisplay.textContent = `${this.credits} credite`;
    }

    const creditsBalance = document.getElementById('credits-balance');
    if (creditsBalance) {
      creditsBalance.textContent = `${this.credits} credite`;
    }

    const viewerCount = document.getElementById('viewer-count');
    if (viewerCount) {
      viewerCount.textContent = `${this.viewerCount} players`;
    }
  }

  // JOCURI DE CASINO

  // Roulette game
  placeBet(type, value) {
    const betAmountEl = document.getElementById('bet-amount');
    const betAmount = betAmountEl ? parseInt(betAmountEl.value) || 10 : 10;

    if (betAmount > this.credits) {
      this.addChatMessage('🎰 CASINO', 'Nu ai suficiente credite pentru acest pariu!');
      return;
    }

    const betKey = `${type}-${value}`;
    const currentBet = this.games.roulette.currentBets.get(betKey) || 0;
    this.games.roulette.currentBets.set(betKey, currentBet + betAmount);
    this.credits -= betAmount;

    console.log(`🎲 Pariu plasat: ${betAmount} credite pe ${value}`);
    this.updateDisplay();
    this.addChatMessage('🎰 CASINO', `Pariu plasat: ${betAmount} credite pe ${value}`);
  }

  spinRoulette(username = 'Casino') {
    if (this.games.roulette.isSpinning) {
      console.log('⚠️ Ruleta se învârte deja!');
      return;
    }

    console.log(`🎯 ${username} învârte ruleta!`);

    // Auto-place bet if no bets
    if (this.games.roulette.currentBets.size === 0) {
      console.log('🎲 Plasez pariu automat pe roșu...');
      this.placeBet('outside', 'red');
    }

    this.games.roulette.isSpinning = true;
    const spinBtn = document.getElementById('spin-roulette');
    if (spinBtn) {
      spinBtn.disabled = true;
      spinBtn.textContent = '🎯 SE ÎNVÂRTE...';
    }

    // Animate ball
    const ball = document.getElementById('roulette-ball');
    if (ball) {
      ball.style.animation = 'spin 4s ease-out';
    }

    this.addChatMessage('🎯 ROULETTE', `${username} a învârtit ruleta! Să vedem ce iese...`);

    // Generate winning number
    setTimeout(() => {
      const numbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];
      const winningNumber = numbers[Math.floor(Math.random() * numbers.length)];

      console.log(`🎯 Numărul câștigător: ${winningNumber}`);

      this.games.roulette.lastNumber = winningNumber;
      const lastNumberEl = document.getElementById('last-number');
      if (lastNumberEl) {
        lastNumberEl.textContent = `Ultimul număr: ${winningNumber}`;
      }

      this.addChatMessage('🎯 REZULTAT', `Bila s-a oprit pe ${winningNumber}!`);

      this.processRouletteWin(winningNumber);
      this.games.roulette.isSpinning = false;

      if (spinBtn) {
        spinBtn.disabled = false;
        spinBtn.textContent = '🎯 ÎNVÂRTE RULETA!';
      }

      if (ball) {
        ball.style.animation = '';
      }
    }, 4000);
  }

  processRouletteWin(winningNumber) {
    let totalWin = 0;

    for (const [betKey, betAmount] of this.games.roulette.currentBets) {
      const [type, value] = betKey.split('-');
      let isWin = false;
      let multiplier = 1;

      if (type === 'outside') {
        if (value === 'red' && this.isRedNumber(winningNumber)) {
          isWin = true;
          multiplier = 2;
        } else if (value === 'black' && !this.isRedNumber(winningNumber) && winningNumber !== 0) {
          isWin = true;
          multiplier = 2;
        } else if (value === 'even' && winningNumber % 2 === 0 && winningNumber !== 0) {
          isWin = true;
          multiplier = 2;
        } else if (value === 'odd' && winningNumber % 2 === 1) {
          isWin = true;
          multiplier = 2;
        }
      }

      if (isWin) {
        const winAmount = betAmount * multiplier;
        totalWin += winAmount;
        this.addChatMessage('🎉 CÂȘTIG', `Ai câștigat ${winAmount} credite!`);
      }
    }

    this.credits += totalWin;
    this.totalWinnings += totalWin;
    this.games.roulette.currentBets.clear();
    this.updateDisplay();
  }

  isRedNumber(number) {
    const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
    return redNumbers.includes(number);
  }

  clearBets() {
    this.games.roulette.currentBets.clear();
    this.addChatMessage('🎰 CASINO', 'Pariurile au fost șterse.');
  }

  maxBet() {
    document.getElementById('bet-amount').value = Math.min(this.credits, 1000);
  }

  withdrawCredits() {
    if (this.credits > 0) {
      this.addChatMessage('💸 RETRAGERE', `Ai retras ${this.credits} credite! Contactează streamer-ul pentru detalii.`);
      this.credits = 0;
      this.updateDisplay();
    } else {
      this.addChatMessage('⚠️ ERROR', 'Nu ai credite de retras!');
    }
  }

  // Placeholder methods for other games
  dealBlackjack(username = 'Casino') {
    this.addChatMessage('🃏 BLACKJACK', `${username} a pornit o mână de blackjack!`);
  }

  hitBlackjack() {
    this.addChatMessage('🃏 BLACKJACK', 'Ai cerut o carte!');
  }

  standBlackjack() {
    this.addChatMessage('🃏 BLACKJACK', 'Te-ai oprit!');
  }

  spinSlots(username = 'Casino') {
    this.addChatMessage('🎰 SLOTS', `${username} joacă la păcănele!`);
  }

  rollDice(username = 'Casino') {
    this.addChatMessage('🎲 DICE', `${username} aruncă zarurile!`);
  }
}

// Initialize the casino
const casino = new TikTokCasino();

// Make casino globally available for HTML onclick handlers
window.casino = casino;

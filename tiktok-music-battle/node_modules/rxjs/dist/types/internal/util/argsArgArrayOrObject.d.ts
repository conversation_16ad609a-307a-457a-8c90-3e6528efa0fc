/**
 * Used in functions where either a list of arguments, a single array of arguments, or a
 * dictionary of arguments can be returned. Returns an object with an `args` property with
 * the arguments in an array, if it is a dictionary, it will also return the `keys` in another
 * property.
 */
export declare function argsArgArrayOrObject<T, O extends Record<string, T>>(args: T[] | [O] | [T[]]): {
    args: T[];
    keys: string[] | null;
};
//# sourceMappingURL=argsArgArrayOrObject.d.ts.map